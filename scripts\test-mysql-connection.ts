import dotenv from 'dotenv';
import { MySQLManager, MySQLConfig } from '../src/database/MySQLManager.js';

// 加载环境变量
dotenv.config();

async function testConnection() {
  try {
    console.log('开始测试MySQL连接...');
    
    const config: MySQLConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'workchat_admin'
    };
    
    console.log('数据库配置:', {
      host: config.host,
      port: config.port,
      user: config.user,
      password: '***',
      database: config.database
    });
    
    console.log('创建MySQLManager实例...');
    const mysqlManager = new MySQLManager(config);
    
    console.log('初始化数据库连接池...');
    await mysqlManager.initialize();
    
    console.log('测试数据库连接...');
    await mysqlManager.testConnection();
    console.log('数据库连接成功!');
    
    console.log('检查表是否存在...');
    const categoriesExists = await mysqlManager.tableExists('option_categories');
    const itemsExists = await mysqlManager.tableExists('option_items');
    
    console.log('option_categories表存在:', categoriesExists);
    console.log('option_items表存在:', itemsExists);
    
    if (categoriesExists && itemsExists) {
      console.log('检查现有数据...');
      const categoriesCount = await mysqlManager.query('SELECT COUNT(*) as count FROM option_categories');
      const itemsCount = await mysqlManager.query('SELECT COUNT(*) as count FROM option_items');
      
      console.log('现有分类数量:', (categoriesCount as unknown as any[])[0]?.count || 0);
      console.log('现有选项数量:', (itemsCount as unknown as any[])[0]?.count || 0);
    }
    
    await mysqlManager.close();
    console.log('测试完成');
    
  } catch (error) {
    console.error('测试失败:', error);
    process.exit(1);
  }
}

testConnection();