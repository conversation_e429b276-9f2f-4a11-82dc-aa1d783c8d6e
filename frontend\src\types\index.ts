// 用户
export interface User {
  id: number
  name?: string
  username?: string
  realName?: string
  avatar?: string
  role?: string
  roleId?: number
  roleName?: string
  department?: string
  departmentId?: number
  departmentName?: string
  phone?: string
  permissions?: string[]
  status?: string
  remark?: string
  lastLoginAt?: string
  createdAt: string
  updatedAt: string
}

// 客户相关类型
export interface Customer {
  id: number
  name: string
  mobile: string
  company?: string
  position?: string
  source: string
  status: string
  owner_id: number
  owner_name?: string
  assignedTo?: string
  tags?: CustomerTag[]
  follow_count: number
  last_follow_time?: string
  created_at: string
  updated_at: string
  // 表单相关字段
  gender?: string
  level?: string
  region?: string[]
  address?: string
  remark?: string
  // 公海相关字段
  pooledAt?: string
}

export interface CustomerTag {
  id: number
  name: string
  color?: string
}

// 跟进记录类型
export interface FollowRecord {
  id: number
  customer_id: number
  customer_name: string
  customerId?: number
  customerName?: string
  type: string
  follow_time: string
  followTime?: string
  content: string
  next_follow_time?: string
  nextFollowTime?: string
  follow_by?: string
  followBy?: string
  status: string
  created_by: number
  created_by_name: string
  remark?: string
  created_at: string
  updated_at: string
  createdAt?: string
  updatedAt?: string
  // 新增三阶段跟进记录字段
  stage: 'follow' | 'visit' | 'deal' // 主要阶段：跟进、到店、成交
  subStage?: string // 子阶段
  designer?: string // 设计师（到店阶段使用）
  designerId?: number // 设计师ID
  amount?: number // 金额（成交阶段使用）
  contractNo?: string // 合同编号（成交阶段使用）
  paymentStatus?: string // 付款状态（成交阶段使用）
  visitDate?: string // 到店日期（到店阶段使用）
  measureDate?: string // 量房日期（到店阶段使用）
  dealDate?: string // 成交日期（成交阶段使用）
}

// 跟进阶段子类型
export type FollowSubStage = 'initial' | 'follow_up' | 'interested' | 'considering'

// 到店阶段子类型
export type VisitSubStage = 'measure' | 'visit' | 'home_visit'

// 成交阶段子类型
export type DealSubStage = 'small_deposit' | 'large_deposit' | 'presale'

// 会议记录类型
export interface MeetingRecord {
  id: number
  title: string
  type?: string
  customer_id: number
  customer_name: string
  customerId?: number
  customerName?: string
  customerIds?: number[]
  customerNames?: string[]
  meeting_time: number | null
  meetingTime?: number | null
  startTime?: string
  endTime?: string
  duration: number
  location: string
  participants: string[]
  attendeeIds?: number[]
  attendeeNames?: string[]
  content: string
  agenda?: string
  notes?: string
  attachments?: string[]
  next_action?: string
  remark?: string
  status: string
  created_by: number
  created_by_name: string
  created_at: string
  updated_at: string
}

// 成交记录类型
export interface DealRecord {
  id: number
  customer_id: number
  customer_name: string
  customerId?: number
  customerName?: string
  packageType: string
  contractAmount: number
  paidAmount: number
  remainingAmount: number
  paymentMethod: string
  contractTime: number | null
  projectDuration: number
  designer: string
  designerId?: number
  sales: string
  salesId?: number
  contractNo: string
  status: string
  remark?: string
  attachments?: string[]
  created_by: number
  created_by_name: string
  created_at: string
  updated_at: string
}

// 营销活动类型
export interface MarketingCampaign {
  id: number
  name: string
  type: string
  description: string
  start_date: string
  end_date: string
  budget: number
  target_audience: string
  channels: string[]
  status: string
  metrics: {
    reach: number
    clicks: number
    conversions: number
    cost_per_click: number
    roi: number
  }
  created_by: number
  created_by_name: string
  created_at: string
  updated_at: string
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data: T
  code?: number
}

export interface PaginationResponse<T = any> {
  data: T[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

// 分页参数类型
export interface PaginationParams {
  page: number
  pageSize: number
}

// 登录相关类型
export interface LoginRequest {
  username: string
  password: string
  captcha?: string
  captchaKey?: string
  remember?: boolean
}

export interface LoginResponse {
  token: string
  user: User
  expires_in: number
}

// 菜单类型
export interface MenuItem {
  key: string
  label: string
  icon?: string
  path?: string
  children?: MenuItem[]
  permissions?: string[]
}

// 面包屑类型
export interface BreadcrumbItem {
  label: string
  path?: string
}

// 表格列类型
export interface TableColumn {
  key: string
  title: string
  width?: number
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  filterable?: boolean
  render?: (row: any, index: number) => any
}

// 表格分页类型
export interface TablePagination {
  page: number
  pageSize: number
  total: number
  showSizePicker?: boolean
  pageSizes?: number[]
}

// 统计数据类型
export interface StatItem {
  key: string
  label: string
  value: string | number
  change?: string
  trend?: 'up' | 'down'
  color?: string
  icon?: any
}

// 图表数据类型
export interface ChartData {
  name: string
  value: number
  [key: string]: any
}

// 文件上传类型
export interface UploadFile {
  id: string
  name: string
  size: number
  type: string
  url?: string
  status: 'pending' | 'uploading' | 'success' | 'error'
  progress?: number
}

// 通知类型
export interface Notification {
  id: string
  title: string
  content: string
  type: 'info' | 'success' | 'warning' | 'error' | 'system'
  read: boolean
  created_at: string
}

// 待办事项类型
export interface TodoItem {
  id: string
  title: string
  type: string
  priority: 'high' | 'medium' | 'low'
  status: 'pending' | 'completed'
  customerId?: number
  customerName?: string
  dueTime: string
  description?: string
  createdAt?: string
  updatedAt?: string
  completedAt?: string
}

// 活动记录类型
export interface ActivityItem {
  id: string
  user: {
    name: string
    avatar?: string
  }
  action: string
  target: string
  type: string
  createdAt: Date
}

// 表单验证规则类型
export interface FormRule {
  required?: boolean
  message?: string
  trigger?: string | string[]
  validator?: (rule: any, value: any) => boolean | Promise<boolean>
  min?: number
  max?: number
  pattern?: RegExp
}

// 路由元信息类型
export interface RouteMeta {
  title?: string
  icon?: string
  requiresAuth?: boolean
  permissions?: string[]
  roles?: string[]
  hidden?: boolean
  keepAlive?: boolean
}



// 销售记录
export interface SalesRecord {
  id: number
  orderNo: string
  customerName: string
  productName: string
  amount: number
  quantity: number
  salesPerson: string
  channel: string
  status: string
  orderDate: string
}

// 客户分析
export interface CustomerAnalytics {
  id: number
  name: string
  phone: string
  level: string
  source: string
  totalSpent: number
  orderCount: number
  lastOrderDate?: string
  activityScore?: number
  registeredAt: string
}

// 活动记录类型
export interface ActivityRecord {
  id: number
  type: string
  title: string
  description: string
  customer_id?: number
  customer_name?: string
  user_id: number
  user_name: string
  created_at: string
}

// 微信相关接口
export interface CreateBrowsingTrackRequest {
  customer_id: number
  page_url: string
  page_title?: string
  visit_duration?: number
  referrer?: string
}

export interface CreateShareRecordRequest {
  customer_id: number
  content_type: string
  content_id: number
  share_platform: string
  share_content?: string
}

export interface CreateWechatGroupRequest {
  name: string
  description?: string
  group_type: string
  max_members?: number
}

export interface UpdateWechatGroupRequest {
  name?: string
  description?: string
  group_type?: string
  max_members?: number
  status?: string
}

export interface CreateWechatMessageRequest {
  group_id?: number
  customer_id?: number
  message_type: string
  content: string
  media_url?: string
}

export interface GetShareRecordsParams {
  page?: number
  pageSize?: number
  customer_id?: number
  content_type?: string
  share_platform?: string
  start_date?: string
  end_date?: string
}

export interface GetWechatGroupsParams {
  page?: number
  pageSize?: number
  search?: string
  group_type?: string
  status?: string
}

export interface GetWechatMessagesParams {
  page?: number
  pageSize?: number
  group_id?: number
  customer_id?: number
  message_type?: string
  start_date?: string
  end_date?: string
}

export interface CreateWechatCustomerRequest {
  wechat_id: string
  nickname?: string
  avatar_url?: string
  phone?: string
  email?: string
  tags?: string[]
}

export interface UpdateWechatCustomerRequest {
  wechat_id?: string
  nickname?: string
  avatar_url?: string
  phone?: string
  email?: string
  tags?: string[]
  status?: string
}

// 微信客户相关接口
export interface WechatCustomer {
  id: number
  wechat_id: string
  openid?: string
  nickname?: string
  avatar_url?: string
  phone?: string
  email?: string
  city?: string
  tags?: string[]
  remark?: string
  status: string
  created_at: string
  updated_at: string
}

export interface BrowsingAction {
  id: number
  track_id: number
  action_type: 'click' | 'scroll' | 'form_submit' | 'download' | 'share'
  element_selector?: string
  element_text?: string
  action_data?: Record<string, any>
  timestamp: string
}

export interface BrowsingTrack {
  id: number
  customer_id: number
  openid: string
  page_url: string
  page_title: string
  referrer?: string
  user_agent: string
  ip_address: string
  location?: string
  device_type: 'mobile' | 'desktop' | 'tablet'
  browser: string
  os: string
  visit_duration: number
  duration: number
  page_type?: string
  actions: BrowsingAction[]
  created_at: string
}

export interface ShareRecord {
  id: number
  customer_id: number
  content_type: string
  content_id: number
  share_platform: string
  share_content?: string
  created_at: string
}

export interface WechatGroup {
  id: number
  name: string
  description?: string
  group_type: string
  max_members?: number
  current_members: number
  status: string
  created_at: string
  updated_at: string
  // 扩展属性
  type: string
  memberCount: number
  todayMessageCount?: number
  monthMessageCount?: number
  weekMessageCount?: number
  activeMembers?: number
  createTime: string
  lastActiveTime?: string
  group_name: string
  ownerName?: string
  groupId: number
}

export interface WechatMessage {
  id: number
  group_id?: number
  customer_id?: number
  message_type: string
  content: string
  media_url?: string
  media_size?: number
  created_at: string
  // 扩展属性
  type?: string
  senderId?: string
  senderName?: string
  groupName?: string
  sendTime?: string
  isKeywordHit?: boolean
  sentiment?: string
  is_read?: boolean
  is_replied?: boolean
  reply_content?: string
  reply_time?: string
  data?: any
}

export interface WechatStatistics {
  total_customers: number
  active_customers: number
  total_groups: number
  total_messages: number
  browsing_tracks_count: number
  share_records_count: number
}

export interface GetWechatCustomersParams {
  page?: number
  pageSize?: number
  search?: string
  status?: string
  tags?: string[]
}

export interface GetBrowsingTracksParams {
  page?: number
  pageSize?: number
  customer_id?: number
  page_url?: string
  start_date?: string
  end_date?: string
}

// 导出选项数据相关类型
export * from './options'