import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { nextTick } from 'vue'
import CustomerOptionManager from '../CustomerOptionManager.vue'
import { useCustomerOptionsStore } from '@/stores/customerOptions'
import { CUSTOMER_OPTION_CATEGORIES } from '@/constants/customerOptions'

// Mock Naive UI components
vi.mock('naive-ui', () => ({
  NCard: { name: 'NCard', template: '<div><slot /></div>' },
  NSpace: { name: 'NSpace', template: '<div><slot /></div>' },
  NButton: { name: 'NButton', template: '<button><slot /></button>' },
  NInput: { name: 'NInput', template: '<input />' },
  NSelect: { name: 'NSelect', template: '<select><slot /></select>' },
  NDataTable: { name: 'NDataTable', template: '<table><slot /></table>' },
  NModal: { name: 'NModal', template: '<div v-if="show"><slot /></div>', props: ['show'] },
  NForm: { name: 'NForm', template: '<form><slot /></form>' },
  NFormItem: { name: 'NFormItem', template: '<div><slot /></div>' },
  NIcon: { name: 'NIcon', template: '<i><slot /></i>' },
  NPagination: { name: 'NPagination', template: '<div></div>' },
  NPopconfirm: { name: 'NPopconfirm', template: '<div><slot /></div>' },
  NTag: { name: 'NTag', template: '<span><slot /></span>' },
  NTooltip: { name: 'NTooltip', template: '<div><slot /></div>' },
  NColorPicker: { name: 'NColorPicker', template: '<input type="color" />' },
  NInputNumber: { name: 'NInputNumber', template: '<input type="number" />' },
  NSwitch: { name: 'NSwitch', template: '<input type="checkbox" />' },
  createDiscreteApi: () => ({
    message: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn(),
      info: vi.fn()
    },
    dialog: {
      warning: vi.fn()
    }
  })
}))

// Mock icons
vi.mock('@vicons/ionicons5', () => ({
  SearchOutline: { name: 'SearchOutline' },
  AddOutline: { name: 'AddOutline' },
  RefreshOutline: { name: 'RefreshOutline' },
  CreateOutline: { name: 'CreateOutline' },
  TrashOutline: { name: 'TrashOutline' },
  CheckmarkOutline: { name: 'CheckmarkOutline' },
  CloseOutline: { name: 'CloseOutline' }
}))

// Mock API
vi.mock('@/api/customerOptions', () => ({
  getOptionCategories: vi.fn(),
  getOptionsByCategory: vi.fn(),
  createOptionItem: vi.fn(),
  updateOptionItem: vi.fn(),
  deleteOptionItem: vi.fn(),
  batchDeleteOptionItems: vi.fn(),
  batchUpdateOptionItemStatus: vi.fn()
}))

// Mock composables
vi.mock('@/composables/useCustomerOptions', () => ({
  useCustomerOptions: () => ({
    // 响应式数据
    optionList: { value: [] },
    loading: { value: false },
    submitting: { value: false },
    searchKeyword: { value: '' },
    statusFilter: { value: '' },
    pagination: {
      value: {
        page: 1,
        pageSize: 10,
        itemCount: 0,
        showSizePicker: true,
        pageSizes: [10, 20, 50, 100]
      }
    },
    selectedRowKeys: { value: [] },
    
    // 计算属性
    categoryInfo: { value: null },
    selectedItems: { value: [] },
    hasNoData: { value: true },
    cacheKey: { value: 'test-cache' },
    
    // 方法
    loadData: vi.fn(),
    createItem: vi.fn(),
    updateItem: vi.fn(),
    deleteItem: vi.fn(),
    batchDelete: vi.fn(),
    batchUpdateStatus: vi.fn(),
    handleSearch: vi.fn(),
    handleFilter: vi.fn(),
    resetFilters: vi.fn(),
    clearCache: vi.fn()
  })
}))

describe('CustomerOptionManager', () => {
  let wrapper: VueWrapper<any>
  let pinia: any
  let store: any

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    store = useCustomerOptionsStore()
    
    // Mock store methods
    store.loadCategories = vi.fn()
    store.loadCategoryOptions = vi.fn()
    store.createOption = vi.fn()
    store.updateOption = vi.fn()
    store.deleteOption = vi.fn()
    store.batchDeleteOptions = vi.fn()
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
    vi.clearAllMocks()
  })

  const createWrapper = (props = {}) => {
    return mount(CustomerOptionManager, {
      props: {
        categoryCode: 'customer_source',
        categoryName: '客户来源',
        ...props
      },
      global: {
        plugins: [pinia]
      }
    })
  }

  describe('组件渲染', () => {
    it('应该正确渲染组件', () => {
      wrapper = createWrapper()
      expect(wrapper.exists()).toBe(true)
      expect(wrapper.find('.customer-option-manager').exists()).toBe(true)
    })

    it('应该显示正确的分类标题', () => {
      wrapper = createWrapper({ categoryCode: 'customer_source' })
      const categoryInfo = CUSTOMER_OPTION_CATEGORIES['customer_source']
      expect(wrapper.text()).toContain(categoryInfo.name)
    })

    it('应该渲染搜索框', () => {
      wrapper = createWrapper()
      expect(wrapper.find('input[placeholder*="搜索"]').exists()).toBe(true)
    })

    it('应该渲染操作按钮', () => {
      wrapper = createWrapper()
      expect(wrapper.find('button').exists()).toBe(true)
    })
  })

  describe('Props 验证', () => {
    it('应该接受有效的 categoryCode', () => {
      wrapper = createWrapper({ categoryCode: 'customer_level' })
      expect(wrapper.props('categoryCode')).toBe('customer_level')
    })

    it('应该有默认的 categoryCode', () => {
      wrapper = createWrapper()
      expect(wrapper.props('categoryCode')).toBe('customer_source')
    })
  })

  describe('数据加载', () => {
    it('应该在组件挂载时加载数据', async () => {
      const mockLoadData = vi.fn()
      vi.mocked(require('@/composables/useCustomerOptions').useCustomerOptions).mockReturnValue({
        loadData: mockLoadData,
        optionList: { value: [] },
        loading: { value: false },
        submitting: { value: false },
        searchKeyword: { value: '' },
        statusFilter: { value: '' },
        pagination: { value: { page: 1, pageSize: 10, itemCount: 0 } },
        selectedRowKeys: { value: [] },
        categoryInfo: { value: null },
        selectedItems: { value: [] },
        hasNoData: { value: true },
        cacheKey: { value: 'test' },
        createItem: vi.fn(),
        updateItem: vi.fn(),
        deleteItem: vi.fn(),
        batchDelete: vi.fn(),
        batchUpdateStatus: vi.fn(),
        handleSearch: vi.fn(),
        handleFilter: vi.fn(),
        resetFilters: vi.fn(),
        clearCache: vi.fn()
      })

      wrapper = createWrapper()
      await nextTick()
      
      expect(mockLoadData).toHaveBeenCalled()
    })

    it('应该在 categoryCode 变化时重新加载数据', async () => {
      const mockLoadData = vi.fn()
      vi.mocked(require('@/composables/useCustomerOptions').useCustomerOptions).mockReturnValue({
        loadData: mockLoadData,
        optionList: { value: [] },
        loading: { value: false },
        submitting: { value: false },
        searchKeyword: { value: '' },
        statusFilter: { value: '' },
        pagination: { value: { page: 1, pageSize: 10, itemCount: 0 } },
        selectedRowKeys: { value: [] },
        categoryInfo: { value: null },
        selectedItems: { value: [] },
        hasNoData: { value: true },
        cacheKey: { value: 'test' },
        createItem: vi.fn(),
        updateItem: vi.fn(),
        deleteItem: vi.fn(),
        batchDelete: vi.fn(),
        batchUpdateStatus: vi.fn(),
        handleSearch: vi.fn(),
        handleFilter: vi.fn(),
        resetFilters: vi.fn(),
        clearCache: vi.fn()
      })

      wrapper = createWrapper({ categoryCode: 'customer_source' })
      await nextTick()
      
      await wrapper.setProps({ categoryCode: 'customer_level' })
      await nextTick()
      
      expect(mockLoadData).toHaveBeenCalledTimes(2)
    })
  })

  describe('搜索功能', () => {
    it('应该能够处理搜索输入', async () => {
      const mockHandleSearch = vi.fn()
      vi.mocked(require('@/composables/useCustomerOptions').useCustomerOptions).mockReturnValue({
        handleSearch: mockHandleSearch,
        searchKeyword: { value: '' },
        optionList: { value: [] },
        loading: { value: false },
        submitting: { value: false },
        statusFilter: { value: '' },
        pagination: { value: { page: 1, pageSize: 10, itemCount: 0 } },
        selectedRowKeys: { value: [] },
        categoryInfo: { value: null },
        selectedItems: { value: [] },
        hasNoData: { value: true },
        cacheKey: { value: 'test' },
        loadData: vi.fn(),
        createItem: vi.fn(),
        updateItem: vi.fn(),
        deleteItem: vi.fn(),
        batchDelete: vi.fn(),
        batchUpdateStatus: vi.fn(),
        handleFilter: vi.fn(),
        resetFilters: vi.fn(),
        clearCache: vi.fn()
      })

      wrapper = createWrapper()
      const searchInput = wrapper.find('input[placeholder*="搜索"]')
      
      await searchInput.setValue('测试搜索')
      await searchInput.trigger('input')
      
      // 由于防抖，需要等待一段时间
      await new Promise(resolve => setTimeout(resolve, 300))
      
      expect(mockHandleSearch).toHaveBeenCalledWith('测试搜索')
    })
  })

  describe('CRUD 操作', () => {
    it('应该能够创建新项目', async () => {
      const mockCreateItem = vi.fn().mockResolvedValue({ success: true })
      vi.mocked(require('@/composables/useCustomerOptions').useCustomerOptions).mockReturnValue({
        createItem: mockCreateItem,
        optionList: { value: [] },
        loading: { value: false },
        submitting: { value: false },
        searchKeyword: { value: '' },
        statusFilter: { value: '' },
        pagination: { value: { page: 1, pageSize: 10, itemCount: 0 } },
        selectedRowKeys: { value: [] },
        categoryInfo: { value: null },
        selectedItems: { value: [] },
        hasNoData: { value: true },
        cacheKey: { value: 'test' },
        loadData: vi.fn(),
        updateItem: vi.fn(),
        deleteItem: vi.fn(),
        batchDelete: vi.fn(),
        batchUpdateStatus: vi.fn(),
        handleSearch: vi.fn(),
        handleFilter: vi.fn(),
        resetFilters: vi.fn(),
        clearCache: vi.fn()
      })

      wrapper = createWrapper()
      
      // 模拟创建操作
      const createData = {
        code: 'test_code',
        value: 'test_value',
        label: '测试标签',
        description: '测试描述'
      }
      
      await wrapper.vm.handleCreate(createData)
      
      expect(mockCreateItem).toHaveBeenCalledWith(createData)
    })

    it('应该能够更新项目', async () => {
      const mockUpdateItem = vi.fn().mockResolvedValue({ success: true })
      vi.mocked(require('@/composables/useCustomerOptions').useCustomerOptions).mockReturnValue({
        updateItem: mockUpdateItem,
        optionList: { value: [] },
        loading: { value: false },
        submitting: { value: false },
        searchKeyword: { value: '' },
        statusFilter: { value: '' },
        pagination: { value: { page: 1, pageSize: 10, itemCount: 0 } },
        selectedRowKeys: { value: [] },
        categoryInfo: { value: null },
        selectedItems: { value: [] },
        hasNoData: { value: true },
        cacheKey: { value: 'test' },
        loadData: vi.fn(),
        createItem: vi.fn(),
        deleteItem: vi.fn(),
        batchDelete: vi.fn(),
        batchUpdateStatus: vi.fn(),
        handleSearch: vi.fn(),
        handleFilter: vi.fn(),
        resetFilters: vi.fn(),
        clearCache: vi.fn()
      })

      wrapper = createWrapper()
      
      const updateData = {
        id: 1,
        code: 'updated_code',
        value: 'updated_value',
        label: '更新标签'
      }
      
      await wrapper.vm.handleUpdate(updateData)
      
      expect(mockUpdateItem).toHaveBeenCalledWith(updateData)
    })

    it('应该能够删除项目', async () => {
      const mockDeleteItem = vi.fn().mockResolvedValue({ success: true })
      vi.mocked(require('@/composables/useCustomerOptions').useCustomerOptions).mockReturnValue({
        deleteItem: mockDeleteItem,
        optionList: { value: [] },
        loading: { value: false },
        submitting: { value: false },
        searchKeyword: { value: '' },
        statusFilter: { value: '' },
        pagination: { value: { page: 1, pageSize: 10, itemCount: 0 } },
        selectedRowKeys: { value: [] },
        categoryInfo: { value: null },
        selectedItems: { value: [] },
        hasNoData: { value: true },
        cacheKey: { value: 'test' },
        loadData: vi.fn(),
        createItem: vi.fn(),
        updateItem: vi.fn(),
        batchDelete: vi.fn(),
        batchUpdateStatus: vi.fn(),
        handleSearch: vi.fn(),
        handleFilter: vi.fn(),
        resetFilters: vi.fn(),
        clearCache: vi.fn()
      })

      wrapper = createWrapper()
      
      await wrapper.vm.handleDelete(1)
      
      expect(mockDeleteItem).toHaveBeenCalledWith(1)
    })
  })

  describe('批量操作', () => {
    it('应该能够批量删除项目', async () => {
      const mockBatchDelete = vi.fn().mockResolvedValue({ success: true })
      vi.mocked(require('@/composables/useCustomerOptions').useCustomerOptions).mockReturnValue({
        batchDelete: mockBatchDelete,
        selectedRowKeys: { value: [1, 2, 3] },
        optionList: { value: [] },
        loading: { value: false },
        submitting: { value: false },
        searchKeyword: { value: '' },
        statusFilter: { value: '' },
        pagination: { value: { page: 1, pageSize: 10, itemCount: 0 } },
        categoryInfo: { value: null },
        selectedItems: { value: [] },
        hasNoData: { value: true },
        cacheKey: { value: 'test' },
        loadData: vi.fn(),
        createItem: vi.fn(),
        updateItem: vi.fn(),
        deleteItem: vi.fn(),
        batchUpdateStatus: vi.fn(),
        handleSearch: vi.fn(),
        handleFilter: vi.fn(),
        resetFilters: vi.fn(),
        clearCache: vi.fn()
      })

      wrapper = createWrapper()
      
      await wrapper.vm.handleBatchDelete()
      
      expect(mockBatchDelete).toHaveBeenCalledWith([1, 2, 3])
    })

    it('应该能够批量更新状态', async () => {
      const mockBatchUpdateStatus = vi.fn().mockResolvedValue({ success: true })
      vi.mocked(require('@/composables/useCustomerOptions').useCustomerOptions).mockReturnValue({
        batchUpdateStatus: mockBatchUpdateStatus,
        selectedRowKeys: { value: [1, 2, 3] },
        optionList: { value: [] },
        loading: { value: false },
        submitting: { value: false },
        searchKeyword: { value: '' },
        statusFilter: { value: '' },
        pagination: { value: { page: 1, pageSize: 10, itemCount: 0 } },
        categoryInfo: { value: null },
        selectedItems: { value: [] },
        hasNoData: { value: true },
        cacheKey: { value: 'test' },
        loadData: vi.fn(),
        createItem: vi.fn(),
        updateItem: vi.fn(),
        deleteItem: vi.fn(),
        batchDelete: vi.fn(),
        handleSearch: vi.fn(),
        handleFilter: vi.fn(),
        resetFilters: vi.fn(),
        clearCache: vi.fn()
      })

      wrapper = createWrapper()
      
      await wrapper.vm.handleBatchEnable()
      
      expect(mockBatchUpdateStatus).toHaveBeenCalledWith([1, 2, 3], 'active')
    })
  })

  describe('错误处理', () => {
    it('应该处理 API 错误', async () => {
      const mockCreateItem = vi.fn().mockRejectedValue(new Error('API Error'))
      vi.mocked(require('@/composables/useCustomerOptions').useCustomerOptions).mockReturnValue({
        createItem: mockCreateItem,
        optionList: { value: [] },
        loading: { value: false },
        submitting: { value: false },
        searchKeyword: { value: '' },
        statusFilter: { value: '' },
        pagination: { value: { page: 1, pageSize: 10, itemCount: 0 } },
        selectedRowKeys: { value: [] },
        categoryInfo: { value: null },
        selectedItems: { value: [] },
        hasNoData: { value: true },
        cacheKey: { value: 'test' },
        loadData: vi.fn(),
        updateItem: vi.fn(),
        deleteItem: vi.fn(),
        batchDelete: vi.fn(),
        batchUpdateStatus: vi.fn(),
        handleSearch: vi.fn(),
        handleFilter: vi.fn(),
        resetFilters: vi.fn(),
        clearCache: vi.fn()
      })

      wrapper = createWrapper()
      
      const createData = {
        code: 'test_code',
        value: 'test_value',
        label: '测试标签'
      }
      
      await expect(wrapper.vm.handleCreate(createData)).rejects.toThrow('API Error')
    })
  })

  describe('表单验证', () => {
    it('应该验证必填字段', async () => {
      wrapper = createWrapper()
      
      // 测试空数据提交
      const emptyData = {
        code: '',
        value: '',
        label: ''
      }
      
      const isValid = wrapper.vm.validateForm(emptyData)
      expect(isValid).toBe(false)
    })

    it('应该验证代码格式', async () => {
      wrapper = createWrapper()
      
      // 测试无效代码格式
      const invalidCodeData = {
        code: '123-invalid-code!',
        value: 'test_value',
        label: '测试标签'
      }
      
      const isValid = wrapper.vm.validateForm(invalidCodeData)
      expect(isValid).toBe(false)
    })
  })
})