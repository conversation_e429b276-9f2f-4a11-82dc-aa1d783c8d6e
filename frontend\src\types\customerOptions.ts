// ==================== 基础数据类型 ====================

/**
 * 选项分类接口
 */
export interface OptionCategory {
  /** 分类ID */
  id: string
  /** 分类代码 */
  code: string
  /** 分类名称 */
  name: string
  /** 分类描述 */
  description?: string
  /** 排序值 */
  sortOrder: number
  /** 是否启用 */
  enabled: boolean
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
}

/**
 * 选项数据接口
 */
export interface OptionItem {
  /** 选项ID */
  id: string
  /** 分类代码 */
  categoryCode: string
  /** 选项代码 */
  code: string
  /** 选项名称 */
  name: string
  /** 选项值 */
  value: string
  /** 选项描述 */
  description?: string
  /** 排序值 */
  sortOrder: number
  /** 是否启用 */
  enabled: boolean
  /** 扩展属性 */
  properties?: Record<string, any>
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
}

/**
 * API响应基础接口
 */
export interface ApiResponse<T = any> {
  /** 响应代码 */
  code: number
  /** 响应消息 */
  message: string
  /** 响应数据 */
  data: T
  /** 是否成功 */
  success: boolean
}

/**
 * 分页响应接口
 */
export interface PaginatedResponse<T = any> {
  /** 数据列表 */
  items: T[]
  /** 总记录数 */
  total: number
  /** 当前页码 */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 总页数 */
  totalPages: number
}

// ==================== 请求参数类型 ====================

/**
 * 分页查询参数
 */
export interface PaginationParams {
  /** 页码 */
  page?: number
  /** 每页数量 */
  pageSize?: number
  /** 搜索关键词 */
  keyword?: string
  /** 状态筛选 */
  enabled?: boolean
}

/**
 * 选项分类查询参数
 */
export interface QueryOptionCategoriesParams extends PaginationParams {
  /** 分类代码 */
  code?: string
}

/**
 * 选项数据查询参数
 */
export interface QueryOptionItemsParams extends PaginationParams {
  /** 分类代码 */
  categoryCode: string
  /** 选项代码 */
  code?: string
}

/**
 * 获取选项分类参数（API兼容）
 */
export interface GetOptionCategoriesParams extends PaginationParams {
  /** 分类代码 */
  code?: string
}

/**
 * 获取选项数据参数（API兼容）
 */
export interface GetOptionItemsParams extends PaginationParams {
  /** 分类代码 */
  category_code?: string
  /** 选项代码 */
  code?: string
}

/**
 * 创建选项分类请求
 */
export interface CreateOptionCategoryRequest {
  /** 分类代码 */
  code: string
  /** 分类名称 */
  name: string
  /** 分类描述 */
  description?: string
  /** 排序值 */
  sortOrder?: number
  /** 是否启用 */
  enabled?: boolean
}

/**
 * 更新选项分类请求
 */
export interface UpdateOptionCategoryRequest {
  /** 分类名称 */
  name?: string
  /** 分类描述 */
  description?: string
  /** 排序值 */
  sortOrder?: number
  /** 是否启用 */
  enabled?: boolean
}

/**
 * 创建选项数据请求
 */
export interface CreateOptionItemRequest {
  /** 分类代码 */
  categoryCode?: string
  /** 分类ID（API兼容字段） */
  category_id?: string
  /** 选项代码 */
  code: string
  /** 选项名称 */
  name: string
  /** 选项值 */
  value: string
  /** 选项描述 */
  description?: string
  /** 排序值 */
  sortOrder?: number
  /** 是否启用 */
  enabled?: boolean
  /** 扩展属性 */
  properties?: Record<string, any>
}

/**
 * 更新选项数据请求
 */
export interface UpdateOptionItemRequest {
  /** 分类ID（API兼容字段） */
  category_id?: string
  /** 选项代码 */
  code?: string
  /** 选项名称 */
  label?: string
  /** 选项值 */
  value?: string
  /** 选项描述 */
  description?: string
  /** 排序值 */
  sort_order?: number
  /** 是否激活（API兼容字段） */
  is_active?: boolean
  /** 扩展属性 */
  properties?: Record<string, any>
}

// ==================== 响应类型 ====================

/**
 * 选项分类列表响应
 */
export type OptionCategoriesResponse = ApiResponse<PaginatedResponse<OptionCategory>>

/**
 * 选项数据列表响应
 */
export type OptionItemsResponse = ApiResponse<PaginatedResponse<OptionItem>>

/**
 * 获取选项分类响应（API兼容）
 */
export type GetOptionCategoriesResponse = ApiResponse<PaginatedResponse<OptionCategory>>

/**
 * 获取选项数据响应（API兼容）
 */
export type GetOptionItemsResponse = ApiResponse<PaginatedResponse<OptionItem>>

/**
 * 创建选项分类响应
 */
export type CreateOptionCategoryResponse = ApiResponse<OptionCategory>

/**
 * 创建选项数据响应
 */
export type CreateOptionItemResponse = ApiResponse<OptionItem>

/**
 * 更新选项分类响应
 */
export type UpdateOptionCategoryResponse = ApiResponse<OptionCategory>

/**
 * 更新选项数据响应
 */
export type UpdateOptionItemResponse = ApiResponse<OptionItem>

/**
 * 删除操作响应
 */
export type DeleteResponse = ApiResponse<{ deleted: boolean }>

// ==================== 组件Props类型 ====================

/**
 * 客户参数管理组件Props
 */
export interface CustomerOptionManagerProps {
  /** 参数分类代码 */
  categoryCode: string
  /** 参数分类名称 */
  categoryName: string
  /** 是否只读模式 */
  readonly?: boolean
  /** 自定义表格列配置 */
  customColumns?: any[]
  /** 是否显示批量操作 */
  showBatchActions?: boolean
  /** 是否显示搜索框 */
  showSearch?: boolean
  /** 是否显示筛选器 */
  showFilters?: boolean
  /** 默认每页数量 */
  defaultPageSize?: number
}

/**
 * 客户参数管理组件Emits
 */
export interface CustomerOptionManagerEmits {
  /** 数据变更事件 */
  'data-change': [data: OptionItem[]]
  /** 选择变更事件 */
  'selection-change': [selectedKeys: string[]]
  /** 操作完成事件 */
  'operation-complete': [operation: string, success: boolean, data?: any]
  /** 错误事件 */
  'error': [error: Error]
}

// ==================== 组合函数类型 ====================

/**
 * useCustomerOptions组合函数参数
 */
export interface UseCustomerOptionsParams {
  /** 参数分类代码 */
  categoryCode: string
  /** 是否自动加载数据 */
  autoLoad?: boolean
  /** 分页配置 */
  pagination?: {
    page?: number
    pageSize?: number
  }
  /** 是否启用缓存 */
  enableCache?: boolean
}

/**
 * 分页配置接口
 */
export interface PaginationConfig {
  /** 当前页码 */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 总记录数 */
  itemCount: number
  /** 是否显示每页数量选择器 */
  showSizePicker: boolean
  /** 每页数量选项 */
  pageSizes: number[]
  /** 页码变更回调 */
  onChange: (page: number) => void
  /** 每页数量变更回调 */
  onUpdatePageSize: (pageSize: number) => void
}

/**
 * useCustomerOptions组合函数返回值
 */
export interface UseCustomerOptionsReturn {
  // 响应式数据
  /** 选项数据列表 */
  items: Ref<OptionItem[]>
  /** 加载状态 */
  loading: Ref<boolean>
  /** 提交状态 */
  submitting: Ref<boolean>
  /** 分页配置 */
  pagination: Reactive<PaginationConfig>
  /** 搜索关键词 */
  searchKeyword: Ref<string>
  /** 状态筛选 */
  statusFilter: Ref<string>
  /** 选中的行键 */
  checkedRowKeys: Ref<Array<string | number>>

  // 计算属性
  /** 分类信息 */
  categoryInfo: ComputedRef<any>
  /** 是否有选中项 */
  hasSelection: ComputedRef<boolean>
  /** 选中项数量 */
  selectionCount: ComputedRef<number>
  /** 是否为空 */
  isEmpty: ComputedRef<boolean>

  // 操作方法
  /** 加载数据 */
  loadItems: () => Promise<void>
  /** 创建选项 */
  createItem: (data: CreateOptionItemRequest) => Promise<boolean>
  /** 更新选项 */
  updateItem: (id: string, data: UpdateOptionItemRequest) => Promise<boolean>
  /** 删除选项 */
  deleteItem: (id: string) => Promise<boolean>
  /** 批量删除 */
  batchDelete: (ids: string[]) => Promise<boolean>
  /** 批量更新状态 */
  batchUpdateStatus: (ids: string[], isActive: boolean) => Promise<boolean>
  /** 清除缓存 */
  clearCache: () => void

  // 搜索和筛选
  /** 处理搜索 */
  handleSearch: (keyword: string) => void
  /** 处理筛选 */
  handleFilter: (status: string) => void
  /** 重置筛选 */
  resetFilters: () => void
  /** 刷新数据 */
  refreshData: () => Promise<void>
}

// ==================== 错误类型定义 ====================

/**
 * 客户选项数据类型（用于组件显示）
 */
export interface CustomerOption {
  id?: number
  optionCode: string
  optionValue: string
  displayLabel: string
  description?: string
  categoryCode?: string
  status?: number
  sortOrder?: number
  createdAt?: string
  updatedAt?: string
}

/**
 * 客户参数管理错误类
 */
export class CustomerOptionsError extends Error {
  /** 错误代码 */
  code: ErrorCode
  /** 错误详情 */
  details?: any
  /** HTTP状态码 */
  statusCode?: number

  constructor(message: string, code: ErrorCode, details?: any, statusCode?: number) {
    super(message)
    this.name = 'CustomerOptionsError'
    this.code = code
    this.details = details
    this.statusCode = statusCode
  }
}

/**
 * 错误代码枚举
 */
export enum ErrorCode {
  /** 权限不足 */
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  /** 数据验证失败 */
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  /** 网络错误 */
  NETWORK_ERROR = 'NETWORK_ERROR',
  /** 服务器错误 */
  SERVER_ERROR = 'SERVER_ERROR',
  /** 未知错误 */
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  /** 数据不存在 */
  NOT_FOUND = 'NOT_FOUND',
  /** 数据冲突 */
  CONFLICT = 'CONFLICT'
}

// ==================== Vue相关类型 ====================

/**
 * Vue Ref类型导入
 */
import type { Ref, Reactive, ComputedRef } from 'vue'

/**
 * Naive UI 数据表格列类型
 */
export interface DataTableColumn<T = any> {
  /** 列标题 */
  title: string
  /** 数据字段键 */
  key: string
  /** 列宽度 */
  width?: number
  /** 最小宽度 */
  minWidth?: number
  /** 是否可排序 */
  sortable?: boolean
  /** 自定义渲染函数 */
  render?: (row: T, index: number) => any
  /** 列对齐方式 */
  align?: 'left' | 'center' | 'right'
}

/**
 * Naive UI 表单验证规则
 */
export interface FormValidationRule {
  /** 是否必填 */
  required?: boolean
  /** 错误消息 */
  message?: string
  /** 触发方式 */
  trigger?: string | string[]
  /** 最小长度 */
  min?: number
  /** 最大长度 */
  max?: number
  /** 正则表达式 */
  pattern?: RegExp
  /** 自定义验证函数 */
  validator?: (rule: any, value: any) => boolean | Error | Promise<void>
}

/**
 * Naive UI 选择器选项
 */
export interface SelectOption {
  /** 选项标签 */
  label: string
  /** 选项值 */
  value: string | number
  /** 是否禁用 */
  disabled?: boolean
  /** 选项分组 */
  group?: string
}

/**
 * 表单验证规则集合
 */
export type FormValidationRules = Record<string, FormValidationRule[]>