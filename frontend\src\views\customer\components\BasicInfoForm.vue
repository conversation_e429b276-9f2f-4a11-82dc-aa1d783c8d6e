<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    style="width: 800px; max-width: 90vw;"
    :mask-closable="false"
    :closable="true"
    @close="handleCancel"
  >
    <template #header>
      <div style="display: flex; align-items: center; gap: 8px;">
        <n-icon size="18" color="#1677ff">
          <person-outline />
        </n-icon>客户信息
      </div>
    </template>

    <!-- 表单内容 -->
    <div class="frame">
      <!-- 加载状态提示 -->
      <div v-if="optionsLoading" class="loading-container">
        <n-spin size="large">
          <template #description>
            正在加载选项数据...
          </template>
        </n-spin>
      </div>
      
      <!-- 表单内容 -->
      <n-form
          v-else
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
        >
        <!-- 客户姓名和性别 -->
        <n-grid :cols="2" :x-gap="16" class="form-section">
          <n-form-item-gi label="客户姓名" path="name">
            <n-input
              v-model:value="formData.name"
              placeholder="请输入客户姓名"
              clearable
            />
          </n-form-item-gi>
          <n-form-item-gi label="性别" path="gender">
            <n-select
              v-model:value="formData.gender"
              placeholder="请选择性别"
              :options="genderOptions"
              :render-label="renderGenderLabel"
              clearable
            />
          </n-form-item-gi>
        </n-grid>

        <!-- 手机号码和小区名称 -->
        <n-grid :cols="2" :x-gap="16" class="form-section">
          <n-form-item-gi label="手机号码" path="phone">
            <n-input
              v-model:value="formData.phone"
              placeholder="请输入手机号码"
              type="text"
              maxlength="11"
              clearable
            />
          </n-form-item-gi>
          <n-form-item-gi label="小区名称" path="community">
            <n-input
              v-model:value="formData.community"
              placeholder="请输入小区名称"
              clearable
            />
          </n-form-item-gi>
        </n-grid>

        <!-- 装修类型和房屋面积 -->
        <n-grid :cols="2" :x-gap="16" class="form-section">
          <n-form-item-gi label="装修类型" path="decorationType">
            <n-select
              v-model:value="formData.decorationType"
              placeholder="请选择装修类型"
              :options="decorationTypeOptions"
              :render-label="renderDecorationTypeLabel"
              clearable
            />
          </n-form-item-gi>
          <n-form-item-gi label="房屋面积" path="area">
            <n-input-number
              v-model:value="formData.area"
              placeholder="请输入房屋面积"
              :min="1"
              :max="9999"
              :precision="0"
              clearable
            >
              <template #suffix>㎡</template>
            </n-input-number>
          </n-form-item-gi>
        </n-grid>

        <!-- 客户来源和客户等级 -->
        <n-grid :cols="2" :x-gap="16" class="form-section">
          <n-form-item-gi label="客户来源" path="source">
            <n-select
              v-model:value="formData.source"
              placeholder="请选择客户来源"
              :options="channelSourceOptions"
              :render-label="renderChannelSourceLabel"
              clearable
            />
          </n-form-item-gi>
          <n-form-item-gi label="客户等级" path="level">
            <n-select
              v-model:value="formData.level"
              placeholder="请选择客户等级"
              :options="categoryOptions"
              :render-label="renderCategoryLabel"
              clearable
            />
          </n-form-item-gi>
        </n-grid>

        <!-- 交房情况和重点客户 -->
        <n-grid :cols="2" :x-gap="16" class="form-section">
          <n-form-item-gi label="交房情况" path="houseStatus">
            <n-select
              v-model:value="formData.houseStatus"
              placeholder="请选择交房情况"
              :options="houseStatusOptions"
              :render-label="renderHouseStatusLabel"
              clearable
            />
          </n-form-item-gi>
          <n-form-item-gi label="重点客户" path="isImportant">
            <n-switch
              v-model:value="formData.isImportant"
              :checked-value="true"
              :unchecked-value="false"
            />
          </n-form-item-gi>
        </n-grid>

        <!-- 客户标签 -->
        <n-form-item label="客户标签" path="tags" class="form-section">
          <n-dynamic-tags
            v-model:value="formData.tags as (string | any)[]"
            :render-tag="renderTag"
            placeholder="输入标签后按回车添加"
          />
        </n-form-item>

        <!-- 备注信息 -->
        <n-form-item label="备注信息" path="remark" class="form-section">
          <n-input
            v-model:value="formData.remark"
            type="textarea"
            placeholder="请输入备注信息"
            :rows="3"
            clearable
          />
        </n-form-item>
      
        <!-- 操作按钮 -->
        <n-form-item class="form-section">
          <div style="display: flex; justify-content: flex-end; gap: 12px;">
            <n-button 
              @click="handleCancel"
              :disabled="submitting"
            >
              取消
            </n-button>
            <n-button 
              type="primary" 
              @click="handleSubmit" 
              :loading="submitting"
              :disabled="optionsLoading || submitting"
            >
              {{ isEdit ? '更新' : '创建' }}
            </n-button>
          </div>
        </n-form-item>
      </n-form>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, h, computed, onMounted, type VNodeChild } from 'vue'
import {
  NForm,
  NFormItemGi,
  NGrid,
  NInput,
  NInputNumber,
  NSelect,
  NSwitch,
  NButton,
  NSpace,
  NIcon,
  NAlert,
  NText,
  NTag,
  NModal,
  NDynamicTags,
  useMessage,
  type FormInst,
  type FormRules,
  type SelectRenderLabel
} from 'naive-ui'
import { customerValidationRules, type CustomerBasicInfo } from '@/types/customer'
import type { Customer } from '@/api/customerService'
import { CustomerService } from '@/api/customerService'
import { useCustomerOptionsStore } from '@/stores/customerOptions'
import {
  PersonOutline,
  IdCardOutline,
  CallOutline,
  HomeOutline,
  BusinessOutline,
  ResizeOutline,
  ShareOutline,
  StarOutline,
  PeopleOutline,
  PersonAddOutline,
  FlashOutline,
  RefreshOutline,
  CheckmarkOutline,
  MaleOutline,
  FemaleOutline,
  HelpOutline,
  TrophyOutline,
  DiamondOutline,
  RibbonOutline,
  KeyOutline,
  GlobeOutline,
  PhonePortraitOutline,
  StorefrontOutline,
  TvOutline,
  ConstructOutline,
  BrushOutline,
  HammerOutline,
  ColorPaletteOutline
} from '@vicons/ionicons5'

// Props定义
interface Props {
  show?: boolean
  modelValue?: Customer
  isEdit?: boolean
  loading?: boolean
}

// Emits定义
interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'update:modelValue', value: Customer): void
  (e: 'submit', value: Customer): void
  (e: 'reset'): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  isEdit: false,
  loading: false
})

const emit = defineEmits<Emits>()

// 响应式数据
const message = useMessage()
const formRef = ref<FormInst>()
const optionsStore = useCustomerOptionsStore()

// 模态框显示状态
const showModal = computed({
  get: () => props.show,
  set: (value: boolean) => emit('update:show', value)
})

// 表单数据
const formData = reactive<Customer>({
  id: 0,
  name: '',
  phone: '',
  email: '',
  company: '',
  position: '',
  address: '',
  source: undefined as any,
  status: 'active',
  level: undefined as any,
  gender: undefined as any,
  region: '',
  age: undefined,
  decoration_type: undefined as any,
  house_status: undefined as any,
  budget_range: '',
  contact_time: '',
  tags: [] as string[],
  notes: '',
  assigned_to: undefined,
  // 前端兼容字段
  area: undefined,
  decorationType: undefined as any,
  community: '',
  houseStatus: undefined as any,
  isImportant: false,
  remark: '',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
})

// 渲染函数
const renderTag = (tag: string, index: number) => {
  return h(NTag, {
    type: 'info',
    closable: true,
    onClose: () => {
      if (Array.isArray(formData.tags)) {
        formData.tags.splice(index, 1)
      }
    }
  }, { default: () => tag })
}

// 切换重点客户状态
const toggleKeyCustomer = () => {
  formData.isImportant = !formData.isImportant
}

// 性别选项 - 从 store 获取
const genderOptions = computed(() => optionsStore.toSelectOptions('customer_gender'))

// 其他选项数据 - 从 store 获取
const decorationTypeOptions = computed(() => optionsStore.toSelectOptions('decoration_type'))
const channelSourceOptions = computed(() => optionsStore.toSelectOptions('customer_source'))
const categoryOptions = computed(() => optionsStore.toSelectOptions('customer_level'))
const houseStatusOptions = computed(() => optionsStore.toSelectOptions('house_status'))

// 图标映射 - 为了保持现有的图标显示
const iconMap = {
  // 性别图标
  male: MaleOutline,
  female: FemaleOutline,
  // 装修类型图标
  new: ConstructOutline,
  old: BrushOutline,
  rough: HammerOutline,
  hardcover: ColorPaletteOutline,
  // 客户来源图标
  online: GlobeOutline,
  referral: ShareOutline,
  exhibition: TvOutline,
  store: StorefrontOutline,
  phone: PhonePortraitOutline,
  other: HelpOutline,
  // 客户等级图标
  A: TrophyOutline,
  B: DiamondOutline,
  C: RibbonOutline,
  D: KeyOutline,
  // 房屋状态图标
  undelivered: HomeOutline,
  delivered: CheckmarkOutline,
  renovating: ConstructOutline,
  completed: StarOutline
}

// 渲染标签函数
const renderGenderLabel: SelectRenderLabel = (option): VNodeChild => {
  const icon = iconMap[option.value as keyof typeof iconMap]
  return h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
    h(NIcon, { size: 16 }, { default: () => h(icon) }),
    h('span', String(option.label))
  ])
}

const renderCategoryLabel: SelectRenderLabel = (option): VNodeChild => {
  const icon = iconMap[option.value as keyof typeof iconMap]
  return h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
    h(NIcon, { size: 16 }, { default: () => h(icon) }),
    h('span', String(option.label))
  ])
}

const renderHouseStatusLabel: SelectRenderLabel = (option): VNodeChild => {
  const icon = iconMap[option.value as keyof typeof iconMap]
  return h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
    h(NIcon, { size: 16 }, { default: () => h(icon) }),
    h('span', String(option.label))
  ])
}

const renderDecorationTypeLabel: SelectRenderLabel = (option): VNodeChild => {
  const icon = iconMap[option.value as keyof typeof iconMap]
  return h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
    h(NIcon, { size: 16 }, { default: () => h(icon) }),
    h('span', String(option.label))
  ])
}

const renderChannelSourceLabel: SelectRenderLabel = (option): VNodeChild => {
  const icon = iconMap[option.value as keyof typeof iconMap]
  return h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
    h(NIcon, { size: 16 }, { default: () => h(icon) }),
    h('span', String(option.label))
  ])
}

// 快速模板填充
const fillQuickTemplate = (template: string) => {
  const templates = {
    online: {
      source: 'online',
      level: 'B',
      isImportant: false,
      area: 100
    },
    referral: {
      source: 'referral',
      level: 'A',
      isImportant: true,
      area: 120
    },
    exhibition: {
      source: 'exhibition',
      level: 'B',
      isImportant: false,
      area: 80
    }
  }
  
  const templateData = templates[template as keyof typeof templates]
  if (templateData) {
    Object.assign(formData, templateData)
  }
}

// 表单验证规则
const rules: FormRules = {
  name: {
    required: true,
    message: '请输入客户姓名',
    trigger: ['blur', 'input']
  },
  phone: [
    {
      required: true,
      message: '请输入手机号码',
      trigger: ['blur', 'input']
    },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号码格式',
      trigger: ['blur', 'input']
    }
  ],
  area: {
    validator: (rule: any, value: any) => {
      if (value === null || value === undefined || value === '') {
        return new Error('请输入房屋面积')
      }
      const num = Number(value)
      if (isNaN(num) || num < 1 || num > 9999) {
        return new Error('房屋面积必须在1-9999㎡之间')
      }
      return true
    },
    trigger: ['blur', 'change']
  },
  source: {
    required: true,
    message: '请选择客户来源',
    trigger: ['blur', 'change']
  },
  community: {
    required: true,
    message: '请输入小区名称',
    trigger: ['blur', 'input']
  }
}

// 监听props变化，更新表单数据
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      const convertedData = { ...newValue }
      
      // 转换后端数据为前端格式
      if (convertedData.budget_range) {
        convertedData.area = parseFloat(convertedData.budget_range) || undefined
      }
      if (convertedData.decoration_type) {
        convertedData.decorationType = convertedData.decoration_type
      }
      if (convertedData.house_status) {
        convertedData.houseStatus = convertedData.house_status
      }
      if (convertedData.notes) {
        convertedData.remark = convertedData.notes
      }
      // 转换tags字符串为数组
      let tagsArray: string[] = []
      if (typeof convertedData.tags === 'string' && convertedData.tags) {
        tagsArray = convertedData.tags.split(',').filter(tag => tag.trim())
      } else if (Array.isArray(convertedData.tags)) {
        tagsArray = convertedData.tags
      }
      
      // 确保tags是string[]类型
      const finalData = { ...convertedData, tags: tagsArray }
      Object.assign(formData, finalData)
    }
  },
  { immediate: true, deep: true }
)

// 监听表单数据变化，向上传递
watch(
  formData,
  (newValue) => {
    emit('update:modelValue', { ...newValue })
  },
  { deep: true }
)

// 监听选项数据变化，自动刷新
watch(
  () => [optionsStore.categories],
  () => {
    // 当选项数据发生变化时，重新计算选项
    // 这里不需要额外操作，因为计算属性会自动更新
  },
  { deep: true }
)

// 选项数据加载状态
const optionsLoading = ref(false)

// 初始化选项数据
onMounted(async () => {
  try {
    optionsLoading.value = true
    await optionsStore.loadAllCustomerOptions()
  } catch (error) {
    console.error('加载选项数据失败:', error)
    message.error('加载选项数据失败，请刷新页面重试')
  } finally {
    optionsLoading.value = false
  }
})

// 提交加载状态
const submitting = ref(false)

// 事件处理函数
const handleSubmit = async () => {
  if (submitting.value) return // 防止重复提交
  
  try {
    await formRef.value?.validate()
    submitting.value = true
    
    // 数据转换：将前端兼容字段转换为后端字段
    const customerData = { ...formData }
    
    // 转换字段映射
    if (customerData.area !== undefined) {
      customerData.budget_range = customerData.area?.toString() || ''
    }
    if (customerData.decorationType) {
      customerData.decoration_type = customerData.decorationType
    }
    if (customerData.houseStatus) {
      customerData.house_status = customerData.houseStatus
    }
    if (customerData.remark) {
      customerData.notes = customerData.remark
    }
    // 转换tags数组为字符串
     if (Array.isArray(customerData.tags)) {
       customerData.tags = customerData.tags.join(',')
     }
    
    // 移除前端兼容字段，避免发送到后端
    const { area, decorationType, community, houseStatus, isImportant, remark, ...backendData } = customerData
    
    if (backendData.id && backendData.id > 0) {
      // 更新客户
      await CustomerService.updateCustomer(backendData.id, backendData)
      message.success('客户信息更新成功')
    } else {
      // 创建新客户
      const newCustomer = await CustomerService.createCustomer(backendData)
      formData.id = newCustomer.id
      message.success('客户创建成功')
    }
    
    emit('submit', { ...formData })
    
    // 成功后重置表单
    handleReset()
    emit('update:show', false)
  } catch (error: any) {
    console.error('保存客户信息失败:', error)
    message.error(error.message || '保存失败，请重试')
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  // 取消时重置表单
  handleReset()
  emit('cancel')
  emit('update:show', false)
}

const handleReset = () => {
  formRef.value?.restoreValidation()
  
  // 重置为初始值
  Object.assign(formData, {
    id: 0,
    name: '',
    phone: '',
    email: '',
    company: '',
    position: '',
    address: '',
    source: undefined as any,
    status: 'active',
    level: undefined as any,
    gender: undefined as any,
    region: '',
    age: undefined,
    decoration_type: undefined as any,
    house_status: undefined as any,
    budget_range: '',
    contact_time: '',
    tags: [] as string[],
    notes: '',
    assigned_to: undefined,
    // 前端兼容字段
    area: undefined,
    decorationType: undefined as any,
    community: '',
    houseStatus: undefined as any,
    isImportant: false,
    remark: '',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  })
  
  emit('reset')
}

// 暴露验证方法
const validate = async () => {
  return await formRef.value?.validate()
}

// 暴露重置方法
const resetForm = () => {
  handleReset()
}

defineExpose({
  validate,
  resetForm
})
</script>

<style scoped>
.customer-form-modal {
  width: 100%;
  max-width: none;
  margin: 0;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: visible;
  height: auto;
}

.frame {
  padding: 24px;
  background: #ffffff;
  width: 100%;
  height: auto;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.rectangle28 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.modal-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.form-section {
  margin-bottom: 16px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  padding: 40px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .frame {
    padding: 24px 16px;
  }
  
  .modal-title {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .frame {
    padding: 16px 12px;
  }
  
  .rectangle28 {
    margin-bottom: 24px;
  }
}
</style>