/**
 * 客户参数管理相关常量定义
 * @description 定义客户参数管理功能所需的所有常量
 * <AUTHOR> Document
 * @date 2024-01-20
 */

// ==================== 客户参数分类代码 ====================

/**
 * 客户参数分类代码枚举
 */
export enum CustomerOptionCategory {
  /** 客户来源 */
  SOURCE = 'customer_source',
  /** 客户等级 */
  LEVEL = 'customer_level',
  /** 客户状态 */
  STATUS = 'customer_status',
  /** 客户标签 */
  TAG = 'customer_tag',
  /** 装修类型 */
  DECORATION_TYPE = 'decoration_type',
  /** 房屋状态 */
  HOUSE_STATUS = 'house_status',
  /** 套餐类型 */
  PACKAGE_TYPE = 'package_type',
  /** 支付方式 */
  PAYMENT_METHOD = 'payment_method',
  /** 设计师 */
  DESIGNER = 'designer',
  /** 销售员 */
  SALES = 'sales'
}

/**
 * 客户参数分类信息映射
 */
export const CUSTOMER_OPTION_CATEGORIES = {
  [CustomerOptionCategory.SOURCE]: {
    code: CustomerOptionCategory.SOURCE,
    name: '客户来源',
    description: '客户获取渠道和来源分类',
    icon: 'UserAddOutlined',
    color: '#1890ff'
  },
  [CustomerOptionCategory.LEVEL]: {
    code: CustomerOptionCategory.LEVEL,
    name: '客户等级',
    description: '客户价值等级分类',
    icon: 'CrownOutlined',
    color: '#faad14'
  },
  [CustomerOptionCategory.STATUS]: {
    code: CustomerOptionCategory.STATUS,
    name: '客户状态',
    description: '客户当前状态分类',
    icon: 'FlagOutlined',
    color: '#52c41a'
  },
  [CustomerOptionCategory.TAG]: {
    code: CustomerOptionCategory.TAG,
    name: '客户标签',
    description: '客户特征标签分类',
    icon: 'TagsOutlined',
    color: '#722ed1'
  },
  [CustomerOptionCategory.DECORATION_TYPE]: {
    code: CustomerOptionCategory.DECORATION_TYPE,
    name: '装修类型',
    description: '房屋装修类型分类',
    icon: 'HomeOutlined',
    color: '#13c2c2'
  },
  [CustomerOptionCategory.HOUSE_STATUS]: {
    code: CustomerOptionCategory.HOUSE_STATUS,
    name: '房屋状态',
    description: '房屋当前状态分类',
    icon: 'BuildingOutlined',
    color: '#eb2f96'
  },
  [CustomerOptionCategory.PACKAGE_TYPE]: {
    code: CustomerOptionCategory.PACKAGE_TYPE,
    name: '套餐类型',
    description: '装修套餐类型分类',
    icon: 'GiftOutlined',
    color: '#f5222d'
  },
  [CustomerOptionCategory.PAYMENT_METHOD]: {
    code: CustomerOptionCategory.PAYMENT_METHOD,
    name: '支付方式',
    description: '付款方式分类',
    icon: 'CreditCardOutlined',
    color: '#52c41a'
  },
  [CustomerOptionCategory.DESIGNER]: {
    code: CustomerOptionCategory.DESIGNER,
    name: '设计师',
    description: '设计师人员分类',
    icon: 'DesignOutlined',
    color: '#1890ff'
  },
  [CustomerOptionCategory.SALES]: {
    code: CustomerOptionCategory.SALES,
    name: '销售员',
    description: '销售人员分类',
    icon: 'TeamOutlined',
    color: '#faad14'
  }
} as const

// ==================== 默认选项数据 ====================

/**
 * 默认客户来源选项
 */
export const DEFAULT_CUSTOMER_SOURCES = [
  { code: 'website', value: 'website', label: '官方网站', color: '#1890ff' },
  { code: 'wechat', value: 'wechat', label: '微信推广', color: '#52c41a' },
  { code: 'phone', value: 'phone', label: '电话咨询', color: '#faad14' },
  { code: 'referral', value: 'referral', label: '客户推荐', color: '#722ed1' },
  { code: 'exhibition', value: 'exhibition', label: '展会获取', color: '#13c2c2' },
  { code: 'advertisement', value: 'advertisement', label: '广告投放', color: '#f5222d' },
  { code: 'other', value: 'other', label: '其他渠道', color: '#8c8c8c' }
]

/**
 * 默认客户等级选项
 */
export const DEFAULT_CUSTOMER_LEVELS = [
  { code: 'vip', value: 'vip', label: 'VIP客户', color: '#faad14', sort_order: 1 },
  { code: 'premium', value: 'premium', label: '高级客户', color: '#722ed1', sort_order: 2 },
  { code: 'standard', value: 'standard', label: '标准客户', color: '#1890ff', sort_order: 3 },
  { code: 'basic', value: 'basic', label: '普通客户', color: '#52c41a', sort_order: 4 },
  { code: 'potential', value: 'potential', label: '潜在客户', color: '#13c2c2', sort_order: 5 }
]

/**
 * 默认客户状态选项
 */
export const DEFAULT_CUSTOMER_STATUSES = [
  { code: 'active', value: 'active', label: '活跃客户', color: '#52c41a' },
  { code: 'inactive', value: 'inactive', label: '非活跃客户', color: '#faad14' },
  { code: 'potential', value: 'potential', label: '潜在客户', color: '#1890ff' },
  { code: 'lost', value: 'lost', label: '流失客户', color: '#f5222d' },
  { code: 'blacklist', value: 'blacklist', label: '黑名单', color: '#8c8c8c' }
]

/**
 * 默认客户标签选项
 */
export const DEFAULT_CUSTOMER_TAGS = [
  { code: 'high_value', value: 'high_value', label: '高价值', color: '#faad14' },
  { code: 'frequent_buyer', value: 'frequent_buyer', label: '频繁购买', color: '#52c41a' },
  { code: 'price_sensitive', value: 'price_sensitive', label: '价格敏感', color: '#1890ff' },
  { code: 'quality_focused', value: 'quality_focused', label: '注重品质', color: '#722ed1' },
  { code: 'service_oriented', value: 'service_oriented', label: '服务导向', color: '#13c2c2' },
  { code: 'tech_savvy', value: 'tech_savvy', label: '技术敏感', color: '#eb2f96' }
]

// ==================== 表格配置常量 ====================

/**
 * 表格列宽度配置
 */
export const TABLE_COLUMN_WIDTHS = {
  /** 选择框列宽度 */
  SELECTION: 60,
  /** 序号列宽度 */
  INDEX: 80,
  /** 代码列宽度 */
  CODE: 120,
  /** 名称列宽度 */
  NAME: 150,
  /** 值列宽度 */
  VALUE: 150,
  /** 描述列宽度 */
  DESCRIPTION: 200,
  /** 排序列宽度 */
  SORT_ORDER: 100,
  /** 状态列宽度 */
  STATUS: 100,
  /** 颜色列宽度 */
  COLOR: 100,
  /** 创建时间列宽度 */
  CREATED_AT: 180,
  /** 更新时间列宽度 */
  UPDATED_AT: 180,
  /** 操作列宽度 */
  ACTIONS: 150
}

/**
 * 默认分页配置
 */
export const DEFAULT_PAGINATION = {
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true,
  prefix: ({ itemCount }: { itemCount: number }) => `共 ${itemCount} 条`
}

/**
 * 状态选项
 */
export const STATUS_OPTIONS = [
  { label: '全部', value: null },
  { label: '启用', value: true },
  { label: '禁用', value: false }
]

// ==================== 表单验证常量 ====================

/**
 * 表单验证消息
 */
export const VALIDATION_MESSAGES = {
  REQUIRED: '此字段为必填项',
  CODE_REQUIRED: '请输入选项代码',
  CODE_FORMAT: '选项代码只能包含字母、数字和下划线',
  CODE_LENGTH: '选项代码长度应在2-50个字符之间',
  LABEL_REQUIRED: '请输入显示标签',
  LABEL_LENGTH: '显示标签长度应在1-100个字符之间',
  VALUE_REQUIRED: '请输入选项值',
  VALUE_LENGTH: '选项值长度应在1-200个字符之间',
  DESCRIPTION_LENGTH: '描述长度不能超过500个字符',
  SORT_ORDER_RANGE: '排序值应在0-9999之间',
  COLOR_FORMAT: '请输入有效的颜色值'
}

/**
 * 表单验证规则
 */
export const VALIDATION_RULES = {
  /** 选项代码验证规则 */
  CODE: [
    { required: true, message: VALIDATION_MESSAGES.CODE_REQUIRED, trigger: 'blur' },
    { min: 2, max: 50, message: VALIDATION_MESSAGES.CODE_LENGTH, trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: VALIDATION_MESSAGES.CODE_FORMAT, trigger: 'blur' }
  ],
  /** 显示标签验证规则 */
  LABEL: [
    { required: true, message: VALIDATION_MESSAGES.LABEL_REQUIRED, trigger: 'blur' },
    { min: 1, max: 100, message: VALIDATION_MESSAGES.LABEL_LENGTH, trigger: 'blur' }
  ],
  /** 选项值验证规则 */
  VALUE: [
    { required: true, message: VALIDATION_MESSAGES.VALUE_REQUIRED, trigger: 'blur' },
    { min: 1, max: 200, message: VALIDATION_MESSAGES.VALUE_LENGTH, trigger: 'blur' }
  ],
  /** 描述验证规则 */
  DESCRIPTION: [
    { max: 500, message: VALIDATION_MESSAGES.DESCRIPTION_LENGTH, trigger: 'blur' }
  ],
  /** 排序值验证规则 */
  SORT_ORDER: [
    { type: 'integer' as const, min: 0, max: 9999, message: VALIDATION_MESSAGES.SORT_ORDER_RANGE, trigger: 'blur' }
  ]
}

// ==================== 缓存配置常量 ====================

/**
 * 缓存过期时间配置（毫秒）
 */
export const CACHE_EXPIRY = {
  /** 短期缓存：5分钟 */
  SHORT: 5 * 60 * 1000,
  /** 中期缓存：30分钟 */
  MEDIUM: 30 * 60 * 1000,
  /** 长期缓存：2小时 */
  LONG: 2 * 60 * 60 * 1000
}

/**
 * 缓存键配置
 */
export const CACHE_KEYS = {
  /** 选项分类列表缓存键 */
  CATEGORIES: 'customer_option_categories',
  /** 选项项列表缓存键 */
  ITEMS: (categoryCode: string) => `customer_option_items_${categoryCode}`
}

// ==================== API端点常量 ====================

/**
 * API端点配置
 */
export const API_ENDPOINTS = {
  // 选项数据获取端点（用于下拉框等数据展示）
  OPTIONS: {
    CATEGORIES: '/api/options/categories',
    ALL: '/api/options/all',
    BY_CATEGORY: (categoryName: string) => `/api/options/by-category/${categoryName}`,
    ITEMS: (categoryId: string) => `/api/options/items/${categoryId}`
  },
  // 选项管理端点（用于CRUD操作）
  MANAGEMENT: {
    CATEGORIES: {
      LIST: '/api/options-management/categories',
      CREATE: '/api/options-management/categories',
      UPDATE: (id: string) => `/api/options-management/categories/${id}`,
      DELETE: (id: string) => `/api/options-management/categories/${id}`
    },
    ITEMS: {
      LIST: '/api/options-management/items',
      CREATE: '/api/options-management/items',
      UPDATE: (id: string) => `/api/options-management/items/${id}`,
      DELETE: (id: string) => `/api/options-management/items/${id}`,
      BATCH_DELETE: '/api/options-management/items/batch-delete'
    }
  }
}

// ==================== 导出所有常量 ====================

export default {
  CustomerOptionCategory,
  CUSTOMER_OPTION_CATEGORIES,
  DEFAULT_CUSTOMER_SOURCES,
  DEFAULT_CUSTOMER_LEVELS,
  DEFAULT_CUSTOMER_STATUSES,
  DEFAULT_CUSTOMER_TAGS,
  TABLE_COLUMN_WIDTHS,
  DEFAULT_PAGINATION,
  STATUS_OPTIONS,
  VALIDATION_MESSAGES,
  VALIDATION_RULES,
  CACHE_EXPIRY,
  CACHE_KEYS,
  API_ENDPOINTS
}