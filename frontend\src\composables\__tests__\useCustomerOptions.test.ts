import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { ref, computed } from 'vue'
import { createPinia, setActivePinia } from 'pinia'
import { useCustomerOptions } from '../useCustomerOptions'
import * as customerOptionsApi from '@/api/customerOptions'
import { CUSTOMER_OPTION_CATEGORIES } from '@/constants/customerOptions'
import type { OptionItem, OptionCategory, GetOptionItemsResponse, CreateOptionItemResponse } from '@/types/customerOptions'
// Mock API
vi.mock('@/api/customerOptions', () => ({
  getOptionCategories: vi.fn(),
  getOptionItemsByCategory: vi.fn(),
  createOptionItem: vi.fn(),
  updateOptionItem: vi.fn(),
  deleteOptionItem: vi.fn(),
  batchDeleteOptionItems: vi.fn(),
  batchUpdateOptionItemStatus: vi.fn()
}))

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock message API
vi.mock('naive-ui', () => ({
  createDiscreteApi: () => ({
    message: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn(),
      info: vi.fn()
    }
  })
}))

describe('useCustomerOptions', () => {
  let pinia: any

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('初始化', () => {
    it('应该正确初始化响应式数据', () => {
      const { items, loading, submitting, searchKeyword, statusFilter, pagination, checkedRowKeys } = useCustomerOptions({ categoryCode: 'customer_source' })
      
      expect(items.value).toEqual([])
      expect(loading.value).toBe(false)
      expect(submitting.value).toBe(false)
      expect(searchKeyword.value).toBe('')
      expect(statusFilter.value).toBe(null)
      expect(checkedRowKeys.value).toEqual([])
      expect(pagination).toEqual({
        page: 1,
        pageSize: 10,
        itemCount: 0,
        showSizePicker: true,
        pageSizes: [10, 20, 50, 100],
        onChange: expect.any(Function),
        onUpdatePageSize: expect.any(Function)
      })
    })

    it('应该正确计算分类信息', () => {
      const { categoryInfo } = useCustomerOptions({ categoryCode: 'customer_source' })
      
      expect(categoryInfo.value).toEqual(CUSTOMER_OPTION_CATEGORIES['customer_source'])
    })

    it('应该正确计算缓存键', () => {
      const { clearCache } = useCustomerOptions({ categoryCode: 'customer_level' })
      
      expect(clearCache).toBeDefined()
    })
  })

  describe('缓存管理', () => {


    it('应该能够清除缓存', async () => {
      const { clearCache } = useCustomerOptions({ categoryCode: 'customer_source' })
      
      // 调用清除缓存方法
      clearCache()
      
      // 验证缓存已清除
      expect(true).toBe(true) // 简单验证
    })

    it('应该处理过期的缓存数据', () => {
      const expiredData = {
        data: [{ id: 1, code: 'test' }],
        timestamp: Date.now() - (6 * 60 * 60 * 1000) // 6小时前
      }
      localStorageMock.getItem.mockReturnValue(JSON.stringify(expiredData))
      
      const { clearCache } = useCustomerOptions({ categoryCode: 'customer_source' })
      
      expect(clearCache).toBeDefined()
    })
  })

  describe('数据加载', () => {
    it('应该能够加载数据', async () => {
      const mockData = {
        items: [{ 
            id: '1', 
            categoryCode: 'customer_source',
            code: 'test', 
            value: 'test', 
            name: '测试',
             sortOrder: 1,
             enabled: true,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z'
          }],
        page: 1,
        pageSize: 10,
        totalPages: 1,
        total: 1
      }
      vi.mocked(customerOptionsApi.getOptionItemsByCategory).mockResolvedValue({
        code: 200,
        success: true,
        data: mockData,
        message: '获取成功'
      })
      
      const { loadItems, items, pagination } = useCustomerOptions({ categoryCode: 'customer_source' })
      await loadItems()
      
      expect(customerOptionsApi.getOptionItemsByCategory).toHaveBeenCalledWith('customer_source', {
        page: 1,
        pageSize: 10,
        search: '',
        status: ''
      })
      expect(items.value).toEqual(mockData.items)
      expect(pagination.itemCount).toBe(mockData.total)
    })

    it('应该优先使用缓存数据', async () => {
      const cachedData = [{ 
          id: '1', 
          categoryCode: 'customer_source',
          code: 'cached', 
          value: 'cached', 
          name: '缓存数据',
          sortOrder: 1,
          enabled: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }]
      localStorageMock.getItem.mockReturnValue(JSON.stringify({
        data: cachedData,
        timestamp: Date.now()
      }))
      
      const { loadItems, items } = useCustomerOptions({ categoryCode: 'customer_source' })
      await loadItems()
      
      expect(items.value).toEqual(cachedData)
      expect(customerOptionsApi.getOptionItemsByCategory).not.toHaveBeenCalled()
    })

    it('应该处理加载错误', async () => {
      vi.mocked(customerOptionsApi.getOptionItemsByCategory).mockRejectedValue(new Error('API Error'))
      
      const { loadItems } = useCustomerOptions({ categoryCode: 'customer_source' })
      
      await expect(loadItems()).rejects.toThrow('API Error')
    })

    it('应该在强制刷新时跳过缓存', async () => {
      const cachedData = [{ 
          id: '1', 
          categoryCode: 'customer_source',
          code: 'cached',
          name: 'cached',
          value: 'cached',
          sortOrder: 1,
          enabled: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }]
      const apiData = {
        items: [{ 
            id: '2', 
            categoryCode: 'customer_source',
            code: 'api', 
            value: 'api', 
            name: 'API数据',
            sortOrder: 1,
            enabled: true,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z'
          }],
        page: 1,
        pageSize: 10,
        totalPages: 1,
        total: 1
      }
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify({
        data: cachedData,
        timestamp: Date.now()
      }))
      vi.mocked(customerOptionsApi.getOptionItemsByCategory).mockResolvedValue({
        code: 200,
        success: true,
        data: apiData,
        message: '获取成功'
      })
      
      const { loadItems, items } = useCustomerOptions({ categoryCode: 'customer_source' })
      await loadItems() // 刷新数据
      
      expect(items.value).toEqual(apiData.items)
      expect(customerOptionsApi.getOptionItemsByCategory).toHaveBeenCalled()
    })
  })

  describe('CRUD 操作', () => {
    it('应该能够创建选项', async () => {
      const newItem = { 
          id: '1', 
          categoryCode: 'customer_source',
          code: 'new', 
          value: 'new', 
          name: '新选项',
          sortOrder: 1,
          enabled: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }
      vi.mocked(customerOptionsApi.createOptionItem).mockResolvedValue({
        code: 200,
        success: true,
        data: newItem,
        message: '创建成功'
      })
      
      const createData = {
        categoryCode: 'customer_source',
        code: 'new',
        value: 'new',
        name: '新选项'
      }
      
      const { createItem } = useCustomerOptions({ categoryCode: 'customer_source' })
      const result = await createItem(createData)
      
      expect(customerOptionsApi.createOptionItem).toHaveBeenCalledWith(createData)
      expect(result).toEqual(newItem)
    })

    it('应该能够更新选项', async () => {
      const updatedItem = { 
          id: '1', 
          categoryCode: 'customer_source',
          code: 'updated', 
          value: 'updated', 
          name: '更新选项',
          sortOrder: 1,
          enabled: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }
      vi.mocked(customerOptionsApi.updateOptionItem).mockResolvedValue({
        code: 200,
        success: true,
        data: updatedItem,
        message: '更新成功'
      })
      
      const updateData = {
        id: 1,
        code: 'updated',
        value: 'updated',
        label: '更新选项'
      }
      
      const { updateItem } = useCustomerOptions({ categoryCode: 'customer_source' })
      const result = await updateItem('1', updateData)
      
      expect(customerOptionsApi.updateOptionItem).toHaveBeenCalledWith('1', updateData)
      expect(result).toBe(true)
    })

    it('应该能够删除选项', async () => {
      vi.mocked(customerOptionsApi.deleteOptionItem).mockResolvedValue({ 
        code: 200,
        success: true, 
        data: { deleted: true },
        message: '删除成功'
      })
      
      const { deleteItem } = useCustomerOptions({ categoryCode: 'customer_source' })
      const result = await deleteItem('1')
      
      expect(customerOptionsApi.deleteOptionItem).toHaveBeenCalledWith('1')
      expect(result).toEqual({ 
          code: 200,
          success: true, 
          data: { deleted: true },
          message: '删除成功'
        })
    })

    it('应该能够批量删除选项', async () => {
      vi.mocked(customerOptionsApi.batchDeleteOptionItems).mockResolvedValue({ 
        code: 200,
        success: true, 
        data: { deleted: true },
        message: '批量删除成功'
      })
      
      const { batchDelete } = useCustomerOptions({ categoryCode: 'customer_source' })
      const result = await batchDelete(['1', '2', '3'])
      
      expect(customerOptionsApi.batchDeleteOptionItems).toHaveBeenCalledWith(['1', '2', '3'])
        expect(result).toEqual({ 
           code: 200,
           success: true, 
           data: { deleted: true },
           message: '批量删除成功'
         })
    })

    it('应该能够批量更新状态', async () => {
      vi.mocked(customerOptionsApi.batchUpdateOptionItemsStatus).mockResolvedValue({ 
        code: 200,
        success: true, 
        data: [],
        message: '批量更新成功'
      })
      
      const { batchUpdateStatus } = useCustomerOptions({ categoryCode: 'customer_source' })
      const result = await batchUpdateStatus(['1', '2'], true)
      
      expect(customerOptionsApi.batchUpdateOptionItemsStatus).toHaveBeenCalledWith(['1', '2'], true)
      expect(result).toEqual({ 
          code: 200,
          success: true, 
          data: [],
          message: '批量更新成功'
        })
    })
  })

  describe('搜索和筛选', () => {
    it('应该能够处理搜索', async () => {
      const { handleSearch, searchKeyword } = useCustomerOptions({ categoryCode: 'customer_source' })
      
      handleSearch('测试搜索')
      
      expect(searchKeyword.value).toBe('测试搜索')
    })

    it('应该能够处理状态筛选', async () => {
      const { handleFilter, statusFilter } = useCustomerOptions({ categoryCode: 'customer_source' })
      
      handleFilter('active')
      
      expect(statusFilter.value).toBe('active')
    })

    it('应该能够重置筛选条件', () => {
      const { resetFilters, searchKeyword, statusFilter, pagination } = useCustomerOptions({ categoryCode: 'customer_source' })
      
      // 设置一些筛选条件
      searchKeyword.value = '测试'
      statusFilter.value = 'active'
      pagination.page = 2
      
      resetFilters()
      
      expect(searchKeyword.value).toBe('')
      expect(statusFilter.value).toBe('')
      expect(pagination.page).toBe(1)
    })
  })

  describe('计算属性', () => {
    it('应该正确计算选中项', () => {
      const { checkedRowKeys, items } = useCustomerOptions({ categoryCode: 'customer_source' })
      
      items.value = [
        { 
           id: '1', 
           categoryCode: 'customer_source',
           code: 'item1', 
           value: 'item1', 
           name: '项目1',
           sortOrder: 1,
           enabled: true,
           createdAt: '2024-01-01T00:00:00Z',
           updatedAt: '2024-01-01T00:00:00Z'
         },
         { 
           id: '2', 
           categoryCode: 'customer_source',
           code: 'item2', 
           value: 'item2', 
           name: '项目2',
           sortOrder: 2,
           enabled: true,
           createdAt: '2024-01-01T00:00:00Z',
           updatedAt: '2024-01-01T00:00:00Z'
         },
         { 
           id: '3', 
           categoryCode: 'customer_source',
           code: 'item3', 
           value: 'item3', 
           name: '项目3',
           sortOrder: 3,
           enabled: true,
           createdAt: '2024-01-01T00:00:00Z',
           updatedAt: '2024-01-01T00:00:00Z'
         }
      ]
      checkedRowKeys.value = ['1', '3']
      
      const selectedItems = computed(() => {
          return items.value.filter(item => checkedRowKeys.value.includes(item.id))
        })
        
        expect(selectedItems.value).toEqual([
          { 
            id: '1', 
            categoryCode: 'customer_source',
            code: 'item1', 
            value: 'item1', 
            name: '项目1',
            sortOrder: 1,
            enabled: true,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z'
          },
          { 
            id: '3', 
            categoryCode: 'customer_source',
            code: 'item3', 
            value: 'item3', 
            name: '项目3',
            sortOrder: 3,
            enabled: true,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z'
          }
      ])
    })

    it('应该正确计算是否无数据', () => {
      const { isEmpty, items, loading } = useCustomerOptions({ categoryCode: 'customer_source' })
      
      // 无数据且未加载
      expect(isEmpty.value).toBe(true)
      
      // 有数据
      items.value = [{ 
        id: '1', 
        categoryCode: 'customer_source',
        code: 'test', 
        value: 'test', 
        name: '测试',
        sortOrder: 1,
        enabled: true,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }]
      expect(isEmpty.value).toBe(false)
      
      // 无数据但正在加载
      items.value = []
      loading.value = true
      expect(isEmpty.value).toBe(false)
    })
  })

  describe('防抖搜索', () => {
    it('应该实现防抖搜索功能', async () => {
      const mockLoadData = vi.fn()
      vi.mocked(customerOptionsApi.getOptionItemsByCategory).mockResolvedValue({
         code: 200,
         message: 'success',
         success: true,
         data: {
           items: [],
           total: 0,
           page: 1,
           pageSize: 10,
           totalPages: 0
         }
       })
      
      const { handleSearch } = useCustomerOptions({ categoryCode: 'customer_source' })
      
      // 快速连续调用
      handleSearch('a')
      handleSearch('ab')
      handleSearch('abc')
      
      // 等待防抖时间
      await new Promise(resolve => setTimeout(resolve, 300))
      
      // 应该只调用一次
      expect(customerOptionsApi.getOptionItemsByCategory).toHaveBeenCalledTimes(1)
    })
  })

  describe('错误处理', () => {
    it('应该处理创建操作的错误', async () => {
      vi.mocked(customerOptionsApi.createOptionItem).mockRejectedValue(new Error('创建失败'))
      
      const { createItem } = useCustomerOptions({ categoryCode: 'customer_source' })
      
      await expect(createItem({
        categoryCode: 'customer_source',
        code: 'test',
        value: 'test',
        name: '测试'
      })).rejects.toThrow('创建失败')
    })

    it('应该处理更新操作的错误', async () => {
      vi.mocked(customerOptionsApi.updateOptionItem).mockRejectedValue(new Error('更新失败'))
      
      const { updateItem } = useCustomerOptions({ categoryCode: 'customer_source' })
      
      await expect(updateItem('1', {
         value: 'test',
         label: '测试'
       })).rejects.toThrow('更新失败')
    })

    it('应该处理删除操作的错误', async () => {
      vi.mocked(customerOptionsApi.deleteOptionItem).mockRejectedValue(new Error('删除失败'))
      
      const { deleteItem } = useCustomerOptions({ categoryCode: 'customer_source' })
      
      await expect(deleteItem('1')).rejects.toThrow('删除失败')
    })
  })

  describe('状态管理', () => {
    it('应该正确管理加载状态', async () => {
      let resolvePromise: (value: any) => void
      const promise = new Promise(resolve => {
        resolvePromise = resolve
      })
      vi.mocked(customerOptionsApi.getOptionItemsByCategory).mockReturnValue(promise as Promise<GetOptionItemsResponse>)
      
      const { loadItems, loading } = useCustomerOptions({ categoryCode: 'customer_source' })
      
      // 开始加载
      const loadPromise = loadItems()
      expect(loading.value).toBe(true)
      
      // 完成加载
      resolvePromise!({ data: [], total: 0 })
      await loadPromise
      expect(loading.value).toBe(false)
    })

    it('应该正确管理提交状态', async () => {
      let resolvePromise: (value: any) => void
      const promise = new Promise(resolve => {
        resolvePromise = resolve
      })
      vi.mocked(customerOptionsApi.createOptionItem).mockReturnValue(promise as Promise<CreateOptionItemResponse>)
      
      const { createItem, submitting } = useCustomerOptions({ categoryCode: 'customer_source' })
      
      // 开始提交
      const createPromise = createItem({
        categoryCode: 'customer_source',
        code: 'test',
        value: 'test',
        name: '测试'
      })
      expect(submitting.value).toBe(true)
      
      // 完成提交
      resolvePromise!({ id: 1, code: 'test', value: 'test', name: '测试' })
      await createPromise
      expect(submitting.value).toBe(false)
    })
  })
})