<template>
  <div class="ticket-list">
    <n-card title="工单管理" :bordered="false">
      <!-- 搜索和筛选区域 -->
      <div class="search-section mb-4">
        <n-space>
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索工单号、客户姓名"
            clearable
            style="width: 300px"
          >
            <template #prefix>
              <n-icon :component="Search" />
            </template>
          </n-input>
          <n-select
            v-model:value="statusFilter"
            placeholder="工单状态"
            clearable
            style="width: 150px"
            :options="statusOptions"
          />
          <n-select
            v-model:value="priorityFilter"
            placeholder="优先级"
            clearable
            style="width: 120px"
            :options="priorityOptions"
          />
          <n-button type="primary" @click="searchTickets">
            <template #icon>
              <n-icon :component="Search" />
            </template>
            搜索
          </n-button>
          <n-button @click="resetSearch">重置</n-button>
          <n-button type="primary" @click="showCreateModal = true">
            <template #icon>
              <n-icon :component="Add" />
            </template>
            新建工单
          </n-button>
        </n-space>
      </div>

      <!-- 工单列表 -->
      <n-data-table
        :columns="columns"
        :data="filteredTickets"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row: Ticket) => row.id"
        striped
      />
    </n-card>

    <!-- 新建工单模态框 -->
    <n-modal v-model:show="showCreateModal" preset="dialog" title="新建工单">
      <n-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-placement="left"
        label-width="auto"
      >
        <n-form-item label="客户" path="customerId">
          <n-select
            v-model:value="createForm.customerId"
            placeholder="选择客户"
            filterable
            :options="customerOptions"
            @update:value="onCustomerSelect"
          />
        </n-form-item>
        <n-form-item label="楼盘信息" path="propertyInfo">
          <n-input
            v-model:value="createForm.propertyInfo"
            placeholder="自动获取客户楼盘信息"
            readonly
          />
        </n-form-item>
        <n-form-item label="问题描述" path="description">
          <n-input
            v-model:value="createForm.description"
            type="textarea"
            placeholder="请详细描述维修问题"
            :rows="4"
          />
        </n-form-item>
        <n-form-item label="优先级" path="priority">
          <n-select
            v-model:value="createForm.priority"
            placeholder="选择优先级"
            :options="priorityOptions"
          />
        </n-form-item>
        <n-form-item label="预计完成时间" path="expectedTime">
          <n-date-picker
            v-model:value="createForm.expectedTime"
            type="datetime"
            placeholder="选择预计完成时间"
            style="width: 100%"
          />
        </n-form-item>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showCreateModal = false">取消</n-button>
          <n-button type="primary" @click="createTicket">创建</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 工单详情模态框 -->
    <n-modal v-model:show="showDetailModal" preset="dialog" title="工单详情" style="width: 800px">
      <div v-if="selectedTicket">
        <n-descriptions :column="2" bordered>
          <n-descriptions-item label="工单号">{{ selectedTicket.ticketNo }}</n-descriptions-item>
          <n-descriptions-item label="状态">
            <n-tag :type="getStatusType(selectedTicket.status)">{{ getStatusText(selectedTicket.status) }}</n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="客户姓名">{{ selectedTicket.customerName }}</n-descriptions-item>
          <n-descriptions-item label="联系电话">{{ selectedTicket.phone }}</n-descriptions-item>
          <n-descriptions-item label="楼盘信息" :span="2">{{ selectedTicket.propertyInfo }}</n-descriptions-item>
          <n-descriptions-item label="问题描述" :span="2">{{ selectedTicket.description }}</n-descriptions-item>
          <n-descriptions-item label="优先级">
            <n-tag :type="getPriorityType(selectedTicket.priority)">{{ getPriorityText(selectedTicket.priority) }}</n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="创建时间">{{ formatDate(selectedTicket.createdAt) }}</n-descriptions-item>
          <n-descriptions-item label="预计完成时间">{{ formatDate(selectedTicket.expectedTime) }}</n-descriptions-item>
          <n-descriptions-item label="实际完成时间">{{ selectedTicket.completedAt ? formatDate(selectedTicket.completedAt) : '-' }}</n-descriptions-item>
        </n-descriptions>
      </div>
      <template #action>
        <n-button @click="showDetailModal = false">关闭</n-button>
      </template>
    </n-modal>

    <!-- 工单处理模态框 -->
    <n-modal v-model:show="showProcessModal" preset="dialog" title="工单处理" style="width: 800px">
      <div v-if="processingTicket">
        <!-- 工单基本信息 -->
        <n-descriptions title="工单信息" :column="2" bordered class="mb-4">
          <n-descriptions-item label="工单号">{{ processingTicket.ticketNo }}</n-descriptions-item>
          <n-descriptions-item label="客户姓名">{{ processingTicket.customerName }}</n-descriptions-item>
          <n-descriptions-item label="联系电话">{{ processingTicket.phone }}</n-descriptions-item>
          <n-descriptions-item label="优先级">
            <n-tag :type="getPriorityType(processingTicket.priority)">{{ getPriorityText(processingTicket.priority) }}</n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="楼盘信息" :span="2">{{ processingTicket.propertyInfo }}</n-descriptions-item>
          <n-descriptions-item label="问题描述" :span="2">{{ processingTicket.description }}</n-descriptions-item>
        </n-descriptions>

        <!-- 处理表单 -->
        <n-form
          :model="processForm"
          label-placement="left"
          label-width="100px"
        >
          <n-form-item label="处理状态">
            <n-select
              v-model:value="processForm.status"
              :options="statusOptions"
            />
          </n-form-item>
          <n-form-item label="分配给">
            <n-input
              v-model:value="processForm.assignedTo"
              placeholder="输入处理人员姓名"
            />
          </n-form-item>
          <n-form-item label="处理说明">
            <n-input
              v-model:value="processForm.processNote"
              type="textarea"
              placeholder="请输入处理说明或备注"
              :rows="3"
            />
          </n-form-item>
          <n-form-item label="完成时间" v-if="processForm.status === 'completed'">
            <n-date-picker
              v-model:value="processForm.completedTime"
              type="datetime"
              placeholder="选择完成时间"
              style="width: 100%"
            />
          </n-form-item>
        </n-form>
      </div>
      <template #action>
        <n-space>
          <n-button @click="showProcessModal = false">取消</n-button>
          <n-button type="primary" @click="submitProcess">提交处理</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, h } from 'vue'
import { useMessage } from 'naive-ui'
import type { DataTableColumns, FormInst } from 'naive-ui'
import { Search, Add, Eye, CreateOutline, CheckmarkCircleOutline } from '@vicons/ionicons5'
import { useCustomerOptionsStore } from '@/stores/customerOptions'
import { CustomerOptionCategory } from '@/constants/customerOptions'

interface Ticket {
  id: string
  ticketNo: string
  customerId: string
  customerName: string
  phone: string
  propertyInfo: string
  description: string
  status: 'pending' | 'processing' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  createdAt: string
  expectedTime: string
  completedAt?: string
  assignedTo?: string
}

interface Customer {
  id: string
  name: string
  phone: string
  propertyInfo: string
}

const message = useMessage()
const customerOptionsStore = useCustomerOptionsStore()
const loading = ref(false)
const showCreateModal = ref(false)
const showDetailModal = ref(false)
const selectedTicket = ref<Ticket | null>(null)
const createFormRef = ref<FormInst | null>(null)

// 搜索和筛选
const searchKeyword = ref('')
const statusFilter = ref<string | null>(null)
const priorityFilter = ref<string | null>(null)

// 表单数据
const createForm = reactive({
  customerId: '',
  propertyInfo: '',
  description: '',
  priority: 'medium',
  expectedTime: null as number | null
})

// 表单验证规则
const createRules = {
  customerId: { required: true, message: '请选择客户', trigger: 'change' },
  description: { required: true, message: '请输入问题描述', trigger: 'blur' },
  priority: { required: true, message: '请选择优先级', trigger: 'change' },
  expectedTime: { required: true, message: '请选择预计完成时间', trigger: 'change' }
}

// 模拟数据
const tickets = ref<Ticket[]>([
  {
    id: '1',
    ticketNo: 'T202401001',
    customerId: '1',
    customerName: '张三',
    phone: '13800138001',
    propertyInfo: '阳光花园 A栋 1201室',
    description: '卫生间水龙头漏水，需要更换密封圈',
    status: 'pending',
    priority: 'medium',
    createdAt: '2024-01-15 09:30:00',
    expectedTime: '2024-01-16 18:00:00'
  },
  {
    id: '2',
    ticketNo: 'T202401002',
    customerId: '2',
    customerName: '李四',
    phone: '13800138002',
    propertyInfo: '绿城花园 B栋 2305室',
    description: '空调制冷效果差，需要检查维修',
    status: 'processing',
    priority: 'high',
    createdAt: '2024-01-14 14:20:00',
    expectedTime: '2024-01-15 20:00:00',
    assignedTo: '维修师傅王五'
  },
  {
    id: '3',
    ticketNo: 'T202401003',
    customerId: '3',
    customerName: '王五',
    phone: '13800138003',
    propertyInfo: '海景豪庭 C栋 1508室',
    description: '电路跳闸，需要检查电路问题',
    status: 'completed',
    priority: 'urgent',
    createdAt: '2024-01-13 10:15:00',
    expectedTime: '2024-01-13 18:00:00',
    completedAt: '2024-01-13 16:30:00',
    assignedTo: '电工师傅赵六'
  }
])

const customers = ref<Customer[]>([
  { id: '1', name: '张三', phone: '13800138001', propertyInfo: '阳光花园 A栋 1201室' },
  { id: '2', name: '李四', phone: '13800138002', propertyInfo: '绿城花园 B栋 2305室' },
  { id: '3', name: '王五', phone: '13800138003', propertyInfo: '海景豪庭 C栋 1508室' },
  { id: '4', name: '赵六', phone: '13800138004', propertyInfo: '蓝天公寓 D栋 808室' }
])

// 选项数据
const statusOptions = computed(() => 
  customerOptionsStore.toSelectOptions(CustomerOptionCategory.STATUS)
)

const priorityOptions = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' },
  { label: '紧急', value: 'urgent' }
]

const customerOptions = computed(() => 
  customers.value.map(customer => ({
    label: `${customer.name} (${customer.phone})`,
    value: customer.id
  }))
)

// 筛选后的工单列表
const filteredTickets = computed(() => {
  let result = tickets.value
  
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(ticket => 
      ticket.ticketNo.toLowerCase().includes(keyword) ||
      ticket.customerName.toLowerCase().includes(keyword)
    )
  }
  
  if (statusFilter.value) {
    result = result.filter(ticket => ticket.status === statusFilter.value)
  }
  
  if (priorityFilter.value) {
    result = result.filter(ticket => ticket.priority === priorityFilter.value)
  }
  
  return result
})

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true,
  prefix: ({ itemCount }: { itemCount: number }) => `共 ${itemCount} 条`
})

// 表格列配置
const columns: DataTableColumns<Ticket> = [
  {
    title: '工单号',
    key: 'ticketNo',
    width: 120
  },
  {
    title: '客户姓名',
    key: 'customerName',
    width: 100
  },
  {
    title: '联系电话',
    key: 'phone',
    width: 120
  },
  {
    title: '楼盘信息',
    key: 'propertyInfo',
    width: 180,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '问题描述',
    key: 'description',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row) => {
      return h('n-tag', { type: getStatusType(row.status) }, () => getStatusText(row.status))
    }
  },
  {
    title: '优先级',
    key: 'priority',
    width: 80,
    render: (row) => {
      return h('n-tag', { type: getPriorityType(row.priority) }, () => getPriorityText(row.priority))
    }
  },
  {
    title: '创建时间',
    key: 'createdAt',
    width: 150,
    render: (row) => formatDate(row.createdAt)
  },
  {
    title: '预计完成',
    key: 'expectedTime',
    width: 150,
    render: (row) => formatDate(row.expectedTime)
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render: (row) => {
      return h('n-space', {}, () => [
        h('n-button', {
          size: 'small',
          type: 'primary',
          ghost: true,
          onClick: () => viewTicket(row)
        }, () => '查看'),
        h('n-button', {
          size: 'small',
          type: 'warning',
          ghost: true,
          onClick: () => editTicket(row)
        }, () => '编辑'),
        row.status === 'pending' ? h('n-button', {
          size: 'small',
          type: 'success',
          ghost: true,
          onClick: () => processTicket(row)
        }, () => '处理') : null
      ])
    }
  }
]

// 工具函数
const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'warning',
    processing: 'info',
    completed: 'success',
    cancelled: 'error'
  }
  return types[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const getPriorityType = (priority: string) => {
  const types: Record<string, string> = {
    low: 'default',
    medium: 'info',
    high: 'warning',
    urgent: 'error'
  }
  return types[priority] || 'default'
}

const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return texts[priority] || priority
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 事件处理函数
const searchTickets = () => {
  // 搜索逻辑已在computed中实现
  message.success('搜索完成')
}

const resetSearch = () => {
  searchKeyword.value = ''
  statusFilter.value = null
  priorityFilter.value = null
}

const onCustomerSelect = (customerId: string) => {
  const customer = customers.value.find(c => c.id === customerId)
  if (customer) {
    createForm.propertyInfo = customer.propertyInfo
  }
}

const createTicket = async () => {
  try {
    await createFormRef.value?.validate()
    
    const customer = customers.value.find(c => c.id === createForm.customerId)
    if (!customer) return
    
    const newTicket: Ticket = {
      id: Date.now().toString(),
      ticketNo: `T${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(tickets.value.length + 1).padStart(3, '0')}`,
      customerId: createForm.customerId,
      customerName: customer.name,
      phone: customer.phone,
      propertyInfo: createForm.propertyInfo,
      description: createForm.description,
      status: 'pending',
      priority: createForm.priority as any,
      createdAt: new Date().toLocaleString('zh-CN'),
      expectedTime: new Date(createForm.expectedTime!).toLocaleString('zh-CN')
    }
    
    tickets.value.unshift(newTicket)
    showCreateModal.value = false
    
    // 重置表单
    Object.assign(createForm, {
      customerId: '',
      propertyInfo: '',
      description: '',
      priority: 'medium',
      expectedTime: null
    })
    
    message.success('工单创建成功')
  } catch (error) {
    message.error('请完善工单信息')
  }
}

const viewTicket = (ticket: Ticket) => {
  selectedTicket.value = ticket
  showDetailModal.value = true
}

const showProcessModal = ref(false)
const processingTicket = ref<Ticket | null>(null)
const processForm = reactive({
  status: '',
  assignedTo: '',
  processNote: '',
  expectedTime: null as number | null,
  completedTime: null as number | null
})

const editTicket = (ticket: Ticket) => {
  processingTicket.value = ticket
  processForm.status = ticket.status
  processForm.assignedTo = ticket.assignedTo || ''
  showProcessModal.value = true
}

const processTicket = (ticket: Ticket) => {
  processingTicket.value = ticket
  processForm.status = 'processing'
  processForm.assignedTo = ''
  showProcessModal.value = true
}

const submitProcess = async () => {
  if (!processingTicket.value) return
  
  try {
    // 更新工单状态
    const ticketIndex = tickets.value.findIndex(t => t.id === processingTicket.value!.id)
    if (ticketIndex !== -1) {
      tickets.value[ticketIndex] = {
        ...tickets.value[ticketIndex],
        status: processForm.status as any,
        assignedTo: processForm.assignedTo,
        completedAt: processForm.status === 'completed' && processForm.completedTime 
          ? new Date(processForm.completedTime).toLocaleString('zh-CN') 
          : undefined
      }
    }
    
    showProcessModal.value = false
    message.success('工单处理成功')
    
    // 重置表单
    Object.assign(processForm, {
      status: '',
      assignedTo: '',
      processNote: '',
      expectedTime: null,
      completedTime: null
    })
  } catch (error) {
    message.error('处理失败，请重试')
  }
}

onMounted(async () => {
  // 加载客户选项数据
  await customerOptionsStore.loadAllCustomerOptions()
})
</script>

<style scoped>
.ticket-list {
  padding: 16px;
}

.search-section {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>