# 项目依赖关系图

## 项目概览

这是一个基于 **Vue 3 + TypeScript + Express + MySQL** 的全栈CRM管理系统，采用前后端分离架构。

### 技术栈

**前端技术栈：**

* Vue 3.5.18 (Composition API)

* TypeScript 5.8

* Pinia 3.0.3 (状态管理)

* Vue Router 4.5.1 (路由管理)

* Naive UI 2.42.0 (UI组件库)

* Vite 6.3.5 (构建工具)

* Axios 1.11.0 (HTTP客户端)

* ECharts 5.6.0 (图表库)

**后端技术栈：**

* Node.js 22.15.0

* Express 4.21.2

* TypeScript 5.8

* MySQL2 3.14.3 (数据库驱动)

* UUID 11.1.0 (唯一标识符)

* Multer 2.0.2 (文件上传)

* CORS 2.8.5 (跨域处理)

**开发工具：**

* ESLint + Prettier (代码规范)

* Jest 30.0.5 (单元测试)

* Playwright (E2E测试)

* Vitest (前端测试)

* Nodemon (开发热重载)

## 项目结构依赖关系

```mermaid
graph TB
    subgraph "项目根目录"
        A[admin/] --> B[frontend/]
        A --> C[api/]
        A --> D[src/database/]
        A --> E[scripts/]
        A --> F[.trae/]
    end
    
    subgraph "前端模块"
        B --> B1[src/views/]
        B --> B2[src/components/]
        B --> B3[src/stores/]
        B --> B4[src/api/]
        B --> B5[src/router/]
        B --> B6[src/types/]
        B --> B7[src/utils/]
    end
    
    subgraph "后端模块"
        C --> C1[routes/]
        C --> C2[middleware/]
        C --> C3[tests/]
    end
    
    subgraph "数据库模块"
        D --> D1[MySQLManager.ts]
        D --> D2[init.sql]
    end
    
    subgraph "脚本模块"
        E --> E1[init-mysql-options-data.ts]
        E --> E2[create-tables.ts]
        E --> E3[mysql/]
    end
```

## 核心依赖关系

### 1. 前端组件依赖关系

```mermaid
graph TD
    subgraph "页面组件 (Views)"
        V1[CustomerSettings.vue]
        V2[CustomerList.vue]
        V3[Dashboard.vue]
        V4[Login.vue]
    end
    
    subgraph "业务组件 (Components)"
        C1[CustomerOptionManager.vue]
        C2[CustomerParameterManager.vue]
        C3[CustomerTable.vue]
        C4[CustomerForm.vue]
        C5[AppLayout.vue]
        C6[SideMenu.vue]
    end
    
    subgraph "状态管理 (Stores)"
        S1[optionsStore.ts]
        S2[customerStore.ts]
        S3[authStore.ts]
        S4[appStore.ts]
    end
    
    subgraph "API服务 (API)"
        A1[options.ts]
        A2[customer.ts]
        A3[auth.ts]
        A4[request.ts]
    end
    
    V1 --> C1
    V1 --> C2
    V1 --> S1
    V2 --> C3
    V2 --> C4
    V2 --> S2
    C1 --> S1
    C2 --> S1
    S1 --> A1
    S2 --> A2
    S3 --> A3
    A1 --> A4
    A2 --> A4
    A3 --> A4
```

### 2. 后端API依赖关系

```mermaid
graph TD
    subgraph "API路由 (Routes)"
        R1[options.ts]
        R2[optionsManagement.ts]
        R3[customer.ts]
        R4[auth.ts]
    end
    
    subgraph "数据库层 (Database)"
        D1[MySQLManager.ts]
        D2[MySQL数据库]
    end
    
    subgraph "中间件 (Middleware)"
        M1[errorHandler.ts]
        M2[validation.ts]
    end
    
    R1 --> D1
    R2 --> D1
    R3 --> D1
    R4 --> D1
    D1 --> D2
    R1 --> M1
    R2 --> M1
    R3 --> M1
    R4 --> M1
```

### 3. 数据流依赖关系

```mermaid
sequenceDiagram
    participant U as 用户界面
    participant C as Vue组件
    participant S as Pinia Store
    participant A as API服务
    participant R as Express路由
    participant D as MySQL数据库
    
    U->>C: 用户操作
    C->>S: 调用Store方法
    S->>A: 发起HTTP请求
    A->>R: 请求后端API
    R->>D: 执行数据库操作
    D-->>R: 返回数据
    R-->>A: 响应数据
    A-->>S: 更新状态
    S-->>C: 响应式更新
    C-->>U: 界面更新
```

## 关键文件依赖关系

### 1. 客户设置模块依赖

**前端依赖链：**

```
CustomerSettings.vue
├── CustomerOptionManager.vue
├── CustomerParameterManager.vue
├── optionsStore.ts
│   ├── options.ts (API)
│   └── request.ts
└── types/options.ts
```

**后端依赖链：**

```
api/routes/options.ts
├── MySQLManager.ts
├── middleware/errorHandler.ts
└── MySQL数据库
    ├── option_categories表
    └── option_items表
```

### 2. 路由依赖关系

**路由配置依赖：**

```
router/index.ts
├── components/layout/AppLayout.vue
├── views/settings/CustomerSettings.vue
├── stores/modules/auth.ts
└── 各个页面组件
```

### 3. 状态管理依赖

**Pinia Store依赖：**

```
stores/index.ts
├── modules/auth.ts
├── modules/app.ts
├── modules/customer.ts
├── modules/user.ts
└── optionsStore.ts
```

## 数据库依赖关系

### 表结构依赖

```mermaid
erDiagram
    OPTION_CATEGORIES {
        varchar id PK
        varchar code UK
        varchar name
        text description
        boolean is_active
        int sort_order
        timestamp created_at
        timestamp updated_at
    }
    
    OPTION_ITEMS {
        varchar id PK
        varchar category_id FK
        varchar code
        varchar label
        varchar value
        varchar color
        varchar icon
        text description
        boolean is_active
        int sort_order
        timestamp created_at
        timestamp updated_at
    }
    
    CUSTOMERS {
        varchar id PK
        varchar name
        varchar phone
        varchar source_id FK
        varchar level_id FK
        timestamp created_at
        timestamp updated_at
    }
    
    OPTION_CATEGORIES ||--o{ OPTION_ITEMS : "has"
    OPTION_ITEMS ||--o{ CUSTOMERS : "source/level"
```

### 初始化脚本依赖

```
scripts/init-mysql-options-data.ts
├── src/database/MySQLManager.ts
├── 预定义分类数据
│   ├── customer_source
│   ├── customer_level
│   ├── decoration_type
│   ├── house_status
│   └── budget_range
└── 对应的选项项数据
```

## 构建和部署依赖

### 开发环境依赖

```mermaid
graph LR
    A[npm run dev] --> B[concurrently]
    B --> C[frontend: vite dev server :8080]
    B --> D[backend: nodemon :3000]
    C --> E[前端代理配置]
    E --> D
    D --> F[MySQL数据库 :3306]
```

### 构建依赖

```
npm run build
├── TypeScript编译 (tsc -b)
├── Vite构建 (frontend)
├── ESLint检查
└── 测试运行 (Jest + Vitest)
```

## 配置文件依赖

### 环境配置

```
.env (根目录)
├── DB_HOST
├── DB_PORT
├── DB_USER
├── DB_PASSWORD
└── DB_NAME

frontend/vite.config.ts
├── 代理配置 (proxy)
├── 插件配置
└── 构建配置

api/server.ts
├── Express配置
├── CORS配置
├── 路由挂载
└── 端口配置 (5000)
```

## 测试依赖关系

### 测试文件结构

```
测试依赖
├── frontend/src/__tests__/ (Vitest)
│   ├── components/
│   ├── stores/
│   └── composables/
├── api/tests/ (Jest)
│   ├── customer.test.ts
│   └── setup.ts
└── frontend/e2e/ (Playwright)
    └── vue.spec.ts
```

## 关键约束和注意事项

### 1. 端口配置约束

* **前端开发服务器：8080** (不是5173)

* **后端API服务器：3000**

* **MySQL数据库：3306**

* **前端代理配置必须指向3000端口**

### 2. 组件修改约束

* 修改组件前必须检查依赖关系

* 删除组件时必须清理所有引用

* 路由配置必须与组件文件同步

* Store状态必须与API接口对应

### 3. API接口约束

* 前端API调用路径必须与后端路由一致

* 数据库初始化脚本必须在API使用前执行

* 错误处理必须在前后端都实现

### 4. 数据库约束

* 选项分类代码必须与前端常量一致

* 外键关系必须正确维护

* 数据迁移脚本必须按顺序执行

## 修改组件时的检查清单

### 修改前检查

1. [ ] 确认组件的所有依赖关系
2. [ ] 检查是否有其他组件引用此组件
3. [ ] 确认路由配置是否涉及
4. [ ] 检查Store状态是否需要修改
5. [ ] 确认API接口是否需要调整

### 修改后验证

1. [ ] 运行TypeScript类型检查
2. [ ] 执行ESLint代码检查
3. [ ] 运行相关单元测试
4. [ ] 验证页面功能正常
5. [ ] 检查控制台无错误
6. [ ] 更新相关文档

### 删除组件时的清理清单

1. [ ] 删除路由配置中的引用
2. [ ] 清理其他组件的import语句
3. [ ] 移除Store中的相关状态
4. [ ] 删除对应的API调用
5. [ ] 清理测试文件
6. [ ] 更新类型定义文件

***

**最后更新时间：** 2024-12-20
**维护人员：** 开发团队
**版本：** v1.0.0

> 注意：每次修改组件后都应该更新此依赖关系文档，确保信息的准确性和时效性。

