import { request } from '@/utils/request'
import type {
  WechatCustomer,
  BrowsingTrack,
  BrowsingAction,
  ShareRecord,
  WechatGroup,
  WechatMessage,
  WechatStatistics,
  GetWechatCustomersParams,
  GetBrowsingTracksParams,
  GetShareRecordsParams,
  GetWechatGroupsParams,
  GetWechatMessagesParams,
  PaginationResponse
} from '@/types'

// API 响应解包工具函数
const unwrapResponse = <T>(response: any): T => {
  // 后端返回格式: {success: boolean, data: T}
  if (response.data && response.data.success !== undefined) {
    return response.data.data || response.data
  }
  return response.data
}



// API 请求参数接口（本地定义的查询参数）
export interface WechatCustomerQuery {
  page?: number
  pageSize?: number
  keyword?: string
  subscribe_status?: string
  gender?: number
  city?: string
  province?: string
  tags?: string[]
  date_range?: [string, string]
}

export interface BrowsingTrackQuery {
  page?: number
  pageSize?: number
  customer_id?: number
  openid?: string
  page_url?: string
  device_type?: string
  date_range?: [string, string]
}

export interface ShareRecordQuery {
  page?: number
  pageSize?: number
  customer_id?: number
  share_type?: string
  content_type?: string
  share_platform?: string
  date_range?: [string, string]
}

// 微信客户管理 API
export const wechatCustomerService = {
  // 获取微信客户列表
  async getCustomers(params?: GetWechatCustomersParams): Promise<PaginationResponse<WechatCustomer>> {
    const response = await request.get('/api/wechat/customers', { params })
    return unwrapResponse<PaginationResponse<WechatCustomer>>(response)
  },

  // 获取微信客户详情
  async getCustomer(id: number): Promise<WechatCustomer> {
    const response = await request.get<WechatCustomer>(`/api/wechat/customers/${id}`)
    return unwrapResponse<WechatCustomer>(response)
  },

  // 更新微信客户信息
  async updateCustomer(id: number, data: Partial<WechatCustomer>): Promise<WechatCustomer> {
    const response = await request.put<WechatCustomer>(`/api/wechat/customers/${id}`, data)
    return unwrapResponse<WechatCustomer>(response)
  },

  // 同步微信客户信息
  async syncCustomer(openid: string): Promise<WechatCustomer> {
    const response = await request.post<WechatCustomer>('/api/wechat/customers/sync', { openid })
    return unwrapResponse<WechatCustomer>(response)
  },

  // 批量同步微信客户
  async batchSyncCustomers(openids: string[]): Promise<{ success: number; failed: number }> {
    const response = await request.post<{ success: number; failed: number }>('/api/wechat/customers/batch-sync', { openids })
    return unwrapResponse<{ success: number; failed: number }>(response)
  },

  // 设置客户标签
  async setCustomerTags(id: number, tags: string[]): Promise<void> {
    const response = await request.put(`/api/wechat/customers/${id}/tags`, { tags })
    return unwrapResponse<void>(response)
  },

  // 设置客户备注
  async setCustomerRemark(id: number, remark: string): Promise<void> {
    const response = await request.put(`/api/wechat/customers/${id}/remark`, { remark })
    return unwrapResponse<void>(response)
  }
}

// 浏览轨迹 API
export const browsingTrackService = {
  // 获取浏览轨迹列表
  async getTracks(params?: GetBrowsingTracksParams): Promise<PaginationResponse<BrowsingTrack>> {
    const response = await request.get('/api/wechat/browsing-tracks', { params })
    return unwrapResponse<PaginationResponse<BrowsingTrack>>(response)
  },

  // 获取浏览轨迹详情
  async getTrack(id: number): Promise<BrowsingTrack> {
    const response = await request.get<BrowsingTrack>(`/api/wechat/browsing-tracks/${id}`)
    return unwrapResponse<BrowsingTrack>(response)
  },

  // 获取客户浏览轨迹
  async getCustomerTracks(customerId: number, params: Omit<BrowsingTrackQuery, 'customer_id'> = {}): Promise<{
    data: BrowsingTrack[]
    total: number
  }> {
    const response = await request.get<{
      data: BrowsingTrack[]
      total: number
    }>(`/api/wechat/customers/${customerId}/browsing-tracks`, { params })
    return unwrapResponse<{
      data: BrowsingTrack[]
      total: number
    }>(response)
  },

  // 记录浏览轨迹
  async recordTrack(data: Omit<BrowsingTrack, 'id' | 'created_at'>): Promise<BrowsingTrack> {
    const response = await request.post<BrowsingTrack>('/api/wechat/browsing-tracks', data)
    return unwrapResponse<BrowsingTrack>(response)
  },

  // 记录浏览行为
  async recordAction(trackId: number, data: Omit<BrowsingAction, 'id' | 'track_id' | 'timestamp'>): Promise<BrowsingAction> {
    const response = await request.post<BrowsingAction>(`/api/wechat/browsing-tracks/${trackId}/actions`, data)
    return unwrapResponse<BrowsingAction>(response)
  },

  // 获取热力图数据
  async getHeatmapData(params: { page_url: string; date_range?: [string, string] }): Promise<{
    clicks: Array<{ x: number; y: number; count: number }>
    scrolls: Array<{ depth: number; count: number }>
  }> {
    const response = await request.get<{
      clicks: Array<{ x: number; y: number; count: number }>
      scrolls: Array<{ depth: number; count: number }>
    }>('/api/wechat/browsing-tracks/heatmap', { params })
    return unwrapResponse<{
      clicks: Array<{ x: number; y: number; count: number }>
      scrolls: Array<{ depth: number; count: number }>
    }>(response)
  }
}

// 转发分享 API
export const shareRecordService = {
  // 获取分享记录列表
  async getRecords(params?: GetShareRecordsParams): Promise<PaginationResponse<ShareRecord>> {
    const response = await request.get('/api/wechat/share-records', { params })
    return unwrapResponse<PaginationResponse<ShareRecord>>(response)
  },

  // 获取分享记录详情
  async getRecord(id: number): Promise<ShareRecord> {
    const response = await request.get<ShareRecord>(`/api/wechat/share-records/${id}`)
    return unwrapResponse<ShareRecord>(response)
  },

  // 创建分享记录
  async createRecord(data: Omit<ShareRecord, 'id' | 'created_at' | 'updated_at'>): Promise<ShareRecord> {
    const response = await request.post<ShareRecord>('/api/wechat/share-records', data)
    return unwrapResponse<ShareRecord>(response)
  },

  // 更新分享统计
  async updateStats(id: number, stats: { view_count?: number; click_count?: number; conversion_count?: number }): Promise<void> {
    const response = await request.put(`/api/wechat/share-records/${id}/stats`, stats)
    return unwrapResponse<void>(response)
  },

  // 获取分享效果统计
  async getShareStats(params: { date_range?: [string, string]; content_type?: string }): Promise<{
    total_shares: number
    total_views: number
    total_clicks: number
    total_conversions: number
    conversion_rate: number
    top_sharers: Array<{
      customer_id: number
      customer_name: string
      share_count: number
    }>
    popular_content: Array<{
      content_id: number
      content_title: string
      share_count: number
      view_count: number
    }>
  }> {
    const response = await request.get<{
      total_shares: number
      total_views: number
      total_clicks: number
      total_conversions: number
      conversion_rate: number
      top_sharers: Array<{
        customer_id: number
        customer_name: string
        share_count: number
      }>
      popular_content: Array<{
        content_id: number
        content_title: string
        share_count: number
        view_count: number
      }>
    }>('/api/wechat/share-records/stats', { params })
    return unwrapResponse<{
      total_shares: number
      total_views: number
      total_clicks: number
      total_conversions: number
      conversion_rate: number
      top_sharers: Array<{
        customer_id: number
        customer_name: string
        share_count: number
      }>
      popular_content: Array<{
        content_id: number
        content_title: string
        share_count: number
        view_count: number
      }>
    }>(response)
  }
}

// 微信群组 API
export const wechatGroupService = {
  // 获取微信群列表
  async getGroups(params?: GetWechatGroupsParams): Promise<PaginationResponse<WechatGroup>> {
    const response = await request.get('/api/wechat/groups', { params })
    return unwrapResponse<PaginationResponse<WechatGroup>>(response)
  },

  // 获取群组详情
  async getGroup(id: number): Promise<WechatGroup> {
    const response = await request.get<WechatGroup>(`/api/wechat/groups/${id}`)
    return unwrapResponse<WechatGroup>(response)
  },

  // 创建群组
  async createGroup(data: Omit<WechatGroup, 'id' | 'created_at' | 'updated_at'>): Promise<WechatGroup> {
    const response = await request.post<WechatGroup>('/api/wechat/groups', data)
    return unwrapResponse<WechatGroup>(response)
  },

  // 更新群组
  async updateGroup(id: number, data: Partial<WechatGroup>): Promise<WechatGroup> {
    const response = await request.put<WechatGroup>(`/api/wechat/groups/${id}`, data)
    return unwrapResponse<WechatGroup>(response)
  },

  // 删除群组
  async deleteGroup(id: number): Promise<void> {
    const response = await request.delete(`/api/wechat/groups/${id}`)
    return unwrapResponse<void>(response)
  },

  // 获取群组成员
  async getGroupMembers(groupId: number): Promise<WechatCustomer[]> {
    const response = await request.get<WechatCustomer[]>(`/api/wechat/groups/${groupId}/members`)
    return unwrapResponse<WechatCustomer[]>(response)
  },

  // 添加群组成员
  async addGroupMembers(groupId: number, openids: string[]): Promise<void> {
    const response = await request.post(`/api/wechat/groups/${groupId}/members`, { openids })
    return unwrapResponse<void>(response)
  },

  // 移除群组成员
  async removeGroupMembers(groupId: number, openids: string[]): Promise<void> {
    const response = await request.delete(`/api/wechat/groups/${groupId}/members`, { data: { openids } })
    return unwrapResponse<void>(response)
  }
}

// 微信消息 API
export const wechatMessageService = {
  // 获取微信消息列表
  async getMessages(params?: GetWechatMessagesParams): Promise<PaginationResponse<WechatMessage>> {
    const response = await request.get('/api/wechat/messages', { params })
    return unwrapResponse<PaginationResponse<WechatMessage>>(response)
  },

  // 获取消息详情
  async getMessage(id: number): Promise<WechatMessage> {
    const response = await request.get<WechatMessage>(`/api/wechat/messages/${id}`)
    return unwrapResponse<WechatMessage>(response)
  },

  // 发送消息
  async sendMessage(data: {
    to_openid?: string
    group_id?: number
    message_type: string
    content: string
    media_url?: string
  }): Promise<WechatMessage> {
    const response = await request.post<WechatMessage>('/api/wechat/messages', data)
    return unwrapResponse<WechatMessage>(response)
  },

  // 标记消息已读
  async markAsRead(id: number): Promise<void> {
    const response = await request.put(`/api/wechat/messages/${id}/read`)
    return unwrapResponse<void>(response)
  },

  // 回复消息
  async replyMessage(id: number, content: string): Promise<void> {
    const response = await request.put(`/api/wechat/messages/${id}/reply`, { content })
    return unwrapResponse<void>(response)
  },

  // 批量标记已读
  async batchMarkAsRead(ids: number[]): Promise<void> {
    const response = await request.put('/api/wechat/messages/batch-read', { ids })
    return unwrapResponse<void>(response)
  }
}

// 微信统计 API
export const wechatStatisticsService = {
  // 获取总体统计
  async getOverallStats(params: { date_range?: [string, string] } = {}): Promise<WechatStatistics> {
    const response = await request.get('/api/wechat/statistics/overall', { params })
    return unwrapResponse<WechatStatistics>(response)
  },

  // 获取客户增长趋势
  async getCustomerGrowthTrend(params: { date_range?: [string, string]; period?: 'day' | 'week' | 'month' } = {}): Promise<Array<{
    date: string
    new_customers: number
    active_customers: number
    total_customers: number
  }>> {
    const response = await request.get('/api/wechat/statistics/customer-growth', { params })
    return unwrapResponse<Array<{
      date: string
      new_customers: number
      active_customers: number
      total_customers: number
    }>>(response)
  },

  // 获取消息统计
  async getMessageStats(params: { date_range?: [string, string] } = {}): Promise<{
    total_messages: number
    messages_by_type: Record<string, number>
    messages_by_hour: Array<{ hour: number; count: number }>
    response_rate: number
    avg_response_time: number
  }> {
    const response = await request.get('/api/wechat/statistics/messages', { params })
    return unwrapResponse<{
      total_messages: number
      messages_by_type: Record<string, number>
      messages_by_hour: Array<{ hour: number; count: number }>
      response_rate: number
      avg_response_time: number
    }>(response)
  },

  // 获取浏览统计
  async getBrowsingStats(params: { date_range?: [string, string] } = {}): Promise<{
    total_visits: number
    unique_visitors: number
    avg_visit_duration: number
    bounce_rate: number
    popular_pages: Array<{
      url: string
      title: string
      visits: number
      avg_duration: number
    }>
    device_distribution: Record<string, number>
    browser_distribution: Record<string, number>
  }> {
    const response = await request.get('/api/wechat/statistics/browsing', { params })
    return unwrapResponse<{
      total_visits: number
      unique_visitors: number
      avg_visit_duration: number
      bounce_rate: number
      popular_pages: Array<{
        url: string
        title: string
        visits: number
        avg_duration: number
      }>
      device_distribution: Record<string, number>
      browser_distribution: Record<string, number>
    }>(response)
  },

  // 获取分享统计
  async getShareStats(params: { date_range?: [string, string] } = {}): Promise<{
    total_shares: number
    total_views: number
    conversion_rate: number
    shares_by_platform: Record<string, number>
    shares_by_content_type: Record<string, number>
    top_content: Array<{
      content_id: number
      content_title: string
      share_count: number
      view_count: number
    }>
  }> {
    const response = await request.get('/api/wechat/statistics/shares', { params })
    return unwrapResponse<{
      total_shares: number
      total_views: number
      conversion_rate: number
      shares_by_platform: Record<string, number>
      shares_by_content_type: Record<string, number>
      top_content: Array<{
        content_id: number
        content_title: string
        share_count: number
        view_count: number
      }>
    }>(response)
  }
}

export default {
  wechatCustomerService,
  browsingTrackService,
  shareRecordService,
  wechatGroupService,
  wechatMessageService,
  wechatStatisticsService
}