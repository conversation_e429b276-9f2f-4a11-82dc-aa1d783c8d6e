<template>
  <div class="option-items-management">
    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="action-left">
        <n-space>
          <n-select
            v-model:value="selectedCategory"
            placeholder="选择分类"
            :options="categoryOptions"
            clearable
            style="width: 200px"
            @update:value="handleCategoryChange"
          />
          <n-button v-if="canCreate" type="primary" @click="showAddModal = true" :disabled="!selectedCategory">
            <template #icon>
              <n-icon><add-outline /></n-icon>
            </template>
            添加选项
          </n-button>
          <n-button @click="refreshData" :loading="loading">
            <template #icon>
              <n-icon><refresh-outline /></n-icon>
            </template>
            刷新
          </n-button>
          <n-button
            v-if="canDelete"
            type="error"
            @click="handleBatchDelete"
            :disabled="checkedRowKeys.length === 0"
          >
            <template #icon>
              <n-icon><trash-outline /></n-icon>
            </template>
            批量删除 ({{ checkedRowKeys.length }})
          </n-button>
        </n-space>
      </div>
      <div class="action-right">
        <n-space>
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索选项代码、值或标签"
            clearable
            style="width: 260px"
            @input="handleSearch"
          >
            <template #prefix>
              <n-icon><search-outline /></n-icon>
            </template>
          </n-input>
          <n-select
            v-model:value="statusFilter"
            placeholder="状态筛选"
            clearable
            style="width: 120px"
            :options="statusOptions"
            @update:value="handleStatusFilter"
          />
        </n-space>
      </div>
    </div>

    <!-- 数据表格 -->
    <n-data-table
      :columns="columns"
      :data="filteredItems"
      :loading="loading"
      :pagination="pagination"
      :row-key="(row: OptionItem) => row.id"
      v-model:checked-row-keys="checkedRowKeys"
      size="medium"
    />

    <!-- 添加选项弹窗 -->
    <n-modal v-model:show="showAddModal" preset="dialog" title="添加选项">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-placement="left"
        label-width="100px"
      >
        <n-form-item label="所属分类" path="category_id">
          <n-select
            v-model:value="formData.category_id"
            :options="categoryOptions"
            placeholder="请选择分类"
          />
        </n-form-item>
        <n-form-item label="选项代码" path="code">
          <n-input
            v-model:value="formData.code"
            placeholder="请输入选项代码（唯一标识）"
          />
        </n-form-item>
        <n-form-item label="选项值" path="value">
          <n-input
            v-model:value="formData.value"
            placeholder="请输入选项值"
          />
        </n-form-item>
        <n-form-item label="显示标签" path="label">
          <n-input
            v-model:value="formData.label"
            placeholder="请输入显示标签"
          />
        </n-form-item>
        <n-form-item label="描述" path="description">
          <n-input
            v-model:value="formData.description"
            type="textarea"
            placeholder="请输入选项描述"
            :rows="3"
          />
        </n-form-item>
        <n-form-item label="排序" path="sort_order">
          <n-input-number
            v-model:value="formData.sort_order"
            placeholder="排序值"
            :min="0"
          />
        </n-form-item>
        <n-form-item label="状态" path="is_active">
          <n-switch v-model:value="formData.is_active" />
        </n-form-item>
        <n-form-item label="扩展属性" path="extra_data">
          <n-input
            v-model:value="extraDataText"
            type="textarea"
            placeholder="JSON格式的扩展数据（可选）"
            :rows="3"
          />
        </n-form-item>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showAddModal = false">取消</n-button>
          <n-button type="primary" @click="handleSubmit" :loading="submitting">
            添加
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 编辑选项弹窗 -->
    <n-modal v-model:show="showEditModal" preset="dialog" title="编辑选项">
      <n-form
        ref="editFormRef"
        :model="editFormData"
        :rules="formRules"
        label-placement="left"
        label-width="100px"
      >
        <n-form-item label="所属分类" path="category_id">
          <n-select
            v-model:value="editFormData.category_id"
            :options="categoryOptions"
            placeholder="请选择分类"
            disabled
          />
        </n-form-item>
        <n-form-item label="选项代码" path="code">
          <n-input
            v-model:value="editFormData.code"
            placeholder="请输入选项代码（唯一标识）"
          />
        </n-form-item>
        <n-form-item label="选项值" path="value">
          <n-input
            v-model:value="editFormData.value"
            placeholder="请输入选项值"
          />
        </n-form-item>
        <n-form-item label="显示标签" path="label">
          <n-input
            v-model:value="editFormData.label"
            placeholder="请输入显示标签"
          />
        </n-form-item>
        <n-form-item label="描述" path="description">
          <n-input
            v-model:value="editFormData.description"
            type="textarea"
            placeholder="请输入选项描述"
            :rows="3"
          />
        </n-form-item>
        <n-form-item label="排序" path="sort_order">
          <n-input-number
            v-model:value="editFormData.sort_order"
            placeholder="排序值"
            :min="0"
          />
        </n-form-item>
        <n-form-item label="状态" path="is_active">
          <n-switch v-model:value="editFormData.is_active" />
        </n-form-item>
        <n-form-item label="扩展属性" path="extra_data">
          <n-input
            v-model:value="editExtraDataText"
            type="textarea"
            placeholder="JSON格式的扩展数据（可选）"
            :rows="3"
          />
        </n-form-item>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showEditModal = false">取消</n-button>
          <n-button type="primary" @click="handleUpdate" :loading="submitting">
            更新
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch, h } from 'vue'
import {
  NDataTable, NButton, NIcon, NSpace, NModal, NForm, NFormItem,
  NInput, NInputNumber, NSwitch, NSelect, NTag, useMessage, useDialog
} from 'naive-ui'
import { useAuthStore } from '@/stores/modules/auth'
import { AddOutline, RefreshOutline, CreateOutline, TrashOutline, SearchOutline } from '@vicons/ionicons5'
import type { DataTableColumns, SelectOption } from 'naive-ui'
import { optionCategoriesApi, optionItemsApi } from '@/api/options'
import type { OptionCategory, OptionItem, CreateOptionItemRequest, UpdateOptionItemRequest } from '@/types/options'

const message = useMessage()
const dialog = useDialog()
const authStore = useAuthStore()

// 权限检查
const canRead = computed(() => authStore.hasPermission('settings:options:read'))
const canCreate = computed(() => authStore.hasPermission('settings:options:create'))
const canUpdate = computed(() => authStore.hasPermission('settings:options:update'))
const canDelete = computed(() => authStore.hasPermission('settings:options:delete'))

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const showAddModal = ref(false)
const showEditModal = ref(false)
const selectedCategory = ref<string | null>(null)
const categories = ref<OptionCategory[]>([])
const items = ref<OptionItem[]>([])
const filteredItems = ref<OptionItem[]>([])
const checkedRowKeys = ref<Array<string | number>>([])
const formRef = ref()
const editFormRef = ref()
const extraDataText = ref('')
const editExtraDataText = ref('')
const searchKeyword = ref('')
const statusFilter = ref<string | null>(null)

// 状态筛选选项
const statusOptions: SelectOption[] = [
  { label: '启用', value: 'true' },
  { label: '禁用', value: 'false' }
]

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    pagination.page = page
    loadItems()
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    loadItems()
  }
})

// 表单数据
const formData = reactive<CreateOptionItemRequest>({
  category_id: '',
  code: '',
  value: '',
  label: '',
  description: '',
  sort_order: 0,
  is_active: true,
  color: '',
  icon: ''
})

const editFormData = reactive<UpdateOptionItemRequest>({
  id: '',
  code: '',
  value: '',
  label: '',
  description: '',
  sort_order: 0,
  is_active: true,
  category_id: '',
  color: '',
  icon: ''
})

// 分类选项
const categoryOptions = computed<SelectOption[]>(() => {
  return categories.value.map(cat => ({
    label: cat.name,
    value: cat.code
  }))
})

// 表单验证规则
const formRules = {
  category_id: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  code: [
    { required: true, message: '请输入选项代码', trigger: 'blur' }
  ],
  value: [
    { required: true, message: '请输入选项值', trigger: 'blur' }
  ],
  label: [
    { required: true, message: '请输入显示标签', trigger: 'blur' }
  ],
  sort_order: [
    {
      required: true,
      message: '请输入排序值',
      trigger: 'blur',
      validator: (rule: any, value: any) => {
        if (value === null || value === undefined || value === '') {
          return new Error('请输入排序值')
        }
        if (isNaN(Number(value))) {
          return new Error('排序值必须是数字')
        }
        return true
      }
    }
  ]
}

// 表格列配置
const columns: DataTableColumns<OptionItem> = [
  {
    type: 'selection'
  },
  {
    title: '选项代码',
    key: 'code',
    width: 120
  },
  {
    title: '选项值',
    key: 'value',
    width: 120
  },
  {
    title: '显示标签',
    key: 'label',
    width: 120
  },
  {
    title: '描述',
    key: 'description',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '排序',
    key: 'sort_order',
    width: 80
  },
  {
    title: '状态',
    key: 'is_active',
    width: 100,
    render(row) {
      return h(NTag, {
        type: row.is_active ? 'success' : 'default'
      }, {
        default: () => row.is_active ? '启用' : '禁用'
      })
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 180,
    render(row) {
      return new Date(row.created_at).toLocaleString()
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render(row) {
      const actions: any[] = []
      
      if (canUpdate.value) {
        actions.push(
          h(NButton, {
            size: 'small',
            type: 'primary',
            onClick: () => handleEdit(row)
          }, {
            default: () => '编辑',
            icon: () => h(NIcon, {}, { default: () => h(CreateOutline) })
          })
        )
      }
      
      if (canDelete.value) {
        actions.push(
          h(NButton, {
            size: 'small',
            type: 'error',
            onClick: () => handleDelete(row)
          }, {
            default: () => '删除',
            icon: () => h(NIcon, {}, { default: () => h(TrashOutline) })
          })
        )
      }
      
      return h(NSpace, {}, {
        default: () => actions
      })
    }
  }
]

// 监听选中分类变化
watch(selectedCategory, (newValue) => {
  if (newValue) {
    formData.category_id = newValue
  }
})

// 监听扩展数据文本变化
watch(extraDataText, (newValue) => {
  try {
    if (newValue.trim()) {
      const parsed = JSON.parse(newValue)
      formData.color = parsed.color || ''
      formData.icon = parsed.icon || ''
    } else {
      formData.color = ''
      formData.icon = ''
    }
  } catch (error) {
    // JSON 解析错误，保持原值
  }
})

watch(editExtraDataText, (newValue) => {
  try {
    if (newValue.trim()) {
      const parsed = JSON.parse(newValue)
      editFormData.color = parsed.color || ''
      editFormData.icon = parsed.icon || ''
    } else {
      editFormData.color = ''
      editFormData.icon = ''
    }
  } catch (error) {
    // JSON 解析错误，保持原值
  }
})

// 加载分类数据
const loadCategories = async () => {
  try {
    const response = await optionCategoriesApi.getList({ page: 1, page_size: 100 })
    categories.value = response.data
  } catch (error) {
    message.error('加载分类数据失败')
    console.error('Load categories error:', error)
  }
}

// 加载选项数据
const loadItems = async () => {
  if (!canRead.value) {
    message.error('您没有权限查看选项数据')
    return
  }
  
  if (!selectedCategory.value) {
    items.value = []
    filteredItems.value = []
    return
  }

  try {
    loading.value = true
    const response = await optionItemsApi.getList({
      category_code: selectedCategory.value,
      page: pagination.page,
      page_size: pagination.pageSize
    })
    items.value = response.data
    pagination.itemCount = response.total
    // 应用筛选
    applyFilters()
  } catch (error) {
    message.error('加载选项数据失败')
    console.error('Load items error:', error)
  } finally {
    loading.value = false
  }
}

// 应用筛选
const applyFilters = () => {
  let filtered = [...items.value]
  
  // 搜索筛选
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(item => 
      item.code.toLowerCase().includes(keyword) ||
      item.value.toLowerCase().includes(keyword) ||
      item.label.toLowerCase().includes(keyword)
    )
  }
  
  // 状态筛选
  if (statusFilter.value !== null) {
    const isActive = statusFilter.value === 'true'
    filtered = filtered.filter(item => item.is_active === isActive)
  }
  
  filteredItems.value = filtered
}

// 搜索处理
const handleSearch = () => {
  applyFilters()
}

// 状态筛选处理
const handleStatusFilter = () => {
  applyFilters()
}

// 分类变化处理
const handleCategoryChange = () => {
  pagination.page = 1
  checkedRowKeys.value = []
  loadItems()
}

// 刷新数据
const refreshData = () => {
  loadItems()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    category_id: selectedCategory.value || '',
    code: '',
    value: '',
    label: '',
    description: '',
    sort_order: 0,
    is_active: true,
    color: '',
    icon: ''
  })
  extraDataText.value = ''
}

const resetEditForm = () => {
  Object.assign(editFormData, {
    id: 0,
    code: '',
    value: '',
    label: '',
    description: '',
    sort_order: 0,
    is_active: true,
    category_id: '',
    color: '',
    icon: ''
  })
  editExtraDataText.value = ''
}

// 提交表单
const handleSubmit = async () => {
  if (!canCreate.value) {
    message.error('您没有权限创建选项')
    return
  }
  
  try {
    await formRef.value?.validate()
    
    // 验证 JSON 格式
    if (extraDataText.value.trim()) {
      try {
        JSON.parse(extraDataText.value)
      } catch (error) {
        message.error('扩展属性必须是有效的JSON格式')
        return
      }
    }
    
    submitting.value = true
    await optionItemsApi.create(formData)
    message.success('添加选项成功')
    showAddModal.value = false
    resetForm()
    loadItems()
  } catch (error) {
    if (error instanceof Error) {
      message.error(error.message || '添加选项失败')
    }
    console.error('Create item error:', error)
  } finally {
    submitting.value = false
  }
}

// 编辑选项
const handleEdit = (row: OptionItem) => {
  if (!canUpdate.value) {
    message.error('您没有权限编辑选项')
    return
  }
  
  Object.assign(editFormData, {
    code: row.code,
    value: row.value,
    label: row.label,
    description: row.description,
    sort_order: row.sort_order,
    is_active: row.is_active,
    color: row.color,
    icon: row.icon
  })
  editFormData.category_id = row.category_id
  editFormData.id = row.id
  editExtraDataText.value = (row.color || row.icon) ? JSON.stringify({ color: row.color, icon: row.icon }, null, 2) : ''
  showEditModal.value = true
}

// 更新选项
const handleUpdate = async () => {
  if (!canUpdate.value) {
    message.error('您没有权限更新选项')
    return
  }
  
  try {
    await editFormRef.value?.validate()
    
    // 验证 JSON 格式
    if (editExtraDataText.value.trim()) {
      try {
        JSON.parse(editExtraDataText.value)
      } catch (error) {
        message.error('扩展属性必须是有效的JSON格式')
        return
      }
    }
    
    submitting.value = true
    await optionItemsApi.update(editFormData.id!, editFormData)
    message.success('更新选项成功')
    showEditModal.value = false
    resetEditForm()
    loadItems()
  } catch (error) {
    if (error instanceof Error) {
      message.error(error.message || '更新选项失败')
    }
    console.error('Update item error:', error)
  } finally {
    submitting.value = false
  }
}

// 删除选项
const handleDelete = (row: OptionItem) => {
  if (!canDelete.value) {
    message.error('您没有权限删除选项')
    return
  }
  
  dialog.warning({
    title: '确认删除',
    content: `确定要删除选项 "${row.label}" 吗？此操作不可恢复。`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await optionItemsApi.delete(row.id)
        message.success('删除选项成功')
        loadItems()
      } catch (error) {
        if (error instanceof Error) {
          message.error(error.message || '删除选项失败')
        }
        console.error('Delete item error:', error)
      }
    }
  })
}

// 批量删除
const handleBatchDelete = () => {
  if (!canDelete.value) {
    message.error('您没有权限删除选项')
    return
  }
  
  if (checkedRowKeys.value.length === 0) {
    message.warning('请选择要删除的选项')
    return
  }

  dialog.warning({
    title: '确认批量删除',
    content: `确定要删除选中的 ${checkedRowKeys.value.length} 个选项吗？此操作不可恢复。`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        const ids = checkedRowKeys.value as string[]
        await optionItemsApi.batchDelete(ids)
        message.success('批量删除成功')
        checkedRowKeys.value = []
        loadItems()
      } catch (error) {
        if (error instanceof Error) {
          message.error(error.message || '批量删除失败')
        }
        console.error('Batch delete error:', error)
      }
    }
  })
}

// 组件挂载时加载数据
onMounted(() => {
  loadCategories()
})

// 监听数据变化，自动应用筛选
watch([items], () => {
  applyFilters()
})
</script>

<style scoped>
.option-items-management {
  padding: 16px 0;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.action-left {
  flex: 1;
}

.action-right {
  flex-shrink: 0;
}

/* 数据表格样式 */
:deep(.n-data-table) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.n-data-table-th) {
  background-color: var(--n-color-target);
  font-weight: 600;
}

/* 按钮样式 */
:deep(.n-button + .n-button) {
  margin-left: 8px;
}

/* 表单样式 */
:deep(.n-form) {
  padding: 20px 0;
}

:deep(.n-form-item) {
  margin-bottom: 20px;
}

/* 模态框样式 */
:deep(.n-modal .n-card) {
  max-width: 700px;
}

/* 选择器样式 */
:deep(.n-select) {
  width: 100%;
}

.status-tag {
  cursor: pointer;
  transition: all 0.2s ease;
}

.status-tag:hover {
  transform: scale(1.05);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .action-right {
    width: 100%;
  }
  
  .action-right .n-space {
    width: 100%;
    justify-content: space-between;
  }
}
</style>