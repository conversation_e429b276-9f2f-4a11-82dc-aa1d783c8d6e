/**
 * 数据验证中间件
 * Data Validation Middleware
 */

import { Request, Response, NextFunction } from 'express';
import { ApiError } from './errorHandler';

// 验证规则接口
interface ValidationRule {
  field: string;
  required?: boolean;
  type?: 'string' | 'number' | 'boolean' | 'email' | 'phone' | 'date' | 'array' | 'object';
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean | string;
  message?: string;
}

// 验证结果接口
interface ValidationResult {
  isValid: boolean;
  errors: Array<{
    field: string;
    message: string;
    value?: any;
  }>;
}

// 验证器类
export class Validator {
  private rules: ValidationRule[];

  constructor(rules: ValidationRule[]) {
    this.rules = rules;
  }

  // 验证数据
  validate(data: any): ValidationResult {
    const errors: Array<{ field: string; message: string; value?: any }> = [];

    for (const rule of this.rules) {
      const value = data[rule.field];
      const error = this.validateField(rule, value);
      if (error) {
        errors.push({
          field: rule.field,
          message: error,
          value
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // 验证单个字段
  private validateField(rule: ValidationRule, value: any): string | null {
    // 必填验证
    if (rule.required && (value === undefined || value === null || value === '')) {
      return rule.message || `${rule.field} 是必填项`;
    }

    // 如果值为空且非必填，跳过其他验证
    if (!rule.required && (value === undefined || value === null || value === '')) {
      return null;
    }

    // 类型验证
    if (rule.type) {
      const typeError = this.validateType(rule, value);
      if (typeError) return typeError;
    }

    // 长度验证
    if (rule.minLength !== undefined || rule.maxLength !== undefined) {
      const lengthError = this.validateLength(rule, value);
      if (lengthError) return lengthError;
    }

    // 数值范围验证
    if (rule.min !== undefined || rule.max !== undefined) {
      const rangeError = this.validateRange(rule, value);
      if (rangeError) return rangeError;
    }

    // 正则表达式验证
    if (rule.pattern) {
      const patternError = this.validatePattern(rule, value);
      if (patternError) return patternError;
    }

    // 自定义验证
    if (rule.custom) {
      const customError = this.validateCustom(rule, value);
      if (customError) return customError;
    }

    return null;
  }

  // 类型验证
  private validateType(rule: ValidationRule, value: any): string | null {
    switch (rule.type) {
      case 'string':
        if (typeof value !== 'string') {
          return rule.message || `${rule.field} 必须是字符串`;
        }
        break;
      case 'number':
        if (typeof value !== 'number' && isNaN(Number(value))) {
          return rule.message || `${rule.field} 必须是数字`;
        }
        break;
      case 'boolean':
        if (typeof value !== 'boolean') {
          return rule.message || `${rule.field} 必须是布尔值`;
        }
        break;

      case 'email':
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailPattern.test(String(value))) {
          return rule.message || `${rule.field} 必须是有效的邮箱地址`;
        }
        break;
      case 'phone':
        const phonePattern = /^1[3-9]\d{9}$/;
        if (!phonePattern.test(String(value))) {
          return rule.message || `${rule.field} 必须是有效的手机号码`;
        }
        break;
      case 'date':
        if (isNaN(Date.parse(value))) {
          return rule.message || `${rule.field} 必须是有效的日期`;
        }
        break;
    }
    return null;
  }

  // 长度验证
  private validateLength(rule: ValidationRule, value: any): string | null {
    let length: number;
    let unit: string;
    
    if (Array.isArray(value)) {
      length = value.length;
      unit = '个元素';
    } else {
      length = String(value).length;
      unit = '个字符';
    }
    
    if (rule.minLength !== undefined && length < rule.minLength) {
      return rule.message || `${rule.field} 长度不能少于 ${rule.minLength} ${unit}`;
    }
    if (rule.maxLength !== undefined && length > rule.maxLength) {
      return rule.message || `${rule.field} 长度不能超过 ${rule.maxLength} ${unit}`;
    }
    return null;
  }

  // 数值范围验证
  private validateRange(rule: ValidationRule, value: any): string | null {
    const numValue = Number(value);
    if (isNaN(numValue)) {
      return rule.message || `${rule.field} 必须是有效数字`;
    }
    if (rule.min !== undefined && numValue < rule.min) {
      return rule.message || `${rule.field} 不能小于 ${rule.min}`;
    }
    if (rule.max !== undefined && numValue > rule.max) {
      return rule.message || `${rule.field} 不能大于 ${rule.max}`;
    }
    return null;
  }

  // 正则表达式验证
  private validatePattern(rule: ValidationRule, value: any): string | null {
    if (!rule.pattern!.test(String(value))) {
      return rule.message || `${rule.field} 格式不正确`;
    }
    return null;
  }

  // 自定义验证
  private validateCustom(rule: ValidationRule, value: any): string | null {
    const result = rule.custom!(value);
    if (typeof result === 'string') {
      return result;
    }
    if (!result) {
      return rule.message || `${rule.field} 验证失败`;
    }
    return null;
  }
}

// 创建验证中间件
export const createValidationMiddleware = (rules: ValidationRule[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const validator = new Validator(rules);
    const result = validator.validate(req.body);

    if (!result.isValid) {
      // 返回第一个具体的错误消息
      const firstError = result.errors[0];
      const error = ApiError.validation(firstError.message, result.errors);
      throw error;
    }

    next();
  };
};

// 查询参数验证中间件
export const createQueryValidationMiddleware = (rules: ValidationRule[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const validator = new Validator(rules);
    const result = validator.validate(req.query);

    if (!result.isValid) {
      const error = ApiError.validation('查询参数验证失败', result.errors);
      throw error;
    }

    next();
  };
};

// 路径参数验证中间件
export const createParamsValidationMiddleware = (rules: ValidationRule[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const validator = new Validator(rules);
    const result = validator.validate(req.params);

    if (!result.isValid) {
      const error = ApiError.validation('路径参数验证失败', result.errors);
      throw error;
    }

    next();
  };
};

// 常用验证规则
export const CommonValidationRules = {
  // ID验证
  id: {
    field: 'id',
    required: true,
    type: 'number' as const,
    min: 1,
    message: 'ID必须是大于0的数字'
  },

  // 分页验证
  pagination: [
    {
      field: 'page',
      type: 'number' as const,
      min: 1,
      message: '页码必须是大于0的数字'
    },
    {
      field: 'pageSize',
      type: 'number' as const,
      min: 1,
      max: 100,
      message: '每页数量必须在1-100之间'
    }
  ],

  // 客户验证规则
  customer: {
    create: [
      {
        field: 'name',
        required: true,
        type: 'string' as const,
        minLength: 1,
        maxLength: 50,
        message: '客户姓名是必填项，长度在1-50个字符之间'
      },
      {
        field: 'phone',
        required: true,
        type: 'phone' as const,
        message: '请输入有效的手机号码'
      },
      {
        field: 'email',
        type: 'email' as const,
        message: '请输入有效的邮箱地址'
      },
      {
        field: 'company',
        type: 'string' as const,
        maxLength: 100,
        message: '公司名称不能超过100个字符'
      }
    ],
    update: [
      {
        field: 'name',
        required: true,
        type: 'string' as const,
        minLength: 1,
        maxLength: 50,
        message: '客户姓名是必填项，长度在1-50个字符之间'
      },
      {
        field: 'phone',
        required: true,
        type: 'phone' as const,
        message: '请输入有效的手机号码'
      },
      {
        field: 'email',
        type: 'email' as const,
        message: '请输入有效的邮箱地址'
      }
    ]
  }
};