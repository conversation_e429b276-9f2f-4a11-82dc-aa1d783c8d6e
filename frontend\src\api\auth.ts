import { request } from './http'
import type { LoginRequest, LoginResponse, User, ApiResponse } from '@/types'

// 认证API接口
export const authApi = {
  // 用户登录
  login: (data: LoginRequest): Promise<ApiResponse<LoginResponse>> => {
    return request.post('/auth/login', data)
  },

  // 用户登出
  logout: (): Promise<ApiResponse<void>> => {
    return request.post('/auth/logout')
  },

  // 获取用户信息
  getUserInfo: (): Promise<ApiResponse<User>> => {
    return request.get('/auth/user')
  },

  // 刷新token
  refreshToken: (): Promise<ApiResponse<{ token: string; expires_in: number }>> => {
    return request.post('/auth/refresh')
  },

  // 修改密码
  changePassword: (data: {
    oldPassword: string
    newPassword: string
    confirmPassword: string
  }): Promise<ApiResponse<void>> => {
    return request.post('/auth/change-password', data)
  },



  // 验证token
  verifyToken: (): Promise<ApiResponse<{ valid: boolean }>> => {
    return request.get('/auth/verify')
  },

  // 获取验证码
  getCaptcha: (): Promise<ApiResponse<{ captcha: string; key: string }>> => {
    return request.get('/auth/captcha')
  },

  // 获取用户权限
  getUserPermissions: (): Promise<ApiResponse<string[]>> => {
    return request.get('/auth/permissions')
  },

  // 获取用户菜单
  getUserMenus: (): Promise<ApiResponse<any[]>> => {
    return request.get('/auth/menus')
  }
}