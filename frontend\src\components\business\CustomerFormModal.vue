<template>
  <n-modal
    v-model:show="visible"
    :mask-closable="false"
    :show-icon="false"
    :closable="false"
    class="customer-form-modal"
  >
    <!-- 按照Figma设计的frame容器 -->
    <div class="frame">
      <!-- 头部区域 rectangle28 -->
      <div class="rectangle28">
        <div class="autoWrapper">
          <div class="ellipse1">
            <svg class="info-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 16V12M12 8H12.01M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z" stroke="#f0f0f0" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </div>
        <p class="text">{{ isEdit ? '编辑客户' : '新增客户' }}</p>
        <div class="close-btn" @click="handleCancel">
          <svg class="close-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 6L6 18M6 6L18 18" stroke="#949494" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>

      <!-- 第一行：客户姓名 + 性别 -->
      <div class="form-row">
        <div class="form-field">
          <label class="field-label">
            <svg class="field-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            客户姓名<span class="required">*</span>
          </label>
          <div class="input-wrapper">
            <input 
              v-model="formData.name" 
              type="text" 
              class="form-input"
              placeholder="请输入客户姓名"
            />
          </div>
        </div>
        <div class="form-field">
          <label class="field-label">
            <svg class="field-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 1V7M12 7C14.2091 7 16 8.79086 16 11C16 13.2091 14.2091 15 12 15C9.79086 15 8 13.2091 8 11C8 8.79086 9.79086 7 12 7ZM12 15V23M8 19H16" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            性别
          </label>
          <div class="select-wrapper">
            <select v-model="formData.gender" class="form-select">
              <option value="">请选择</option>
              <option v-for="option in genderOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </option>
            </select>
            <svg class="select-arrow" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M6 9L12 15L18 9" stroke="#9a9a9a" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </div>
      </div>

      <!-- 第二行：手机号码 + 小区名称 -->
      <div class="form-row">
        <div class="form-field">
          <label class="field-label">
            <svg class="field-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M22 16.92V19.92C22.0011 20.1985 21.9441 20.4742 21.8325 20.7293C21.7209 20.9845 21.5573 21.2136 21.3521 21.4019C21.1468 21.5901 20.9046 21.7335 20.6407 21.8227C20.3769 21.9119 20.0974 21.9451 19.82 21.92C16.7428 21.5856 13.787 20.5341 11.19 18.85C8.77382 17.3147 6.72533 15.2662 5.18999 12.85C3.49997 10.2412 2.44824 7.27099 2.11999 4.18C2.095 3.90347 2.12787 3.62476 2.21649 3.36162C2.30512 3.09849 2.44756 2.85669 2.63476 2.65162C2.82196 2.44655 3.0498 2.28271 3.30379 2.17052C3.55777 2.05833 3.83233 2.00026 4.10999 2H7.10999C7.59531 1.99522 8.06579 2.16708 8.43376 2.48353C8.80173 2.79999 9.04207 3.23945 9.10999 3.72C9.23662 4.68007 9.47144 5.62273 9.80999 6.53C9.94454 6.88792 9.97366 7.27691 9.8939 7.65088C9.81415 8.02485 9.62886 8.36811 9.35999 8.64L8.08999 9.91C9.51355 12.4135 11.5865 14.4864 14.09 15.91L15.36 14.64C15.6319 14.3711 15.9751 14.1858 16.3491 14.1061C16.7231 14.0263 17.1121 14.0555 17.47 14.19C18.3773 14.5286 19.3199 14.7634 20.28 14.89C20.7658 14.9585 21.2094 15.2032 21.5265 15.5775C21.8437 15.9518 22.0122 16.4296 22 16.92Z" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            手机号码<span class="required">*</span>
          </label>
          <div class="input-wrapper">
            <input 
              v-model="formData.phone" 
              type="text" 
              class="form-input"
              placeholder="请输入手机号码"
            />
          </div>
        </div>
        <div class="form-field">
          <label class="field-label">
            <svg class="field-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 10C21 17 12 23 12 23C12 23 3 17 3 10C3 7.61305 3.94821 5.32387 5.63604 3.63604C7.32387 1.94821 9.61305 1 12 1C14.3869 1 16.6761 1.94821 18.3639 3.63604C20.0518 5.32387 21 7.61305 21 10Z" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M12 13C13.6569 13 15 11.6569 15 10C15 8.34315 13.6569 7 12 7C10.3431 7 9 8.34315 9 10C9 11.6569 10.3431 13 12 13Z" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            小区名称<span class="required">*</span>
          </label>
          <div class="input-wrapper">
            <input 
              v-model="formData.community" 
              type="text" 
              class="form-input"
              placeholder="请输入小区名称"
            />
          </div>
        </div>
      </div>

      <!-- 第三行：装修类型 + 房屋面积 -->
      <div class="form-row">
        <div class="form-field">
          <label class="field-label">
            <svg class="field-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 21L12 2L21 21H3Z" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M8 21V13H16V21" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            装修类型<span class="required">*</span>
          </label>
          <div class="select-wrapper">
            <select v-model="formData.decorationType" class="form-select">
              <option value="">请选择</option>
              <option v-for="option in decorationOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </option>
            </select>
            <svg class="select-arrow" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M6 9L12 15L18 9" stroke="#9a9a9a" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </div>
        <div class="form-field">
          <label class="field-label">
            <svg class="field-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M9 22V12H15V22" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            房屋面积
          </label>
          <div class="input-wrapper">
            <input 
              v-model.number="formData.area" 
              type="number" 
              class="form-input"
              placeholder="请输入房屋面积（平方米）"
              min="0"
              step="0.1"
            />
          </div>
        </div>
      </div>

      <!-- 第四行：客户来源 + 客户等级 -->
      <div class="form-row">
        <div class="form-field">
          <label class="field-label">
            <svg class="field-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21M13 7C13 9.20914 11.2091 11 9 11C6.79086 11 5 9.20914 5 7C5 4.79086 6.79086 3 9 3C11.2091 3 13 4.79086 13 7ZM23 21V19C22.9993 18.1137 22.7044 17.2528 22.1614 16.5523C21.6184 15.8519 20.8581 15.3516 20 15.13M16 3.13C16.8604 3.35031 17.623 3.85071 18.1676 4.55232C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89318 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            客户来源<span class="required">*</span>
          </label>
          <div class="select-wrapper">
            <select v-model="formData.source" class="form-select">
              <option value="">请选择</option>
              <option v-for="option in sourceOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </option>
            </select>
            <svg class="select-arrow" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M6 9L12 15L18 9" stroke="#9a9a9a" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </div>
        <div class="form-field">
          <label class="field-label">
            <svg class="field-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            客户等级
          </label>
          <div class="select-wrapper">
            <select v-model="formData.level" class="form-select">
              <option value="">请选择</option>
              <option v-for="option in levelOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </option>
            </select>
            <svg class="select-arrow" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M6 9L12 15L18 9" stroke="#9a9a9a" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </div>
      </div>

      <!-- 第五行：邮箱 + 微信 -->
      <div class="form-row">
        <div class="form-field">
          <label class="field-label">
            <svg class="field-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M22 6L12 13L2 6" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            邮箱
          </label>
          <div class="input-wrapper">
            <input 
              v-model="formData.email" 
              type="email" 
              class="form-input"
              placeholder="请输入邮箱地址"
            />
          </div>
        </div>
        <div class="form-field">
          <label class="field-label">
            <svg class="field-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            微信号
          </label>
          <div class="input-wrapper">
            <input 
              v-model="formData.wechat" 
              type="text" 
              class="form-input"
              placeholder="请输入微信号"
            />
          </div>
        </div>
      </div>

      <!-- 第六行：公司 + 职位 -->
      <div class="form-row">
        <div class="form-field">
          <label class="field-label">
            <svg class="field-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 21H21M5 21V7L13 2L21 7V21M9 9V13M15 9V13" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            公司
          </label>
          <div class="input-wrapper">
            <input 
              v-model="formData.company" 
              type="text" 
              class="form-input"
              placeholder="请输入公司名称"
            />
          </div>
        </div>
        <div class="form-field">
          <label class="field-label">
            <svg class="field-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M16 4H18C18.5304 4 19.0391 4.21071 19.4142 4.58579C19.7893 4.96086 20 5.46957 20 6V20C20 20.5304 19.7893 21.0391 19.4142 21.4142C19.0391 21.7893 18.5304 22 18 22H6C5.46957 22 4.96086 21.7893 4.58579 21.4142C4.21071 21.0391 4 20.5304 4 20V6C4 5.46957 4.21071 4.96086 4.58579 4.58579C4.96086 4.21071 5.46957 4 6 4H8M16 4V2M16 4V6M8 4V2M8 4V6" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            职位
          </label>
          <div class="input-wrapper">
            <input 
              v-model="formData.position" 
              type="text" 
              class="form-input"
              placeholder="请输入职位"
            />
          </div>
        </div>
      </div>

      <!-- 第七行：地址 -->
      <div class="form-row">
        <div class="form-field full-width">
          <label class="field-label">
            <svg class="field-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 10C21 17 12 23 12 23C12 23 3 17 3 10C3 7.61305 3.94821 5.32387 5.63604 3.63604C7.32387 1.94821 9.61305 1 12 1C14.3869 1 16.6761 1.94821 18.3639 3.63604C20.0518 5.32387 21 7.61305 21 10Z" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M12 13C13.6569 13 15 11.6569 15 10C15 8.34315 13.6569 7 12 7C10.3431 7 9 8.34315 9 10C9 11.6569 10.3431 13 12 13Z" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            详细地址
          </label>
          <div class="input-wrapper">
            <input 
              v-model="formData.address" 
              type="text" 
              class="form-input"
              placeholder="请输入详细地址"
            />
          </div>
        </div>
      </div>

      <!-- 第八行：客户状态 + 重点客户 -->
      <div class="form-row">
        <div class="form-field">
          <label class="field-label">
            <svg class="field-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 11H15L22 18H2L9 11Z" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M20 5V6C20 7.10457 19.1046 8 18 8H6C4.89543 8 4 7.10457 4 6V5C4 3.89543 4.89543 3 6 3H18C19.1046 3 20 3.89543 20 5Z" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            客户状态
          </label>
          <div class="select-wrapper">
            <select v-model="formData.status" class="form-select">
               <option value="">请选择</option>
               <option v-for="option in statusOptions" :key="option.value" :value="option.value">
                 {{ option.label }}
               </option>
            </select>
            <svg class="select-arrow" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M6 9L12 15L18 9" stroke="#9a9a9a" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </div>
        <div class="form-field">
          <label class="field-label">
            <svg class="field-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 2.99817C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.5783 8.50903 2.9987 7.05 2.9987C5.59096 2.9987 4.19169 3.5783 3.16 4.61C2.1283 5.6417 1.5487 7.04097 1.5487 8.5C1.5487 9.95903 2.1283 11.3583 3.16 12.39L4.22 13.45L12 21.23L19.78 13.45L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.4518 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39467C21.7563 5.72723 21.351 5.1208 20.84 4.61V4.61Z" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            重点客户
          </label>
          <div class="switch-wrapper">
            <!-- 重点客户开关 - 按照Figma设计精确实现 -->
            <div 
              class="rectangle25" 
              v-if="!formData.isImportant"
              @click="formData.isImportant = true"
            >
              <div class="ellipse3"></div>
            </div>
            <div 
              class="rectangle27" 
              v-if="formData.isImportant"
              @click="formData.isImportant = false"
            >
              <div class="ellipse4"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 第六行：客户标签 -->
      <div class="form-row">
        <div class="form-field full-width">
          <label class="field-label">
            <svg class="field-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M20.59 13.41L13.42 20.58C13.2343 20.766 13.0137 20.9135 12.7709 21.0141C12.5281 21.1148 12.2678 21.1666 12.005 21.1666C11.7422 21.1666 11.4819 21.1148 11.2391 21.0141C10.9963 20.9135 10.7757 20.766 10.59 20.58L2 12V2H12L20.59 10.59C20.9625 10.9647 21.1716 11.4716 21.1716 12C21.1716 12.5284 20.9625 13.0353 20.59 13.41V13.41Z" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M7 7H7.01" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            客户标签
          </label>
          <div class="tags-wrapper">
            <div class="tags-container">
              <div 
                v-for="(tag, index) in formData.tags" 
                :key="index" 
                class="tag-item"
              >
                <span class="tag-text">{{ tag }}</span>
                <svg class="tag-remove" @click="removeTag(index)" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M18 6L6 18M6 6L18 18" stroke="#999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <input 
                v-model="newTag" 
                @keyup.enter="addTag" 
                @blur="addTag"
                @keydown="handleTagKeydown"
                type="text" 
                placeholder="输入标签，支持逗号分隔" 
                class="tag-input"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 第七行：备注信息 -->
      <div class="form-row">
        <div class="form-field full-width">
          <label class="field-label">
            <svg class="field-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M14 2V8H20" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M16 13H8" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M16 17H8" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M10 9H9H8" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            备注信息
          </label>
          <div class="textarea-wrapper">
            <textarea 
              v-model="formData.remark" 
              class="form-textarea"
              rows="3"
              placeholder="请输入备注信息"
            ></textarea>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="form-actions">
        <button class="btn-cancel" @click="handleCancel">
          <svg class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          取消
        </button>
        <button class="btn-submit" @click="handleSubmit">
          <svg class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          {{ isEdit ? '更新' : '创建' }}
        </button>
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import {
  NModal,
  NForm,
  NFormItemGi,
  NGrid,
  NInput,
  NSelect,
  NCascader,
  NDynamicTags,
  NButton,
  useMessage,
  type FormInst,
  type FormRules,
  type CascaderOption
} from 'naive-ui'
import { useCustomerStore } from '@/stores/modules/customer'
import { useCustomerOptionsStore } from '@/stores/customerOptions'
import { CustomerOptionCategory } from '@/constants/customerOptions'
import type { Customer } from '@/api/customerService'

interface CustomerForm {
  id?: number
  name: string
  gender?: string
  phone: string
  community?: string
  decorationType?: string
  area?: number
  source?: string
  level?: 'A' | 'B' | 'C' | 'D'
  houseStatus?: string
  status?: string
  isImportant?: boolean
  tags?: string[]
  remark?: string
  company?: string
  position?: string
  address?: string
  email?: string
  wechat?: string
}

interface Props {
  show: boolean
  customer?: CustomerForm | null
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  customer: null
})

const emit = defineEmits<Emits>()

const message = useMessage()
const customerStore = useCustomerStore()
const customerOptionsStore = useCustomerOptionsStore()

// 响应式数据
const formRef = ref<FormInst>()
const loading = ref(false)

// 表单数据
const formData = ref<CustomerForm>({
  name: '',
  gender: undefined,
  phone: '',
  email: undefined,
  wechat: '',
  company: '',
  position: '',
  address: '',
  community: '',
  decorationType: undefined,
  area: undefined,
  source: undefined,
  level: undefined,
  houseStatus: undefined,
  status: '潜在客户',
  isImportant: false,
  tags: [],
  remark: ''
})

// 新标签输入
const newTag = ref('')

// 计算属性
const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const isEdit = computed(() => !!props.customer?.id)

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入客户姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在2-20个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  community: [
    { required: true, message: '请输入小区名称', trigger: 'blur' }
  ],
  decorationType: [
    { required: true, message: '请选择装修类型', trigger: 'change' }
  ],
  source: [
    { required: true, message: '请选择客户来源', trigger: 'change' }
  ]
}

// 选项数据 - 从customerOptionsStore获取
const genderOptions = computed(() => {
  const items = customerOptionsStore.getCategoryItems(CustomerOptionCategory.GENDER)
  return items.map(item => ({ label: item.name, value: item.value, type: 'option' as const }))
})

const decorationOptions = computed(() => {
  const items = customerOptionsStore.getCategoryItems(CustomerOptionCategory.DECORATION_TYPE)
  return items.map(item => ({ label: item.name, value: item.value, type: 'option' as const }))
})

// 从store获取选项数据
const sourceOptions = computed(() => {
  const items = customerOptionsStore.getCategoryItems(CustomerOptionCategory.SOURCE)
  return items.map(item => ({ label: item.name, value: item.value, type: 'option' as const }))
})

const levelOptions = computed(() => {
  const items = customerOptionsStore.getCategoryItems(CustomerOptionCategory.LEVEL)
  return items.map(item => ({ label: item.name, value: item.value, type: 'option' as const }))
})

const statusOptions = computed(() => {
  const items = customerOptionsStore.getCategoryItems(CustomerOptionCategory.STATUS)
  return items.map(item => ({ label: item.name, value: item.value, type: 'option' as const }))
})

const houseStatusOptions = computed(() => {
  const items = customerOptionsStore.getCategoryItems(CustomerOptionCategory.HOUSE_STATUS)
  return items.map(item => ({ label: item.name, value: item.value, type: 'option' as const }))
})

// 移除地区相关代码，因为Figma设计中没有地区字段

// 添加标签
const addTag = () => {
  const trimmedTag = newTag.value.trim()
  if (!trimmedTag) return
  
  // 支持逗号分隔多个标签
  const tags = trimmedTag.split(',').map(tag => tag.trim()).filter(tag => tag)
  
  if (!formData.value.tags) {
    formData.value.tags = []
  }
  
  tags.forEach(tag => {
    // 检查标签长度限制（1-10个字符）
    if (tag.length > 10) {
      message.warning(`标签"${tag}"长度不能超过10个字符`)
      return
    }
    
    // 检查是否重复
    if (formData.value.tags!.includes(tag)) {
      message.warning(`标签"${tag}"已存在`)
      return
    }
    
    // 检查标签数量限制（最多10个）
    if (formData.value.tags!.length >= 10) {
      message.warning('最多只能添加10个标签')
      return
    }
    
    formData.value.tags!.push(tag)
  })
  
  newTag.value = ''
}

// 删除标签
const removeTag = (index: number) => {
  if (formData.value.tags && index >= 0 && index < formData.value.tags.length) {
    formData.value.tags.splice(index, 1)
  }
}

// 处理标签输入的键盘事件
const handleTagKeydown = (event: KeyboardEvent) => {
  // 支持逗号、分号、空格作为分隔符
  if (event.key === ',' || event.key === ';' || event.key === ' ') {
    event.preventDefault()
    addTag()
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    name: '',
    gender: undefined,
    phone: '',
    email: '',
    wechat: '',
    company: '',
    position: '',
    address: '',
    community: '',
    decorationType: undefined,
    area: undefined,
    source: undefined,
    level: undefined,
    houseStatus: undefined,
    status: '潜在客户',
    isImportant: false,
    tags: [],
    remark: ''
  }
  newTag.value = ''
}

// 处理取消
const handleCancel = () => {
  visible.value = false
  resetForm()
}

// 处理提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true
    
    // 准备提交数据 - 确保字段名与后端数据库表字段匹配
    const submitData = {
      name: formData.value.name,
      phone: formData.value.phone,
      email: formData.value.email || undefined,
      company: formData.value.company || undefined,
      position: formData.value.position || undefined,
      region: formData.value.community || undefined, // 前端community映射到后端region
      gender: formData.value.gender || undefined,
      age: undefined, // 暂时设为undefined，后续可以根据需要计算
      source: formData.value.source || 'other',
      level: formData.value.level || 'C',
      address: formData.value.address || undefined,
      decoration_type: formData.value.decorationType || undefined, // 驼峰转下划线
      house_status: formData.value.houseStatus || undefined, // 驼峰转下划线
      budget_range: undefined, // 暂时设为undefined
      contact_time: undefined, // 暂时设为undefined
      tags: formData.value.tags ? JSON.stringify(formData.value.tags) : undefined, // 转换为JSON字符串
      notes: formData.value.remark || undefined, // 前端remark映射到后端notes
      assigned_to: undefined,
      status: formData.value.status || 'active'
    }
    
    console.log('提交数据:', submitData)
    
    let result
    if (isEdit.value) {
      // 更新客户
      result = await customerStore.updateCustomer(props.customer!.id!, submitData)
    } else {
      // 创建客户
      result = await customerStore.createCustomer(submitData)
    }
    
    if (result.success) {
      visible.value = false
      resetForm()
      emit('success')
    }
  } catch (error) {
    console.error('提交失败:', error)
    message.error('操作失败，请重试')
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载选项数据
onMounted(async () => {
  try {
    await customerOptionsStore.loadAllCustomerOptions()
  } catch (error) {
    console.error('加载选项数据失败:', error)
  }
})

// 监听客户数据变化
watch(
  () => props.customer,
  (customer) => {
    if (customer) {
      formData.value = {
        name: customer.name || '',
        gender: customer.gender || undefined,
        phone: customer.phone || '',
        email: customer.email || undefined,
        wechat: customer.wechat || '',
        company: customer.company || '',
        position: customer.position || '',
        address: customer.address || '',
        community: customer.community || '',
        decorationType: customer.decorationType || undefined,
        area: customer.area || undefined,
        source: customer.source || undefined,
        level: customer.level || undefined,
        houseStatus: customer.houseStatus || undefined,
        status: customer.status || '潜在客户',
        isImportant: customer.isImportant || false,
        tags: customer.tags || [],
        remark: customer.remark || ''
      }
    } else {
      resetForm()
    }
  },
  { immediate: true }
)
</script>

<style scoped>
* {
  box-sizing: border-box;
}

.customer-form-modal {
  --n-color: transparent;
  --n-text-color: transparent;
  --n-box-shadow: none;
  z-index: 1000;
}

/* 按照Figma设计的frame容器样式 */
.frame {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 12px;
  background: #ffffff;
  width: 800px;
  height: auto;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  padding: 0;
}

/* 头部区域 rectangle28 */
.rectangle28 {
  display: flex;
  align-items: center;
  align-self: stretch;
  background: linear-gradient(135deg, #1677ff 0%, #4096ff 100%);
  padding: 0 24px;
  height: 60px;
  flex-shrink: 0;
  justify-content: space-between;
}

.autoWrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ellipse1 {
  width: 28px;
  height: 28px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.info-icon {
  width: 16px;
  height: 16px;
}

.text {
  color: #ffffff;
  font-family: 'PingFang SC', sans-serif;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.close-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.1);
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.close-icon {
  width: 18px;
  height: 18px;
}

/* 表单行样式 - 优化对齐和间距 */
.form-row {
  display: flex;
  gap: 24px;
  padding: 0 28px;
  margin-bottom: 20px;
  align-items: flex-end;
  flex-shrink: 0;
  min-height: 72px;
}

.form-row:first-of-type {
  margin-top: 20px;
}

.form-row:last-of-type {
  margin-bottom: 20px;
}

/* 表单字段样式 - 优化对齐 */
.form-field {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 0;
}

.form-field.full-width {
  flex: 1 1 100%;
  width: 100%;
}

/* 标签样式 - 优化对齐 */
.field-label {
  display: flex;
  align-items: center;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin: 0;
  line-height: 1.5;
  gap: 6px;
  height: 22px;
  margin-bottom: 2px;
}

.field-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.required {
  color: #ff4d4f;
  margin-left: 2px;
}

/* 输入框包装器 */
.input-wrapper {
  position: relative;
}

/* 输入框样式 - 统一高度和对齐 */
.form-input {
  width: 100%;
  height: 42px;
  padding: 0 12px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  font-size: 14px;
  font-family: 'PingFang SC', sans-serif;
  background: #ffffff;
  transition: all 0.2s ease;
  box-sizing: border-box;
  line-height: 1.5;
}

.form-input:focus {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
  outline: none;
}

.form-input::placeholder {
  color: #bfbfbf;
}

/* 下拉框包装器 */
.select-wrapper {
  position: relative;
}

/* 下拉框样式 - 统一高度和对齐 */
.form-select {
  width: 100%;
  height: 42px;
  padding: 0 32px 0 12px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  font-size: 14px;
  font-family: 'PingFang SC', sans-serif;
  background: #ffffff;
  transition: all 0.2s ease;
  box-sizing: border-box;
  appearance: none;
  cursor: pointer;
  line-height: 1.5;
}

.form-select:focus {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
  outline: none;
}

/* 下拉箭头 */
.select-arrow {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  pointer-events: none;
  transition: transform 0.2s ease;
}

.select-wrapper:hover .select-arrow {
  transform: translateY(-50%) scale(1.1);
}

/* 开关样式 - 优化对齐 */
.switch-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
  height: 42px;
}

/* 重点客户开关 - 未激活状态 */
.rectangle25 {
  position: relative;
  width: 44px;
  height: 24px;
  background: #d9d9d9;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  padding: 2px;
}

.rectangle25:hover {
  background: #bfbfbf;
}

.ellipse3 {
  width: 20px;
  height: 20px;
  background: #ffffff;
  border-radius: 50%;
  transition: transform 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 重点客户开关 - 激活状态 */
.rectangle27 {
  position: relative;
  width: 44px;
  height: 24px;
  background: #1677ff;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  padding: 2px;
  justify-content: flex-end;
}

.rectangle27:hover {
  background: #0958d9;
}

.ellipse4 {
  width: 20px;
  height: 20px;
  background: #ffffff;
  border-radius: 50%;
  transition: transform 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.custom-switch {
  position: relative;
  width: 44px;
  height: 24px;
  background: #d9d9d9;
  border-radius: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.custom-switch.active {
  background: #1677ff;
}

.switch-handle {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: #ffffff;
  border-radius: 50%;
  transition: transform 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.custom-switch.active .switch-handle {
  transform: translateX(20px);
}

.switch-text {
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #595959;
}

/* 标签容器样式 - 统一高度 */
.tags-wrapper {
  position: relative;
}

.tags-container {
  min-height: 42px;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  background: #ffffff;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  transition: all 0.2s ease;
}

.tags-container:focus-within {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #f0f8ff;
  border: 1px solid #1677ff;
  border-radius: 16px;
  font-size: 12px;
  color: #1677ff;
}

.tag-text {
  font-family: 'PingFang SC', sans-serif;
}

.tag-remove {
  width: 14px;
  height: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.tag-remove:hover {
  background: rgba(22, 119, 255, 0.1);
}

.tag-input {
  flex: 1;
  min-width: 100px;
  border: none;
  outline: none;
  font-size: 14px;
  font-family: 'PingFang SC', sans-serif;
  background: transparent;
}

.tag-input::placeholder {
  color: #bfbfbf;
}

/* 文本域包装器 */
.textarea-wrapper {
  position: relative;
}

/* 文本域样式 - 优化高度和对齐 */
.form-textarea {
  width: 100%;
  min-height: 84px;
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  font-size: 14px;
  font-family: 'PingFang SC', sans-serif;
  background: #ffffff;
  transition: all 0.2s ease;
  box-sizing: border-box;
  resize: vertical;
  line-height: 1.5;
}

.form-textarea:focus {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
  outline: none;
}

.form-textarea::placeholder {
  color: #bfbfbf;
}

/* 按钮区域样式 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 16px 28px 20px;
  border-top: 1px solid #f0f0f0;
  margin-top: auto;
  flex-shrink: 0;
  background: #ffffff;
}

/* 按钮基础样式 */
.btn-cancel,
.btn-submit {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  font-family: 'PingFang SC', sans-serif;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  min-width: 100px;
  height: 40px;
}

/* 取消按钮样式 */
.btn-cancel {
  background: #ffffff;
  color: #595959;
  border: 1px solid #d9d9d9;
}

.btn-cancel:hover {
  background: #f5f5f5;
  border-color: #bfbfbf;
  color: #262626;
}

.btn-cancel:active {
  background: #e6e6e6;
  transform: translateY(1px);
}

/* 提交按钮样式 - 渐变效果 */
.btn-submit {
  background: linear-gradient(135deg, #1677ff 0%, #4096ff 50%, #69b1ff 100%);
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.3);
}

.btn-submit:hover {
  background: linear-gradient(135deg, #0958d9 0%, #1677ff 50%, #4096ff 100%);
  box-shadow: 0 4px 12px rgba(22, 119, 255, 0.4);
  transform: translateY(-1px);
}

.btn-submit:active {
  background: linear-gradient(135deg, #003eb3 0%, #0958d9 50%, #1677ff 100%);
  box-shadow: 0 2px 6px rgba(22, 119, 255, 0.5);
  transform: translateY(0);
}

/* 按钮图标样式 */
.btn-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 16px;
  }
  
  .form-field {
    width: 100%;
  }
  
  .form-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .btn-cancel,
  .btn-submit {
    width: 100%;
  }
}






</style>