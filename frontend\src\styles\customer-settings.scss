// 客户设置页面样式优化
// 主要包含：标签页美化、卡片布局、响应式设计、动画效果

// 变量定义
$primary-color: #1677ff;
$primary-gradient: linear-gradient(135deg, #1677ff 0%, #40a9ff 100%);
$success-gradient: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
$error-gradient: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
$warning-gradient: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
$border-radius: 8px;
$box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
$transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

// 客户设置页面主容器
.customer-settings {
  padding: 20px;
  min-height: calc(100vh - 120px);
  background: var(--n-body-color);
  
  // 页面头部
  .page-header {
    margin-bottom: 24px;
    padding: 20px 0;
    
    .page-title {
      margin: 0;
      font-size: 28px;
      font-weight: 700;
      color: var(--n-text-color);
      background: $primary-gradient;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      letter-spacing: -0.5px;
    }
    
    .page-description {
      margin: 8px 0 0 0;
      color: var(--n-text-color-2);
      font-size: 16px;
      font-weight: 400;
      line-height: 1.5;
    }
  }
  
  // 设置内容区域
  .settings-content {
    background: var(--n-card-color);
    border-radius: $border-radius;
    padding: 0;
    box-shadow: $box-shadow;
    overflow: hidden;
    
    // 标签页容器
    .n-tabs {
      // 标签页导航
      .n-tabs-nav {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid var(--n-border-color);
        padding: 0 24px;
        
        .n-tabs-nav-scroll-content {
          .n-tabs-tab-wrapper {
            .n-tabs-tab {
              font-weight: 600;
              padding: 16px 24px;
              margin-right: 4px;
              border-radius: 8px 8px 0 0;
              transition: $transition;
              position: relative;
              overflow: hidden;
              
              &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: transparent;
                transition: $transition;
                z-index: -1;
              }
              
              &:hover {
                &::before {
                  background: rgba(22, 119, 255, 0.1);
                }
                transform: translateY(-2px);
              }
              
              &.n-tabs-tab--active {
                background: $primary-gradient;
                color: white;
                font-weight: 700;
                box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
                
                &::before {
                  background: rgba(255, 255, 255, 0.1);
                }
              }
            }
          }
        }
      }
      
      // 标签页内容
      .n-tabs-pane {
        background: var(--n-card-color);
        min-height: 500px;
        
        .tab-content {
          padding: 32px 24px;
          
          // 基础设置表单样式
          .settings-form {
            max-width: 900px;
            
            .n-form-item {
              margin-bottom: 24px;
              
              .n-form-item-label {
                font-weight: 600;
                color: var(--n-text-color);
              }
            }
            
            // 网格布局优化
            .n-grid {
              .n-form-item-gi {
                .n-input,
                .n-select {
                  border-radius: 6px;
                  transition: $transition;
                  
                  &:hover {
                    box-shadow: 0 2px 8px rgba(22, 119, 255, 0.15);
                  }
                  
                  &:focus-within {
                    box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.2);
                  }
                }
                
                .n-switch {
                  .n-switch__rail {
                    background: $primary-gradient;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

// 按钮样式优化
.customer-settings {
  .n-button {
    border-radius: 6px;
    font-weight: 600;
    transition: $transition;
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }
    
    &:hover::before {
      left: 100%;
    }
    
    // 主要按钮
    &.n-button--primary-type {
      background: $primary-gradient;
      border: none;
      box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
      
      &:hover {
        background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(22, 119, 255, 0.4);
      }
      
      &:active {
        transform: translateY(0);
      }
    }
    
    // 信息按钮
    &.n-button--info-type {
      background: $success-gradient;
      border: none;
      color: white;
      box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
      
      &:hover {
        background: linear-gradient(135deg, #73d13d 0%, #95de64 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(82, 196, 26, 0.4);
      }
    }
    
    // 错误按钮
    &.n-button--error-type {
      background: $error-gradient;
      border: none;
      color: white;
      box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
      
      &:hover {
        background: linear-gradient(135deg, #ff7875 0%, #ffa39e 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(255, 77, 79, 0.4);
      }
    }
    
    // 警告按钮
    &.n-button--warning-type {
      background: $warning-gradient;
      border: none;
      color: white;
      box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
      
      &:hover {
        background: linear-gradient(135deg, #ffc53d 0%, #ffd666 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(250, 173, 20, 0.4);
      }
    }
    
    // 默认按钮
    &.n-button--default-type {
      background: var(--n-button-color);
      border: 1px solid var(--n-border-color);
      
      &:hover {
        background: var(--n-button-color-hover);
        border-color: var(--n-border-color-hover);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }
    }
    
    // 小尺寸按钮
    &.n-button--small-type {
      padding: 6px 16px;
      font-size: 13px;
      font-weight: 500;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .customer-settings {
    padding: 16px;
    
    .page-header {
      .page-title {
        font-size: 24px;
      }
      
      .page-description {
        font-size: 14px;
      }
    }
    
    .settings-content {
      .n-tabs {
        .n-tabs-nav {
          padding: 0 16px;
          
          .n-tabs-tab {
            padding: 12px 16px;
            font-size: 14px;
          }
        }
        
        .n-tabs-pane {
          .tab-content {
            padding: 20px 16px;
            
            .settings-form {
              .n-grid {
                grid-template-columns: 1fr !important;
              }
            }
          }
        }
      }
    }
  }
}

// 暗色主题适配
@media (prefers-color-scheme: dark) {
  .customer-settings {
    .page-header {
      .page-title {
        background: linear-gradient(135deg, #4dabf7 0%, #74c0fc 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }
    
    .settings-content {
      .n-tabs {
        .n-tabs-nav {
          background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        }
      }
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.customer-settings {
  animation: fadeInUp 0.6s ease-out;
  
  .settings-content {
    animation: slideInRight 0.8s ease-out 0.2s both;
  }
  
  .tab-content {
    animation: fadeInUp 0.4s ease-out;
  }
}