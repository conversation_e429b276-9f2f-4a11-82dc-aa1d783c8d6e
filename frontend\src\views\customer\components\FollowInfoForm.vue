<template>
  <div class="follow-info-form">
    <!-- 表单头部信息 -->
    <div class="form-header">
      <div class="header-info">
        <n-icon size="18" color="#1677ff">
          <people-outline />
        </n-icon>
        <span class="header-title">{{ isEdit ? '编辑跟进信息' : '新增跟进信息' }}</span>
      </div>
      <div class="header-tips">
        <n-alert type="info" :show-icon="false" size="small">
          记录客户跟进的详细信息，包括访问记录、设计师安排和签单进展
        </n-alert>
      </div>
    </div>

    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="100px"
      require-mark-placement="right-hanging"
      size="medium"
    >
      <!-- 访问记录区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#1677ff">
            <storefront-outline />
          </n-icon>
          <span class="section-title">访问记录</span>
        </div>
        
        <n-grid :cols="2" :x-gap="20" :y-gap="16" responsive="screen">
          <n-form-item-gi label="几访进店" path="visitCount">
            <n-input-number
              v-model:value="formData.visitCount"
              placeholder="请输入访问次数"
              :min="0"
              :max="99"
              :step="1"
              style="width: 100%"
              :show-button="false"
            >
              <template #prefix>
                <n-icon size="16" color="#1677ff">
                  <footsteps-outline />
                </n-icon>
              </template>
              <template #suffix>次</template>
            </n-input-number>
          </n-form-item-gi>
          
          <n-form-item-gi label="是否到店" path="hasVisitedStore">
            <div class="switch-container">
              <n-switch
                v-model:value="formData.hasVisitedStore"
                :checked-value="true"
                :unchecked-value="false"
                size="medium"
              >
                <template #checked>
                  <n-icon size="14"><checkmark-outline /></n-icon>
                </template>
                <template #unchecked>
                  <n-icon size="14"><close-outline /></n-icon>
                </template>
              </n-switch>
              <span class="switch-text">
                {{ formData.hasVisitedStore ? '已到店' : '未到店' }}
              </span>
            </div>
          </n-form-item-gi>
          
          <n-form-item-gi label="到店时间" path="storeVisitTime">
            <n-date-picker
              v-model:value="formData.storeVisitTime"
              type="datetime"
              placeholder="请选择到店时间"
              style="width: 100%"
              :disabled="!formData.hasVisitedStore"
              clearable
              :input-readonly="false"
            >
              <template #date-icon>
                <n-icon size="16" color="#52c41a">
                  <time-outline />
                </n-icon>
              </template>
            </n-date-picker>
          </n-form-item-gi>
        </n-grid>
      </div>

      <!-- 设计团队区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#52c41a">
            <brush-outline />
          </n-icon>
          <span class="section-title">设计团队</span>
        </div>
        
        <n-grid :cols="2" :x-gap="20" :y-gap="16" responsive="screen">
          <n-form-item-gi label="设计师" path="designer">
            <n-input
              v-model:value="formData.designer"
              placeholder="请输入设计师姓名"
              clearable
              :input-props="{ spellcheck: false }"
            >
              <template #prefix>
                <n-icon size="16" color="#52c41a">
                  <person-outline />
                </n-icon>
              </template>
            </n-input>
          </n-form-item-gi>
          
          <n-form-item-gi label="设计师总监" path="designDirector">
            <n-input
              v-model:value="formData.designDirector"
              placeholder="请输入设计师总监姓名"
              clearable
              :input-props="{ spellcheck: false }"
            >
              <template #prefix>
                <n-icon size="16" color="#722ed1">
                  <star-outline />
                </n-icon>
              </template>
            </n-input>
          </n-form-item-gi>
          
          <n-form-item-gi label="设计部门" path="designDepartment">
            <n-input
              v-model:value="formData.designDepartment"
              placeholder="请输入设计部门"
              clearable
              :input-props="{ spellcheck: false }"
            >
              <template #prefix>
                <n-icon size="16" color="#fa8c16">
                  <business-outline />
                </n-icon>
              </template>
            </n-input>
          </n-form-item-gi>
        </n-grid>
      </div>

      <!-- 项目进展区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#722ed1">
            <calendar-outline />
          </n-icon>
          <span class="section-title">项目进展</span>
        </div>
        
        <n-grid :cols="2" :x-gap="20" :y-gap="16" responsive="screen">
          <n-form-item-gi label="量房时间" path="measureTime">
            <n-date-picker
              v-model:value="formData.measureTime"
              type="datetime"
              placeholder="请选择量房时间"
              style="width: 100%"
              clearable
              :input-readonly="false"
            >
              <template #date-icon>
                <n-icon size="16" color="#722ed1">
                  <resize-outline />
                </n-icon>
              </template>
            </n-date-picker>
          </n-form-item-gi>
          
          <n-form-item-gi label="签单时间" path="contractTime">
            <n-date-picker
              v-model:value="formData.contractTime"
              type="datetime"
              placeholder="请选择签单时间"
              style="width: 100%"
              clearable
              :input-readonly="false"
            >
              <template #date-icon>
                <n-icon size="16" color="#fa541c">
                  <document-text-outline />
                </n-icon>
              </template>
            </n-date-picker>
          </n-form-item-gi>
          
          <n-form-item-gi label="签单套餐" path="contractPackage">
            <n-select
              v-model:value="formData.contractPackage"
              :options="contractPackageOptions"
              placeholder="请选择签单套餐"
              clearable
              filterable
            >
              <template #arrow>
                <n-icon size="16" color="#8c8c8c">
                  <chevron-down-outline />
                </n-icon>
              </template>
            </n-select>
          </n-form-item-gi>
        </n-grid>
      </div>

      <!-- 交易凭证区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#13c2c2">
            <image-outline />
          </n-icon>
          <span class="section-title">交易凭证</span>
        </div>
        
        <n-form-item label="交易截图" path="transactionScreenshot">
          <div class="upload-container">
            <n-upload
              v-model:file-list="screenshotFileList"
              :max="1"
              list-type="image-card"
              accept="image/*"
              @update:file-list="handleScreenshotChange"
              :show-preview-button="true"
              :show-remove-button="true"
            >
              <div class="upload-trigger">
                <n-icon size="24" color="#1677ff">
                  <cloud-upload-outline />
                </n-icon>
                <div class="upload-text">
                  <div class="upload-title">点击上传交易截图</div>
                  <div class="upload-hint">支持 JPG、PNG 格式，文件大小不超过 5MB</div>
                </div>
              </div>
            </n-upload>
          </div>
        </n-form-item>
      </div>

      <!-- 跟进内容区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#fa8c16">
            <chatbubble-ellipses-outline />
          </n-icon>
          <span class="section-title">跟进内容</span>
        </div>
        
        <n-form-item label="跟进详情" path="followContent">
          <n-input
            v-model:value="formData.followContent"
            type="textarea"
            placeholder="请详细描述跟进过程、客户反馈、遇到的问题以及下一步计划..."
            :rows="4"
            clearable
            :maxlength="1000"
            show-count
            :input-props="{ spellcheck: false }"
          />
        </n-form-item>
      </div>

      <!-- 快速操作区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#fa8c16">
            <flash-outline />
          </n-icon>
          <span class="section-title">快速操作</span>
        </div>
        
        <div class="quick-actions">
          <n-space>
            <n-button size="small" @click="fillQuickTemplate('first_visit')">
              <template #icon>
                <n-icon><storefront-outline /></n-icon>
              </template>
              首次到店
            </n-button>
            <n-button size="small" @click="fillQuickTemplate('measure')">
              <template #icon>
                <n-icon><resize-outline /></n-icon>
              </template>
              量房安排
            </n-button>
            <n-button size="small" @click="fillQuickTemplate('contract')">
              <template #icon>
                <n-icon><document-text-outline /></n-icon>
              </template>
              签单成功
            </n-button>
            <n-button size="small" @click="autoFillDesigner">
              <template #icon>
                <n-icon><person-add-outline /></n-icon>
              </template>
              分配设计师
            </n-button>
          </n-space>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <n-space>
          <n-button @click="handleReset" size="medium">
            <template #icon>
              <n-icon><refresh-outline /></n-icon>
            </template>
            重置
          </n-button>
          <n-button type="primary" @click="handleSubmit" :loading="loading" size="medium">
            <template #icon>
              <n-icon><save-outline /></n-icon>
            </template>
            {{ isEdit ? '更新' : '保存' }}
          </n-button>
        </n-space>
      </div>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, onMounted } from 'vue'
import {
  NForm,
  NFormItem,
  NFormItemGi,
  NGrid,
  NInput,
  NInputNumber,
  NSelect,
  NSwitch,
  NDatePicker,
  NUpload,
  NButton,
  NSpace,
  NIcon,
  NAlert,
  useMessage,
  type FormInst,
  type FormRules,
  type UploadFileInfo
} from 'naive-ui'
import { useCustomerOptionsStore, CustomerOptionCategory } from '@/stores/customerOptions'
import {
  PeopleOutline,
  StorefrontOutline,
  FootstepsOutline,
  CheckmarkOutline,
  CloseOutline,
  TimeOutline,
  BrushOutline,
  PersonOutline,
  StarOutline,
  BusinessOutline,
  CalendarOutline,
  ResizeOutline,
  DocumentTextOutline,
  ChevronDownOutline,
  ImageOutline,
  CloudUploadOutline,
  ChatbubbleEllipsesOutline,
  FlashOutline,
  PersonAddOutline,
  RefreshOutline,
  SaveOutline
} from '@vicons/ionicons5'

// 定义接口
interface CustomerFollowInfo {
  id?: string
  customerId: string
  visitCount: number
  designer: string
  designDirector: string
  designDepartment: string
  hasVisitedStore: boolean
  storeVisitTime: number | null
  measureTime: number | null
  followContent: string
  contractTime: number | null
  contractPackage: string
  transactionScreenshot: string
  createdAt: string
}

// Props定义
interface Props {
  modelValue?: CustomerFollowInfo
  isEdit?: boolean
  loading?: boolean
}

// Emits定义
interface Emits {
  (e: 'update:modelValue', value: CustomerFollowInfo): void
  (e: 'submit', value: CustomerFollowInfo): void
  (e: 'reset'): void
}

const props = withDefaults(defineProps<Props>(), {
  isEdit: false,
  loading: false
})

const emit = defineEmits<Emits>()

// 响应式数据
const message = useMessage()
const formRef = ref<FormInst>()
const customerOptionsStore = useCustomerOptionsStore()

// 表单数据
const formData = reactive<CustomerFollowInfo>({
  customerId: '',
  visitCount: 0,
  designer: '',
  designDirector: '',
  designDepartment: '',
  hasVisitedStore: false,
  storeVisitTime: null,
  measureTime: null,
  followContent: '',
  contractTime: null,
  contractPackage: '',
  transactionScreenshot: '',
  createdAt: new Date().toISOString()
})

// 文件上传列表
const screenshotFileList = ref<UploadFileInfo[]>([])

// 签单套餐选项
const contractPackageOptions = computed(() => 
  customerOptionsStore.toSelectOptions(CustomerOptionCategory.PACKAGE_TYPE)
)

// 快速模板填充
const fillQuickTemplate = (type: string) => {
  const now = new Date()
  
  const templates = {
    first_visit: {
      visitCount: 1,
      hasVisitedStore: true,
      storeVisitTime: now.getTime(),
      followContent: '客户首次到店参观，对我们的设计风格和服务态度表示满意，已初步了解客户需求。'
    },
    measure: {
      measureTime: now.getTime(),
      followContent: '已安排设计师上门量房，客户配合度较高，现场沟通顺畅，预计3个工作日内出具设计方案。'
    },
    contract: {
      contractTime: now.getTime(),
      followContent: '客户对设计方案非常满意，已成功签单，感谢客户的信任与支持。'
    }
  }
  
  const template = templates[type as keyof typeof templates]
  if (template) {
    Object.assign(formData, template)
    message.success(`已应用${type === 'first_visit' ? '首次到店' : type === 'measure' ? '量房安排' : '签单成功'}模板`)
  }
}

// 自动分配设计师
const autoFillDesigner = () => {
  const designers = ['张设计师', '李设计师', '王设计师', '刘设计师']
  const directors = ['陈总监', '赵总监', '孙总监']
  const departments = ['现代风格设计部', '欧式风格设计部', '中式风格设计部']
  
  formData.designer = designers[Math.floor(Math.random() * designers.length)]
  formData.designDirector = directors[Math.floor(Math.random() * directors.length)]
  formData.designDepartment = departments[Math.floor(Math.random() * departments.length)]
  
  message.success('已自动分配设计师团队')
}

// 表单验证规则
const rules: FormRules = {
  visitCount: {
    required: true,
    type: 'number',
    min: 0,
    max: 99,
    message: '请输入正确的访问次数（0-99次）',
    trigger: ['blur', 'change']
  },
  designer: {
    required: true,
    message: '请输入设计师姓名',
    trigger: ['blur', 'input']
  },
  designDepartment: {
    required: true,
    message: '请输入设计部门',
    trigger: ['blur', 'input']
  },
  followContent: {
    required: true,
    message: '请输入跟进内容',
    trigger: ['blur', 'input']
  },
  storeVisitTime: [
    {
      validator: (rule, value) => {
        if (formData.hasVisitedStore && !value) {
          return new Error('已到店时必须填写到店时间')
        }
        return true
      },
      trigger: ['blur', 'change']
    }
  ]
}

// 监听props变化，更新表单数据
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      Object.assign(formData, newValue)
      
      // 更新文件列表
      if (newValue.transactionScreenshot) {
        screenshotFileList.value = [{
          id: 'screenshot',
          name: '交易截图',
          status: 'finished',
          url: newValue.transactionScreenshot
        }]
      }
    }
  },
  { immediate: true, deep: true }
)

// 监听表单数据变化，向上传递
watch(
  formData,
  (newValue) => {
    emit('update:modelValue', { ...newValue })
  },
  { deep: true }
)

// 文件上传处理
const handleScreenshotChange = (fileList: UploadFileInfo[]) => {
  screenshotFileList.value = fileList
  
  if (fileList.length > 0 && fileList[0].url) {
    formData.transactionScreenshot = fileList[0].url
  } else {
    formData.transactionScreenshot = ''
  }
}

// 事件处理函数
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    emit('submit', { ...formData })
  } catch (error) {
    message.error('请检查表单填写是否正确')
  }
}

const handleReset = () => {
  formRef.value?.restoreValidation()
  
  // 重置为初始值
  Object.assign(formData, {
    customerId: '',
    visitCount: 0,
    designer: '',
    designDirector: '',
    designDepartment: '',
    hasVisitedStore: false,
    storeVisitTime: null,
    measureTime: null,
    followContent: '',
    contractTime: null,
    contractPackage: '',
    transactionScreenshot: '',
    createdAt: new Date().toISOString()
  })
  
  // 重置文件列表
  screenshotFileList.value = []
  
  emit('reset')
}

// 暴露验证方法
const validate = async () => {
  return await formRef.value?.validate()
}

// 暴露重置方法
const resetForm = () => {
  handleReset()
}

// 组件挂载时加载选项数据
onMounted(async () => {
  await customerOptionsStore.loadAllCustomerOptions()
})

defineExpose({
  validate,
  resetForm
})
</script>

<style scoped>
.follow-info-form {
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
}

.form-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.header-tips {
  margin-top: 8px;
}

.form-section {
  margin-bottom: 32px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8e8e8;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.switch-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.switch-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.upload-container {
  width: 100%;
}

.upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-trigger:hover {
  border-color: #1677ff;
  background: #f0f8ff;
}

.upload-text {
  margin-top: 12px;
  text-align: center;
}

.upload-title {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 4px;
}

.upload-hint {
  font-size: 12px;
  color: #8c8c8c;
}

.quick-actions {
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.form-actions {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

/* 表单样式优化 */
:deep(.n-form-item-label) {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

:deep(.n-input) {
  border-radius: 6px;
}

:deep(.n-select) {
  border-radius: 6px;
}

:deep(.n-input-number) {
  border-radius: 6px;
}

:deep(.n-date-picker) {
  border-radius: 6px;
}

/* 开关样式 */
:deep(.n-switch) {
  --n-rail-color-active: #1677ff;
  --n-rail-height: 20px;
  --n-button-width: 18px;
  --n-button-height: 18px;
}

:deep(.n-switch .n-switch__button) {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 上传组件样式 */
:deep(.n-upload) {
  width: 100%;
}

:deep(.n-upload-file-list) {
  margin-top: 8px;
}

:deep(.n-upload-file-list .n-upload-file) {
  border-radius: 8px;
}

:deep(.n-upload-file-list .n-upload-file-info) {
  padding: 8px 12px;
}

/* 文本域样式 */
:deep(.n-input--textarea) {
  border-radius: 6px;
}

:deep(.n-input--textarea .n-input__textarea) {
  line-height: 1.6;
}

/* 必填项标记样式 */
:deep(.n-form-item--required .n-form-item-label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
  font-weight: bold;
}

/* 输入框聚焦效果 */
:deep(.n-input:focus-within) {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

:deep(.n-input-number:focus-within) {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

:deep(.n-date-picker:focus-within) {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

:deep(.n-select:focus-within) {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

/* 禁用状态样式 */
:deep(.n-date-picker--disabled) {
  opacity: 0.6;
  cursor: not-allowed;
}

:deep(.n-date-picker--disabled .n-input) {
  background-color: #f5f5f5;
}

/* 输入框前缀图标样式 */
:deep(.n-input .n-input__prefix) {
  margin-right: 8px;
}

:deep(.n-input-number .n-input__prefix) {
  margin-right: 8px;
}

:deep(.n-input-number .n-input__suffix) {
  color: #8c8c8c;
  font-weight: 500;
}

/* 日期选择器图标样式 */
:deep(.n-date-picker .n-input__suffix) {
  color: #8c8c8c;
}

/* 选择器箭头样式 */
:deep(.n-select .n-base-suffix) {
  color: #8c8c8c;
}

/* 表单项间距调整 */
:deep(.n-form-item) {
  margin-bottom: 20px;
}

:deep(.n-form-item:last-child) {
  margin-bottom: 0;
}

/* 滚动条样式 */
.follow-info-form::-webkit-scrollbar {
  width: 6px;
}

.follow-info-form::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.follow-info-form::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.follow-info-form::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .follow-info-form {
    padding: 0;
    max-height: 60vh;
  }
  
  .form-header {
    margin-bottom: 16px;
    padding-bottom: 12px;
  }
  
  .header-title {
    font-size: 14px;
  }
  
  .form-section {
    margin-bottom: 20px;
    padding: 16px;
  }
  
  .section-title {
    font-size: 13px;
  }
  
  :deep(.n-form-item) {
    margin-bottom: 16px;
  }
  
  :deep(.n-form-item-label) {
    font-size: 13px;
  }
  
  :deep(.n-grid) {
    grid-template-columns: 1fr !important;
  }
  
  .switch-container {
    gap: 8px;
  }
  
  .switch-text {
    font-size: 13px;
  }
  
  .upload-trigger {
    padding: 16px;
  }
  
  .upload-title {
    font-size: 13px;
  }
  
  .upload-hint {
    font-size: 11px;
  }
  
  .form-actions {
    margin-top: 24px;
    padding-top: 16px;
  }
}

@media (max-width: 480px) {
  .form-section {
    padding: 12px;
    margin-bottom: 16px;
  }
  
  .quick-actions {
    padding: 8px;
  }
  
  :deep(.n-space) {
    flex-wrap: wrap;
  }
  
  :deep(.n-button) {
    font-size: 12px;
    padding: 4px 8px;
  }
  
  .switch-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .upload-trigger {
    padding: 12px;
  }
}
</style>