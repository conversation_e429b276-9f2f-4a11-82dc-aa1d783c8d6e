{"name": "admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"client:dev": "cd frontend && vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "check": "tsc --noEmit", "server:dev": "nodemon", "dev": "concurrently \"npm run client:dev\" \"npm run server:dev\"", "migrate": "npx tsx scripts/migrate-mock-data.ts", "migrate:all": "npx tsx scripts/migrate-all-data.ts", "migrate:dev": "npx tsx --env-file=.env scripts/migrate-mock-data.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:mysql": "jest src/database/MySQLManager.test.ts", "test:migration": "npx tsx scripts/test-migration.ts", "migration:run": "npx tsx scripts/run-migration.ts", "migration:test": "npm run test:migration"}, "dependencies": {"@types/inquirer": "^9.0.9", "@types/multer": "^2.0.0", "@types/qrcode": "^1.5.5", "@types/uuid": "^10.0.0", "@types/xlsx": "^0.0.35", "@vicons/ionicons5": "^0.12.0", "@vueuse/core": "^10.7.0", "axios": "^1.6.0", "commander": "^14.0.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.21.2", "inquirer": "^12.9.2", "multer": "^2.0.2", "mysql2": "^3.14.3", "naive-ui": "^2.38.0", "pinia": "^2.1.0", "qrcode": "^1.5.4", "uuid": "^11.1.0", "vue": "^3.4.0", "vue-router": "^4.2.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.28.3", "@babel/preset-env": "^7.28.3", "@eslint/js": "^8.57.0", "@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/jest": "^30.0.0", "@types/node": "^22.15.30", "@types/supertest": "^6.0.3", "@vitejs/plugin-vue": "^5.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/tsconfig": "^0.5.0", "babel-jest": "^30.0.5", "concurrently": "^9.2.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.20.0", "globals": "^16.0.0", "jest": "^30.0.5", "nodemon": "^3.1.10", "sass-embedded": "^1.90.0", "supertest": "^7.1.4", "ts-jest": "^29.4.1", "ts-node": "^10.9.2", "tsx": "^4.20.4", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-trae-solo-badge": "^1.0.0", "vue-tsc": "^1.8.0"}}