import request from '@/utils/request'
import type {
  OptionCategory,
  OptionItem,
  CreateOptionCategoryRequest,
  UpdateOptionCategoryRequest,
  CreateOptionItemRequest,
  UpdateOptionItemRequest,
  GetOptionCategoriesParams,
  GetOptionItemsParams
} from '@/types/options'

// API 响应解包工具函数
const unwrapResponse = <T>(response: any): T => {
  // 后端返回格式: {success: boolean, data: T}
  if (response.data && response.data.success !== undefined) {
    // 对于创建和更新操作，后端返回 {success: true, data: item}
    // 对于列表操作，后端返回 {success: true, data: {items: [], total: ...}}
    return response.data.data
  }
  // 如果没有success字段，直接返回data
  return response.data
}

// 选项分类 API
export const optionCategoriesApi = {
  // 获取选项分类列表
  async getList(params?: GetOptionCategoriesParams) {
    const response = await request.get('/options-management/categories', { params })
    return unwrapResponse<{
      data: OptionCategory[]
      total: number
      page: number
      page_size: number
      total_pages: number
    }>(response)
  },

  // 获取单个分类
  async getById(id: string) {
    const response = await request.get(`/options-management/categories/${id}`)
    return unwrapResponse<{ data: OptionCategory }>(response)
  },

  // 创建分类
  async create(data: CreateOptionCategoryRequest) {
    const response = await request.post('/options-management/categories', data)
    return unwrapResponse<{ data: OptionCategory }>(response)
  },

  // 更新分类
  async update(id: string, data: UpdateOptionCategoryRequest) {
    const response = await request.put(`/options-management/categories/${id}`, data)
    return unwrapResponse<{ data: OptionCategory }>(response)
  },

  // 删除选项分类
  async delete(id: string) {
    const response = await request.delete(`/options-management/categories/${id}`)
    return unwrapResponse(response)
  },

  // 获取所有启用的分类（用于下拉选择，使用公共API）
  async getActive() {
    const response = await request.get('/options/categories?is_active=true')
    return unwrapResponse<OptionCategory[]>(response)
  }
}

// 选项数据 API
export const optionItemsApi = {
  // 只读：按分类ID获取（保留公共API）
  async getByCategory(categoryId: string) {
    const response = await request.get(`/options/items/${categoryId}`)
    return unwrapResponse<OptionItem[]>(response)
  },

  // 只读：按分类代码获取选项（用于前端组件）
  async getByCategoryCode(categoryCode: string) {
    const response = await request.get(`/options/by-category/${categoryCode}`)
    return unwrapResponse<OptionItem[]>(response)
  },

  // 管理端：获取列表（修正路径，带分页）
  async getList(params?: GetOptionItemsParams) {
    const response = await request.get(`/options-management/items`, { params })
    return unwrapResponse<{
      data: OptionItem[]
      total: number
      page: number
      page_size: number
      total_pages: number
    }>(response)
  },

  // 管理端：根据ID获取
  async getById(id: string) {
    const response = await request.get(`/options-management/items/${id}`)
    return unwrapResponse<{ data: OptionItem }>(response)
  },

  // 管理端：创建
  async create(data: CreateOptionItemRequest) {
    const response = await request.post(`/options-management/items`, data)
    return unwrapResponse<{ data: OptionItem }>(response)
  },

  // 管理端：更新
  async update(id: string, data: UpdateOptionItemRequest) {
    const response = await request.put(`/options-management/items/${id}`, data)
    return unwrapResponse<{ data: OptionItem }>(response)
  },

  // 管理端：删除
  async delete(id: string) {
    const response = await request.delete(`/options-management/items/${id}`)
    return unwrapResponse(response)
  },

  // 管理端：批量删除
  async batchDelete(ids: string[]) {
    const response = await request.delete(`/options-management/items/batch`, { data: { ids } })
    return unwrapResponse(response)
  },

  // 管理端：批量更新排序
  async batchUpdateSort(items: { id: string; sort_order: number }[]) {
    const response = await request.put(`/options-management/items/batch-sort`, { items })
    return unwrapResponse(response)
  },

  // 管理端：批量更新状态
  async batchUpdateStatus(data: { ids: string[]; is_active: boolean }) {
    const response = await request.put(`/options-management/items/batch-status`, data)
    return unwrapResponse(response)
  }
}

// 选项数据获取工具函数（用于前端组件）
export const optionsUtils = {
  // 获取客户来源选项
  async getCustomerSources(): Promise<OptionItem[]> {
    try {
      const response = await optionItemsApi.getByCategoryCode('customer_source')
      return response
    } catch (error) {
      console.error('获取客户来源选项失败:', error)
      return []
    }
  },

  // 获取客户等级选项
  async getCustomerLevels(): Promise<OptionItem[]> {
    try {
      const response = await optionItemsApi.getByCategoryCode('customer_level')
      return response
    } catch (error) {
      console.error('获取客户等级选项失败:', error)
      return []
    }
  },

  // 获取客户标签选项
  async getCustomerTags(): Promise<OptionItem[]> {
    try {
      const response = await optionItemsApi.getByCategoryCode('customer_tags')
      return response
    } catch (error) {
      console.error('获取客户标签选项失败:', error)
      return []
    }
  },

  // 通用获取选项方法
  async getOptionsByCategory(categoryCode: string): Promise<OptionItem[]> {
    try {
      const response = await optionItemsApi.getByCategoryCode(categoryCode)
      return response
    } catch (error) {
      console.error(`获取分类 ${categoryCode} 选项失败:`, error)
      return []
    }
  },

  // 将选项数据转换为 Naive UI 的 SelectOption 格式
  toSelectOptions(items: OptionItem[]): Array<{ label: string; value: string; disabled?: boolean }> {
    return items
      .filter(item => item.is_active)
      .sort((a, b) => a.sort_order - b.sort_order)
      .map(item => ({
        label: item.label,
        value: item.value,
        disabled: !item.is_active
      }))
  },

  // 根据值获取标签
  getLabelByValue(items: OptionItem[], value: string): string {
    const item = items.find(item => item.value === value)
    return item ? item.label : value
  }
}