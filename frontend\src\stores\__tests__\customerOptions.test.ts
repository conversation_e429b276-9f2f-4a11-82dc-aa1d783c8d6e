import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { useCustomerOptionsStore } from '../customerOptions'
import * as customerOptionsApi from '@/api/customerOptions'
import { CUSTOMER_OPTION_CATEGORIES } from '@/constants/customerOptions'

// Mock API
vi.mock('@/api/customerOptions', () => ({
  getOptionCategories: vi.fn(),
  getOptionItemsByCategory: vi.fn(),
  createOptionItem: vi.fn(),
  updateOptionItem: vi.fn(),
  deleteOptionItem: vi.fn(),
  batchDeleteOptionItems: vi.fn(),
  batchUpdateOptionItemStatus: vi.fn(),
  getCustomerSources: vi.fn(),
  getCustomerLevels: vi.fn(),
  getCustomerStatuses: vi.fn(),
  getCustomerTags: vi.fn()
}))

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock message API
vi.mock('naive-ui', () => ({
  createDiscreteApi: () => ({
    message: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn(),
      info: vi.fn()
    }
  })
}))

describe('useCustomerOptionsStore', () => {
  let store: ReturnType<typeof useCustomerOptionsStore>
  let pinia: any

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    store = useCustomerOptionsStore()
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      expect(store.categories).toEqual([])
      expect(store.itemsMap).toEqual({})
      expect(store.loadingMap).toEqual({
        categories: false,
        options: {}
      })
      expect(store.error).toBeNull()
    })
  })

  describe('计算属性', () => {
    beforeEach(() => {
      // 设置测试数据
      store.itemsMap = {
        customer_source: [
          { id: '1', categoryCode: 'customer_source', code: 'online', name: 'online', value: 'online', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' },
          { id: '2', categoryCode: 'customer_source', code: 'offline', name: 'offline', value: 'offline', sortOrder: 2, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }
        ],
        customer_level: [
          { id: '3', categoryCode: 'customer_level', code: 'vip', name: 'vip', value: 'vip', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' },
          { id: '4', categoryCode: 'customer_level', code: 'normal', name: 'normal', value: 'normal', sortOrder: 2, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }
        ],
        customer_status: [
          { id: '5', categoryCode: 'customer_status', code: 'active', name: 'active', value: 'active', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' },
          { id: '6', categoryCode: 'customer_status', code: 'inactive', name: 'inactive', value: 'inactive', sortOrder: 2, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }
        ],
        customer_tag: [
          { id: '7', categoryCode: 'customer_tag', code: 'important', name: 'important', value: 'important', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }
        ]
      }
    })

    it('应该正确计算客户来源选项', () => {
      expect(store.customerSources).toEqual([
        { id: '1', code: 'online', value: 'online', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' },
        { id: '2', code: 'offline', value: 'offline', sortOrder: 2, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }
      ])
    })

    it('应该正确计算客户等级选项', () => {
      expect(store.customerLevels).toEqual([
        { id: '3', code: 'vip', value: 'vip', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' },
        { id: '4', code: 'normal', value: 'normal', sortOrder: 2, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }
      ])
    })

    it('应该正确计算客户状态选项', () => {
      expect(store.customerStatuses).toEqual([
        { id: '5', code: 'active', value: 'active', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' },
        { id: '6', code: 'inactive', value: 'inactive', sortOrder: 2, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }
      ])
    })

    it('应该正确计算客户标签选项', () => {
      expect(store.customerTags).toEqual([
        { id: '7', code: 'important', value: 'important', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }
      ])
    })


  })

  describe('缓存管理', () => {
    it('应该能够获取缓存数据', () => {
      const mockData = [{ id: '1', code: 'test', value: 'test', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }]
      localStorageMock.getItem.mockReturnValue(JSON.stringify({
        data: mockData,
        timestamp: Date.now()
      }))
      
      const cachedData = store.getCategoryItems('customer_source')
      
      expect(cachedData).toEqual(mockData)
      expect(localStorageMock.getItem).toHaveBeenCalledWith('customer_options_customer_source')
    })

    it('应该能够保存缓存数据', () => {
      const mockData = [{ id: '1', code: 'test', value: 'test', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }]
      
      // 模拟缓存保存
      localStorageMock.setItem('customer_options_customer_source', JSON.stringify({ data: mockData, timestamp: Date.now() }))
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'customer_options_customer_source',
        expect.stringContaining('"data":')
      )
    })

    it('应该能够清除指定分类的缓存', () => {
      // 模拟清除特定缓存
      localStorageMock.removeItem('customer_options_customer_source')
      
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('customer_options_customer_source')
    })

    it('应该能够清除所有缓存', () => {
      store.clearAllCache()
      
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('customer_options_categories')
      Object.keys(CUSTOMER_OPTION_CATEGORIES).forEach(category => {
        expect(localStorageMock.removeItem).toHaveBeenCalledWith(`customer_options_${category}`)
      })
    })

    it('应该处理过期的缓存数据', () => {
      const expiredData = {
        data: [{ id: '1', code: 'test', value: 'test', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }],
        timestamp: Date.now() - (6 * 60 * 60 * 1000) // 6小时前
      }
      localStorageMock.getItem.mockReturnValue(JSON.stringify(expiredData))
      
      const cachedData = store.getCategoryItems('customer_source')
      
      expect(cachedData).toBeNull()
    })

    it('应该处理无效的缓存数据', () => {
      localStorageMock.getItem.mockReturnValue('invalid json')
      
      const cachedData = store.getCategoryItems('customer_source')
      
      expect(cachedData).toBeNull()
    })
  })

  describe('数据加载', () => {
    it('应该能够加载分类数据', async () => {
      const mockCategories = [
        { id: '1', code: 'customer_source', name: '客户来源', description: '客户来源分类', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }
      ]
      vi.mocked(customerOptionsApi.getOptionCategories).mockResolvedValue({
        success: true,
        code: 200,
        message: 'success',
        data: {
          items: mockCategories,
          total: mockCategories.length,
          page: 1,
          pageSize: 20,
          totalPages: 1
        }
      })
      
      await store.loadCategories()
      
      expect(store.categories).toEqual(mockCategories)
      expect(store.globalLoading).toBe(false)
    })

    it('应该能够加载指定分类的选项数据', async () => {
      const mockOptions = [
        { id: '1', categoryCode: 'customer_source', code: 'online', name: 'online', value: 'online', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' },
        { id: '2', categoryCode: 'customer_source', code: 'offline', name: 'offline', value: 'offline', sortOrder: 2, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }
      ]
      vi.mocked(customerOptionsApi.getOptionItemsByCategory).mockResolvedValue({
        success: true,
        code: 200,
        message: 'success',
        data: {
          items: mockOptions,
          total: mockOptions.length,
          page: 1,
          pageSize: 20,
          totalPages: 1
        }
      })
      
      await store.loadCategoryItems('customer_source', { categoryCode: 'customer_source' })
      
      expect(store.itemsMap.customer_source).toEqual(mockOptions)
      expect(store.getCategoryLoading('customer_source')).toBe(false)
    })

    it('应该能够加载所有客户参数选项', async () => {
      const mockSourceOptions = [{ id: '1', categoryCode: 'customer_source', code: 'online', name: 'online', value: 'online', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }]
       const mockLevelOptions = [{ id: '2', categoryCode: 'customer_level', code: 'vip', name: 'vip', value: 'vip', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }]
      
      vi.mocked(customerOptionsApi.getCustomerSources).mockResolvedValue({
        success: true,
        code: 200,
        message: 'success',
        data: {
          items: mockSourceOptions,
          total: mockSourceOptions.length,
          page: 1,
          pageSize: 20,
          totalPages: 1
        }
      })
      vi.mocked(customerOptionsApi.getCustomerLevels).mockResolvedValue({
        success: true,
        code: 200,
        message: 'success',
        data: {
          items: mockLevelOptions,
          total: mockLevelOptions.length,
          page: 1,
          pageSize: 20,
          totalPages: 1
        }
      })
      vi.mocked(customerOptionsApi.getCustomerStatuses).mockResolvedValue({ 
          code: 200, 
          message: 'success', 
          success: true, 
          data: { items: [], total: 0, page: 1, pageSize: 10, totalPages: 0 } 
        })
      vi.mocked(customerOptionsApi.getCustomerTags).mockResolvedValue({ 
        code: 200, 
        message: 'success', 
        success: true, 
        data: { items: [], total: 0, page: 1, pageSize: 10, totalPages: 0 } 
      })

      
      await store.loadAllCustomerOptions()
      
      expect(store.itemsMap.customer_source).toEqual(mockSourceOptions)
      expect(store.itemsMap.customer_level).toEqual(mockLevelOptions)
    })

    it('应该优先使用缓存数据', async () => {
      const cachedData = [{ id: '1', categoryCode: 'customer_source', code: 'cached', name: 'cached', value: 'cached', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }]
      localStorageMock.getItem.mockReturnValue(JSON.stringify({
        data: cachedData,
        timestamp: Date.now()
      }))
      
      await store.loadCategoryItems('customer_source', { categoryCode: 'customer_source' })
      
      expect(store.itemsMap.customer_source).toEqual(cachedData)
      expect(customerOptionsApi.getOptionItemsByCategory).not.toHaveBeenCalled()
    })

    it('应该在强制刷新时跳过缓存', async () => {
      const cachedData = [{ id: '1', categoryCode: 'customer_source', code: 'cached', name: 'cached', value: 'cached', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }]
       const apiData = [{ id: '2', categoryCode: 'customer_source', code: 'api', name: 'api', value: 'api', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }]
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify({
        data: cachedData,
        timestamp: Date.now()
      }))
      vi.mocked(customerOptionsApi.getOptionItemsByCategory).mockResolvedValue({
        success: true,
        code: 200,
        message: 'success',
        data: {
          items: apiData,
          total: apiData.length,
          page: 1,
          pageSize: 20,
          totalPages: 1
        }
      })
      
      await store.loadCategoryItems('customer_source', { categoryCode: 'customer_source' })
      
      expect(store.itemsMap.customer_source).toEqual(apiData)
      expect(customerOptionsApi.getOptionItemsByCategory).toHaveBeenCalled()
    })

    it('应该处理加载错误', async () => {
      const error = new Error('API Error')
      vi.mocked(customerOptionsApi.getOptionCategories).mockRejectedValue(error)
      
      await store.loadCategories()
      
      expect(store.error).toBe('加载分类数据失败: API Error')
      expect(store.globalLoading).toBe(false)
    })
  })

  describe('数据操作', () => {
    it('应该能够创建选项', async () => {
      const newItem = { id: '1', categoryCode: 'customer_source', code: 'new', name: 'new', value: 'new', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }
      vi.mocked(customerOptionsApi.createOptionItem).mockResolvedValue({ success: true, code: 200, message: 'success', data: newItem })
      
      const createData = {
        categoryCode: 'customer_source',
        code: 'new',
        name: '新选项',
        value: 'new'
      }
      
      const result = await store.createItem('customer_source', createData)
      
      expect(customerOptionsApi.createOptionItem).toHaveBeenCalledWith(createData)
        expect(result).toBe(true)
      // 应该清除相关缓存
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('customer_options_customer_source')
    })

    it('应该能够更新选项', async () => {
      const updatedItem = { id: '1', categoryCode: 'customer_source', code: 'updated', name: 'updated', value: 'updated', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }
      vi.mocked(customerOptionsApi.updateOptionItem).mockResolvedValue({ success: true, code: 200, message: 'success', data: updatedItem })
      
      const updateData = {
          id: '1',
          categoryCode: 'customer_source',
          code: 'updated',
          name: 'updated',
          value: 'updated',
          sortOrder: 1,
          enabled: true
        }
      
      const result = await store.updateItem('customer_source', '1', updateData)
      
      expect(customerOptionsApi.updateOptionItem).toHaveBeenCalledWith('1', updateData)
        expect(result).toBe(true)
    })

    it('应该能够删除选项', async () => {
      vi.mocked(customerOptionsApi.deleteOptionItem).mockResolvedValue({ success: true, code: 200, message: 'success', data: { deleted: true } })
      
      // 先设置一些数据
      store.itemsMap.customer_source = [
        { id: '1', categoryCode: 'customer_source', code: 'test1', name: 'test1', value: 'test1', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' },
        { id: '2', categoryCode: 'customer_source', code: 'test2', name: 'test2', value: 'test2', sortOrder: 2, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }
      ]
      
      const result = await store.deleteItem('customer_source', '1')
      
      expect(customerOptionsApi.deleteOptionItem).toHaveBeenCalledWith('1')
      expect(result).toBe(true)
      // 应该从本地数据中移除
      expect(store.itemsMap.customer_source).toEqual([
        { id: '2', categoryCode: 'customer_source', code: 'test2', name: 'test2', value: 'test2', sortOrder: 2, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }
      ])
    })

    it('应该能够批量删除选项', async () => {
      vi.mocked(customerOptionsApi.batchDeleteOptionItems).mockResolvedValue({ success: true, code: 200, message: 'success', data: { deleted: true } })
      
      // 先设置一些数据
      store.itemsMap.customer_source = [
        { id: '1', categoryCode: 'customer_source', code: 'test1', name: 'test1', value: 'test1', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' },
        { id: '2', categoryCode: 'customer_source', code: 'test2', name: 'test2', value: 'test2', sortOrder: 2, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' },
        { id: '3', categoryCode: 'customer_source', code: 'test3', name: 'test3', value: 'test3', sortOrder: 3, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }
      ]
      
      const result = await store.batchDeleteItems('customer_source', ['1', '2'])
      
      expect(customerOptionsApi.batchDeleteOptionItems).toHaveBeenCalledWith(['1', '2'])
      expect(result).toBe(true)
      // 应该从本地数据中移除
      expect(store.itemsMap.customer_source).toEqual([
        { id: '3', categoryCode: 'customer_source', code: 'test3', name: 'test3', value: 'test3', sortOrder: 3, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }
      ])
    })

    it('应该处理操作错误', async () => {
      const error = new Error('创建失败')
      vi.mocked(customerOptionsApi.createOptionItem).mockRejectedValue(error)
      
      await expect(store.createItem('customer_source', {
        categoryCode: 'customer_source',
        code: 'test',
        name: '测试',
        value: 'test'
      })).rejects.toThrow('创建失败')
      
      expect(store.error).toBe('创建选项失败: 创建失败')
    })
  })

  describe('工具方法', () => {
    beforeEach(() => {
      store.itemsMap = {
        customer_source: [
          { id: '1', categoryCode: 'customer_source', code: 'online', name: 'online', value: 'online', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' },
          { id: '2', categoryCode: 'customer_source', code: 'offline', name: 'offline', value: 'offline', sortOrder: 2, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }
        ]
      }
    })

    it('应该能够获取分类选项', () => {
      const options = store.getCategoryItems('customer_source')
      
      expect(options).toEqual([
          { id: '1', categoryCode: 'customer_source', code: 'online', name: 'online', value: 'online', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' },
          { id: '2', categoryCode: 'customer_source', code: 'offline', name: 'offline', value: 'offline', sortOrder: 2, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }
        ])
    })

    it('应该能够获取加载状态', () => {
      store.loadingMap.customer_source = true
      
      const isLoading = store.getCategoryLoading('customer_source')
      
      expect(isLoading).toBe(true)
    })

    it('应该能够查找选项', () => {
      const option = store.findItemByCode('customer_source', 'online')
      
      expect(option).toEqual({ id: '1', categoryCode: 'customer_source', code: 'online', name: 'online', value: 'online', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' })
    })

    it('应该在找不到选项时返回undefined', () => {
      const option = store.findItemByCode('customer_source', 'nonexistent')
      
      expect(option).toBeNull()
    })

    it('应该能够清除错误', () => {
      store.error = '测试错误'
      
      store.clearError()
      
      expect(store.error).toBeNull()
    })

    it('应该能够重置状态', () => {
      // 设置一些状态
      store.categories = [{ id: '1', code: 'test', name: '测试', description: '测试分类', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }]
      store.itemsMap = { customer_source: [{ id: '1', categoryCode: 'customer_source', code: 'test', name: 'test', value: 'test', sortOrder: 1, enabled: true, createdAt: '2024-01-01', updatedAt: '2024-01-01' }] }
      store.error = '测试错误'
      
      store.resetState()
      
      expect(store.categories).toEqual([])
      expect(store.itemsMap).toEqual({})
      expect(store.error).toBeNull()
      expect(store.loadingMap).toEqual({
        categories: false,
        options: {}
      })
    })
  })

  describe('状态管理', () => {
    it('应该正确管理加载状态', async () => {
      let resolvePromise: (value: any) => void
      const promise = new Promise(resolve => {
        resolvePromise = resolve
      })
      vi.mocked(customerOptionsApi.getOptionCategories).mockReturnValue(promise as any)
      
      // 开始加载
      const loadPromise = store.loadCategories()
      expect(store.globalLoading).toBe(true)
      
      // 完成加载
      resolvePromise!({ data: [], total: 0 })
      await loadPromise
      expect(store.globalLoading).toBe(false)
    })

    it('应该正确管理选项加载状态', async () => {
      let resolvePromise: (value: any) => void
      const promise = new Promise(resolve => {
        resolvePromise = resolve
      })
      vi.mocked(customerOptionsApi.getOptionItemsByCategory).mockReturnValue(promise as any)
      
      // 开始加载
      const loadPromise = store.loadCategoryItems('customer_source')
      expect(store.getCategoryLoading('customer_source')).toBe(true)
      
      // 完成加载
      resolvePromise!({ data: [], total: 0 })
      await loadPromise
      expect(store.getCategoryLoading('customer_source')).toBe(false)
    })
  })

  describe('刷新数据', () => {
    it('应该能够刷新所有数据', async () => {
      vi.mocked(customerOptionsApi.getOptionCategories).mockResolvedValue({ 
          code: 200, 
          message: 'success', 
          success: true, 
          data: { items: [], total: 0, page: 1, pageSize: 10, totalPages: 0 } 
        })
      vi.mocked(customerOptionsApi.getCustomerSources).mockResolvedValue({ 
          code: 200, 
          message: 'success', 
          success: true, 
          data: { items: [], total: 0, page: 1, pageSize: 10, totalPages: 0 } 
        })
      vi.mocked(customerOptionsApi.getCustomerLevels).mockResolvedValue({ 
          code: 200, 
          message: 'success', 
          success: true, 
          data: { items: [], total: 0, page: 1, pageSize: 10, totalPages: 0 } 
        })
      vi.mocked(customerOptionsApi.getCustomerStatuses).mockResolvedValue({ 
          code: 200, 
          message: 'success', 
          success: true, 
          data: { items: [], total: 0, page: 1, pageSize: 10, totalPages: 0 } 
        })
      vi.mocked(customerOptionsApi.getCustomerTags).mockResolvedValue({ 
        code: 200, 
        message: 'success', 
        success: true, 
        data: { items: [], total: 0, page: 1, pageSize: 10, totalPages: 0 } 
      })
      
      await store.refreshAllData()
      
      expect(customerOptionsApi.getOptionCategories).toHaveBeenCalled()
      expect(customerOptionsApi.getCustomerSources).toHaveBeenCalled()
      expect(customerOptionsApi.getCustomerLevels).toHaveBeenCalled()
      expect(customerOptionsApi.getCustomerStatuses).toHaveBeenCalled()
      expect(customerOptionsApi.getCustomerTags).toHaveBeenCalled()
    })

    it('应该能够刷新指定分类数据', async () => {
      vi.mocked(customerOptionsApi.getOptionItemsByCategory).mockResolvedValue({ 
        code: 200, 
        message: 'success', 
        success: true, 
        data: { items: [], total: 0, page: 1, pageSize: 10, totalPages: 0 } 
      })
      
      await store.refreshCategoryItems('customer_source')
      
      expect(customerOptionsApi.getOptionItemsByCategory).toHaveBeenCalledWith('customer_source', {
        page: 1,
        pageSize: 1000
      })
    })
  })
})