<template>
  <div class="customer-option-manager">
    <!-- 头部操作区 -->
    <div class="manager-header">
      <div class="header-left">
        <div class="category-info">
          <n-icon :size="20" :color="categoryInfo.color">
            <component :is="categoryInfo.icon" />
          </n-icon>
          <span class="category-name">{{ categoryInfo.name }}</span>
          <n-tag v-if="categoryInfo.description" size="small" type="info">
            {{ categoryInfo.description }}
          </n-tag>
        </div>
      </div>
      
      <div class="header-right">
        <n-space>
          <!-- 搜索框 -->
          <n-input
            v-if="showSearch"
            v-model:value="searchKeyword"
            placeholder="搜索选项..."
            clearable
            style="width: 200px"
          >
            <template #prefix>
              <n-icon><SearchOutlined /></n-icon>
            </template>
          </n-input>
          
          <!-- 状态筛选 -->
          <n-select
            v-if="showFilters"
            v-model:value="statusFilter"
            placeholder="状态筛选"
            clearable
            style="width: 120px"
            :options="statusOptions"
          />
          
          <!-- 刷新按钮 -->
          <n-button
            circle
            quaternary
            @click="handleRefresh"
            :loading="loading"
          >
            <template #icon>
              <n-icon><ReloadOutlined /></n-icon>
            </template>
          </n-button>
          
          <!-- 添加按钮 -->
          <n-button
            v-if="!readonly"
            type="primary"
            @click="handleAdd"
          >
            <template #icon>
              <n-icon><PlusOutlined /></n-icon>
            </template>
            添加{{ categoryInfo.name }}
          </n-button>
        </n-space>
      </div>
    </div>

    <!-- 批量操作栏 -->
    <div v-if="showBatchActions && hasSelection" class="batch-actions">
      <n-space>
        <span class="selection-info">
          已选择 {{ selectionCount }} 项
        </span>
        
        <n-button
          size="small"
          @click="handleBatchEnable"
          :loading="submitting"
        >
          批量启用
        </n-button>
        
        <n-button
          size="small"
          @click="handleBatchDisable"
          :loading="submitting"
        >
          批量禁用
        </n-button>
        
        <n-popconfirm
          @positive-click="handleBatchDelete"
          negative-text="取消"
          positive-text="确认删除"
        >
          <template #trigger>
            <n-button
              size="small"
              type="error"
              :loading="submitting"
            >
              批量删除
            </n-button>
          </template>
          确定要删除选中的 {{ selectionCount }} 项吗？此操作不可撤销。
        </n-popconfirm>
      </n-space>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <n-data-table
        :columns="tableColumns"
        :data="items"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row: OptionItem) => row.id"
        v-model:checked-row-keys="checkedRowKeys"
        :scroll-x="800"
        striped
        size="small"
        flex-height
        style="height: 400px"
      />
    </div>

    <!-- 添加/编辑模态框 -->
    <n-modal
      v-model:show="showModal"
      :title="modalTitle"
      preset="card"
      style="width: 600px"
      :mask-closable="false"
    >
      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-placement="left"
        label-width="100px"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="选项代码" path="code">
          <n-input
            v-model:value="formData.code"
            placeholder="请输入选项代码"
            :disabled="isEdit"
          />
        </n-form-item>
        
        <n-form-item label="选项值" path="value">
          <n-input
            v-model:value="formData.value"
            placeholder="请输入选项值"
          />
        </n-form-item>
        
        <n-form-item label="选项名称" path="name">
          <n-input
            v-model:value="formData.name"
            placeholder="请输入选项名称"
          />
        </n-form-item>
        
        <n-form-item label="描述" path="description">
          <n-input
            v-model:value="formData.description"
            type="textarea"
            placeholder="请输入描述（可选）"
            :rows="3"
          />
        </n-form-item>
        
        <n-form-item label="扩展属性" path="properties">
          <n-input
            v-model:value="propertiesString"
            type="textarea"
            placeholder="请输入扩展属性（JSON格式，可选）"
            :rows="2"
          />
        </n-form-item>
        
        <n-form-item label="排序" path="sortOrder">
          <n-input-number
            v-model:value="formData.sortOrder"
            placeholder="排序值"
            :min="0"
            :max="9999"
            style="width: 100%"
          />
        </n-form-item>
        
        <n-form-item label="状态" path="enabled">
          <n-switch
            v-model:value="formData.enabled"
          >
            <template #checked>启用</template>
            <template #unchecked>禁用</template>
          </n-switch>
        </n-form-item>
      </n-form>
      
      <template #footer>
        <n-space justify="end">
          <n-button @click="handleCancel">取消</n-button>
          <n-button
            type="primary"
            @click="handleSubmit"
            :loading="submitting"
          >
            {{ isEdit ? '更新' : '创建' }}
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, h } from 'vue'
import {
  NSpace,
  NButton,
  NIcon,
  NTag,
  NInput,
  NSelect,
  NDataTable,
  NModal,
  NForm,
  NFormItem,
  NInputNumber,
  NSwitch,
  NPopconfirm,
  useMessage,
  useDialog,
  type DataTableColumns,
  type FormInst,
  type FormRules
} from 'naive-ui'
// 使用 Naive UI 内置图标
import type {
  OptionItem,
  CreateOptionItemRequest,
  UpdateOptionItemRequest,
  CustomerOptionManagerProps,
  CustomerOptionManagerEmits
} from '@/types/customerOptions'
import { useCustomerOptions } from '@/composables/useCustomerOptions'
import {
  STATUS_OPTIONS,
  TABLE_COLUMN_WIDTHS,
  VALIDATION_RULES,
  CUSTOMER_OPTION_CATEGORIES
} from '@/constants/customerOptions'

// ==================== Props & Emits ====================

const props = withDefaults(defineProps<CustomerOptionManagerProps>(), {
  readonly: false,
  showBatchActions: true,
  showSearch: true,
  showFilters: true,
  defaultPageSize: 10
})

const emit = defineEmits<CustomerOptionManagerEmits>()

// ==================== 组合函数 ====================

const message = useMessage()
const dialog = useDialog()

const {
  items,
  loading,
  submitting,
  pagination,
  searchKeyword,
  statusFilter,
  checkedRowKeys,
  categoryInfo,
  hasSelection,
  selectionCount,
  isEmpty,
  loadItems,
  createItem,
  updateItem,
  deleteItem,
  batchDelete,
  batchUpdateStatus,
  handleSearch,
  handleFilter,
  resetFilters,
  refreshData
} = useCustomerOptions({
  categoryCode: props.categoryCode,
  autoLoad: true,
  pagination: {
    pageSize: props.defaultPageSize
  }
})

// ==================== 响应式数据 ====================

/** 表单引用 */
const formRef = ref<FormInst | null>(null)

/** 模态框显示状态 */
const showModal = ref(false)

/** 是否为编辑模式 */
const isEdit = ref(false)

/** 当前编辑的项目ID */
const editingId = ref<string | null>(null)

/** 表单数据 */
const formData = ref<CreateOptionItemRequest>({
  categoryCode: '',
  code: '',
  name: '',
  value: '',
  description: '',
  sortOrder: 0,
  enabled: true,
  properties: {}
})

/** 扩展属性字符串表示 */
const propertiesString = ref('')

// ==================== 计算属性 ====================

/** 模态框标题 */
const modalTitle = computed(() => {
  return isEdit.value ? `编辑${props.categoryName}` : `添加${props.categoryName}`
})

/** 状态选项 */
const statusOptions = computed(() => [
  { label: '全部', value: '' },
  { label: '启用', value: 'true' },
  { label: '禁用', value: 'false' }
])

/** 表格列配置 */
const tableColumns = computed((): DataTableColumns<OptionItem> => {
  const baseColumns: DataTableColumns<OptionItem> = [
    {
      type: 'selection',
      width: TABLE_COLUMN_WIDTHS.SELECTION,
      disabled: () => props.readonly
    },
    {
      title: '序号',
      key: 'index',
      width: TABLE_COLUMN_WIDTHS.INDEX,
      render: (_, index) => index + 1
    },
    {
      title: '代码',
      key: 'code',
      width: TABLE_COLUMN_WIDTHS.CODE,
      ellipsis: { tooltip: true }
    },
    {
      title: '显示名称',
      key: 'name',
      width: TABLE_COLUMN_WIDTHS.NAME,
      ellipsis: { tooltip: true }
    },
    {
      title: '选项值',
      key: 'value',
      width: TABLE_COLUMN_WIDTHS.NAME,
      ellipsis: { tooltip: true }
    },
    {
      title: '颜色',
      key: 'color',
      width: TABLE_COLUMN_WIDTHS.COLOR,
      render: (row) => {
        return h('div', {
          style: {
            width: '20px',
            height: '20px',
            backgroundColor: '#1677ff',
            borderRadius: '4px',
            display: 'inline-block'
          }
        })
      }
    },
    {
      title: '排序',
      key: 'sortOrder',
      width: TABLE_COLUMN_WIDTHS.SORT_ORDER,
      sorter: (a, b) => a.sortOrder - b.sortOrder
    },
    {
      title: '状态',
      key: 'enabled',
      width: TABLE_COLUMN_WIDTHS.STATUS,
      render: (row) => {
        return h(NTag, {
          type: row.enabled ? 'success' : 'default',
          size: 'small'
        }, {
          default: () => row.enabled ? '启用' : '禁用'
        })
      }
    },
    {
      title: '创建时间',
      key: 'createdAt',
      width: TABLE_COLUMN_WIDTHS.CREATED_AT,
      render: (row) => {
        return new Date(row.createdAt).toLocaleString()
      }
    }
  ]

  // 如果不是只读模式，添加操作列
  if (!props.readonly) {
    baseColumns.push({
      title: '操作',
      key: 'actions',
      width: TABLE_COLUMN_WIDTHS.ACTIONS,
      fixed: 'right',
      render: (row) => {
        return h(NSpace, { size: 'small' }, {
          default: () => [
            h(NButton, {
              size: 'small',
              type: 'primary',
              quaternary: true,
              onClick: () => handleEdit(row)
            }, {
              default: () => '编辑'
            }),
            h(NPopconfirm, {
              onPositiveClick: () => handleDelete(row.id)
            }, {
              trigger: () => h(NButton, {
                size: 'small',
                type: 'error',
                quaternary: true
              }, {
                default: () => '删除'
              }),
              default: () => '确定要删除这个选项吗？'
            })
          ]
        })
      }
    })
  }

  // 如果有自定义列配置，合并配置
  if (props.customColumns && props.customColumns.length > 0) {
    return [...baseColumns, ...props.customColumns]
  }

  return baseColumns
})

/** 表单验证规则 */
const formRules = computed((): FormRules => ({
  code: VALIDATION_RULES.CODE,
  value: VALIDATION_RULES.VALUE,
  name: VALIDATION_RULES.LABEL,
  description: VALIDATION_RULES.DESCRIPTION,
  sortOrder: VALIDATION_RULES.SORT_ORDER
}))

// ==================== 方法 ====================

/**
 * 重置表单数据
 */
const resetFormData = () => {
  formData.value = {
    categoryCode: props.categoryCode,
    code: '',
    name: '',
    value: '',
    description: '',
    sortOrder: 0,
    enabled: true,
    properties: {}
  }
  propertiesString.value = ''
}

/**
 * 处理添加
 */
const handleAdd = () => {
  isEdit.value = false
  editingId.value = null
  resetFormData()
  showModal.value = true
}

/**
 * 处理编辑
 */
const handleEdit = (item: OptionItem) => {
  isEdit.value = true
  editingId.value = item.id
  formData.value = {
    categoryCode: props.categoryCode,
    code: item.code,
    name: item.name,
    value: item.value,
    description: item.description || '',
    sortOrder: item.sortOrder,
    enabled: item.enabled,
    properties: item.properties || {}
  }
  // 设置扩展属性字符串
  propertiesString.value = item.properties ? JSON.stringify(item.properties, null, 2) : ''
  showModal.value = true
}

/**
 * 处理删除
 */
const handleDelete = async (id: string) => {
  const success = await deleteItem(id)
  if (success) {
    emit('operation-complete', 'delete', true, { id })
  }
}

/**
 * 处理批量启用
 */
const handleBatchEnable = async () => {
  const ids = checkedRowKeys.value as string[]
  const success = await batchUpdateStatus(ids, true)
  if (success) {
    emit('operation-complete', 'batch-enable', true, { ids })
  }
}

/**
 * 处理批量禁用
 */
const handleBatchDisable = async () => {
  const ids = checkedRowKeys.value as string[]
  const success = await batchUpdateStatus(ids, false)
  if (success) {
    emit('operation-complete', 'batch-disable', true, { ids })
  }
}

/**
 * 处理批量删除
 */
const handleBatchDelete = async () => {
  const ids = checkedRowKeys.value as string[]
  const success = await batchDelete(ids)
  if (success) {
    emit('operation-complete', 'batch-delete', true, { ids })
  }
}

/**
 * 处理刷新
 */
const handleRefresh = async () => {
  await refreshData()
  emit('operation-complete', 'refresh', true)
}

/**
 * 处理取消
 */
const handleCancel = () => {
  showModal.value = false
  resetFormData()
}

/**
 * 处理提交
 */
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    let success = false
    
    if (isEdit.value && editingId.value) {
      // 更新
      const updateData: UpdateOptionItemRequest = {
        label: formData.value.name,
        value: formData.value.value,
        description: formData.value.description,
        sort_order: formData.value.sortOrder,
        is_active: formData.value.enabled,
        properties: formData.value.properties
      }
      success = await updateItem(editingId.value, updateData)
      if (success) {
        emit('operation-complete', 'update', true, { id: editingId.value, data: updateData })
      }
    } else {
      // 创建
      const createData: CreateOptionItemRequest = {
        categoryCode: props.categoryCode,
        code: formData.value.code,
        name: formData.value.name,
        value: formData.value.value,
        description: formData.value.description,
        sortOrder: formData.value.sortOrder,
        enabled: formData.value.enabled,
        properties: propertiesString.value ? JSON.parse(propertiesString.value) : {}
      }
      success = await createItem(createData)
      if (success) {
        emit('operation-complete', 'create', true, { data: createData })
      }
    }
    
    if (success) {
      showModal.value = false
      resetFormData()
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// ==================== 监听器 ====================

// 监听数据变化，发出事件
watch(
  () => items.value,
  (newItems) => {
    emit('data-change', newItems)
  },
  { deep: true }
)

// 监听选择变化，发出事件
watch(
  () => checkedRowKeys.value,
  (newSelection) => {
    emit('selection-change', newSelection as string[])
  }
)

// ==================== 生命周期 ====================

onMounted(() => {
  // 设置分类ID（如果需要）
  // 这里可以根据categoryCode获取对应的category_id
})
</script>

<style scoped lang="less">
.customer-option-manager {
  .manager-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 8px;
    
    .header-left {
      .category-info {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .category-name {
          font-size: 16px;
          font-weight: 600;
          color: #262626;
        }
      }
    }
  }
  
  .batch-actions {
    margin-bottom: 16px;
    padding: 12px 16px;
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 6px;
    
    .selection-info {
      color: #1890ff;
      font-weight: 500;
    }
  }
  
  .table-container {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
  }
}
</style>