import { http } from './http'
import { mockPoolCustomers, mockDelay, isDevelopment } from '@/mock/customerData'
import type { Customer } from './customerService'

// API 响应解包工具函数
const unwrapResponse = <T>(response: any): T => {
  // 后端返回格式: {success: boolean, data: T}
  if (response.data && response.data.success !== undefined) {
    return response.data.data || response.data
  }
  return response.data
}

// Pool rule types
export interface PoolRule {
  id: number
  name: string
  condition_type: string
  days?: number
  customer_levels?: string[]
  is_active: boolean
  auto_assign: boolean
  created_at: string
  updated_at: string
}

export interface PoolRuleInsert {
  name: string
  condition_type: string
  days?: number
  customer_levels?: string[]
  is_active?: boolean
  auto_assign?: boolean
}

export interface PoolRuleUpdate {
  name?: string
  condition_type?: string
  days?: number
  customer_levels?: string[]
  is_active?: boolean
  auto_assign?: boolean
}

export interface PoolCustomer extends Customer {
  pool_entry_time?: string
  pool_reason?: string
  original_owner?: string
}

export interface PoolCustomersParams {
  page?: number
  pageSize?: number
  search?: string
  level?: string
  source?: string
  dateRange?: [number, number]
}

export interface PoolCustomersResponse {
  data: PoolCustomer[]
  total: number
  page: number
  pageSize: number
}

// 获取公海客户列表
export const getPoolCustomers = async (params: PoolCustomersParams = {}): Promise<PoolCustomersResponse> => {
  // 开发环境返回mock数据
  if (isDevelopment) {
    await mockDelay()
    
    const {
      page = 1,
      pageSize = 20,
      search,
      level,
      source
    } = params
    
    let filteredData = [...mockPoolCustomers]
    
    // 搜索过滤
    if (search) {
      const searchLower = search.toLowerCase()
      filteredData = filteredData.filter(customer => 
        customer.name.toLowerCase().includes(searchLower) ||
        customer.phone.includes(search) ||
        customer.company?.toLowerCase().includes(searchLower)
      )
    }
    
    // 等级过滤
    if (level) {
      filteredData = filteredData.filter(customer => customer.level === level)
    }
    
    // 来源过滤
    if (source) {
      filteredData = filteredData.filter(customer => customer.source === source)
    }
    
    // 分页
    const total = filteredData.length
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedData = filteredData.slice(startIndex, endIndex)
    
    return unwrapResponse(paginatedData, total, page, pageSize)
  }

  const {
    page = 1,
    pageSize = 20,
    search,
    level,
    source,
    dateRange
  } = params

  const queryParams = new URLSearchParams({
    page: page.toString(),
    pageSize: pageSize.toString(),
    assigned_to: 'null' // 公海客户没有分配给任何人
  })

  // 搜索条件
  if (search) {
    queryParams.append('search', search)
  }

  // 客户等级筛选
  if (level) {
    queryParams.append('level', level)
  }

  // 客户来源筛选
  if (source) {
    queryParams.append('source', source)
  }

  // 日期范围筛选
  if (dateRange && dateRange.length === 2) {
    queryParams.append('startDate', new Date(dateRange[0]).toISOString())
    queryParams.append('endDate', new Date(dateRange[1]).toISOString())
  }

  const response = await http.get(`/customers?${queryParams.toString()}`)
  return unwrapResponse(response)
}

// 认领客户
export const claimCustomers = async (customerIds: number[], userId: string): Promise<void> => {
  const response = await http.put('/customers/claim', {
    customerIds,
    userId
  })
  
  if (!response.data.success) {
    throw new Error(`认领客户失败: ${response.data.message || '未知错误'}`)
  }
}

// 将客户移入公海
export const moveToPool = async (customerIds: number[]): Promise<void> => {
  const response = await http.put('/customers/move-to-pool', {
    customerIds
  })
  
  if (!response.data.success) {
    throw new Error(`移入公海失败: ${response.data.message || '未知错误'}`)
  }
}

// 获取公海规则列表
export const getPoolRules = async (): Promise<PoolRule[]> => {
  const response = await http.get('/pool-rules')
  
  if (!response.data.success) {
    throw new Error(`获取公海规则失败: ${response.data.message || '未知错误'}`)
  }
  
  return response.data.data || []
}

// 创建公海规则
export const createPoolRule = async (rule: PoolRuleInsert): Promise<PoolRule> => {
  const response = await http.post('/pool-rules', rule)
  
  if (!response.data.success) {
    throw new Error(`创建公海规则失败: ${response.data.message || '未知错误'}`)
  }
  
  return response.data.data
}

// 更新公海规则
export const updatePoolRule = async (id: number, rule: PoolRuleUpdate): Promise<PoolRule> => {
  const response = await http.put(`/pool-rules/${id}`, rule)
  
  if (!response.data.success) {
    throw new Error(`更新公海规则失败: ${response.data.message || '未知错误'}`)
  }
  
  return response.data.data
}

// 删除公海规则
export const deletePoolRule = async (id: number): Promise<void> => {
  const response = await http.delete(`/pool-rules/${id}`)
  
  if (!response.data.success) {
    throw new Error(`删除公海规则失败: ${response.data.message || '未知错误'}`)
  }
}

// 执行公海规则
export const executePoolRules = async (): Promise<{ movedCount: number; message: string }> => {
  const response = await http.post('/pool-rules/execute')
  
  if (!response.data.success) {
    throw new Error(`执行公海规则失败: ${response.data.message || '未知错误'}`)
  }
  
  return response.data.data
}

// 自动分配公海客户
export const autoAssignPoolCustomers = async (): Promise<{ assignedCount: number; message: string }> => {
  const response = await http.post('/pool-rules/auto-assign')
  
  if (!response.data.success) {
    throw new Error(`自动分配公海客户失败: ${response.data.message || '未知错误'}`)
  }
  
  return response.data.data
}