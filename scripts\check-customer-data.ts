import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'workchat_admin',
  port: parseInt(process.env.DB_PORT || '3306')
};

async function checkCustomerData() {
  let connection;
  
  try {
    console.log('正在连接数据库...');
    console.log('数据库配置:', {
      host: dbConfig.host,
      user: dbConfig.user,
      database: dbConfig.database,
      port: dbConfig.port
    });
    
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功！');
    
    // 检查customers表是否存在
    console.log('\n=== 检查customers表是否存在 ===');
    const [tables] = await connection.execute(
      "SHOW TABLES LIKE 'customers'"
    );
    
    if (Array.isArray(tables) && tables.length === 0) {
      console.log('❌ customers表不存在！');
      return;
    }
    
    console.log('✅ customers表存在');
    
    // 获取表结构
    console.log('\n=== customers表结构 ===');
    const [columns] = await connection.execute('DESCRIBE customers');
    console.log('表字段:');
    if (Array.isArray(columns)) {
      columns.forEach((col: any) => {
        console.log(`  - ${col.Field}: ${col.Type} ${col.Null === 'NO' ? '(必填)' : '(可选)'}`);
      });
    }
    
    // 统计客户数量
    console.log('\n=== 客户数据统计 ===');
    const [countResult] = await connection.execute(
      'SELECT COUNT(*) as total FROM customers'
    );
    
    const total = Array.isArray(countResult) && countResult.length > 0 
      ? (countResult[0] as any).total 
      : 0;
    
    console.log(`客户总数: ${total}`);
    
    if (total === 0) {
      console.log('❌ customers表为空，没有任何客户数据！');
      console.log('\n建议执行以下操作:');
      console.log('1. 运行 npm run ts-node scripts/init-customer-mock-data.ts 初始化模拟数据');
      console.log('2. 或者通过前端界面手动添加客户数据');
      return;
    }
    
    // 显示前10条客户记录
    console.log('\n=== 前10条客户记录 ===');
    const [customers] = await connection.execute(`
      SELECT 
        id, 
        name, 
        phone, 
        email,
        source, 
        level, 
        status,
        created_at
      FROM customers 
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    
    if (Array.isArray(customers) && customers.length > 0) {
      customers.forEach((customer: any, index: number) => {
        console.log(`\n${index + 1}. 客户信息:`);
        console.log(`   ID: ${customer.id}`);
        console.log(`   姓名: ${customer.name}`);
        console.log(`   手机: ${customer.phone}`);
        console.log(`   邮箱: ${customer.email || '未填写'}`);
        console.log(`   来源: ${customer.source || '未知'}`);
        console.log(`   级别: ${customer.level || '未设置'}`);
        console.log(`   状态: ${customer.status || '未知'}`);
        console.log(`   创建时间: ${customer.created_at}`);
      });
    }
    
    // 按来源统计
    console.log('\n=== 按来源统计 ===');
    const [sourceStats] = await connection.execute(`
      SELECT 
        source, 
        COUNT(*) as count 
      FROM customers 
      WHERE source IS NOT NULL AND source != ''
      GROUP BY source 
      ORDER BY count DESC
    `);
    
    if (Array.isArray(sourceStats) && sourceStats.length > 0) {
      sourceStats.forEach((stat: any) => {
        console.log(`  ${stat.source}: ${stat.count}人`);
      });
    } else {
      console.log('  暂无来源统计数据');
    }
    
    // 按级别统计
    console.log('\n=== 按级别统计 ===');
    const [levelStats] = await connection.execute(`
      SELECT 
        level, 
        COUNT(*) as count 
      FROM customers 
      WHERE level IS NOT NULL AND level != ''
      GROUP BY level 
      ORDER BY count DESC
    `);
    
    if (Array.isArray(levelStats) && levelStats.length > 0) {
      levelStats.forEach((stat: any) => {
        console.log(`  ${stat.level}: ${stat.count}人`);
      });
    } else {
      console.log('  暂无级别统计数据');
    }
    
    console.log('\n=== 数据检查完成 ===');
    console.log('✅ 数据库中存在真实的客户数据');
    console.log('📋 前端客户列表应该显示这些数据库中的真实数据');
    console.log('🔄 如果前端显示为空，请检查API接口和网络连接');
    
  } catch (error) {
    console.error('❌ 检查客户数据时发生错误:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('ECONNREFUSED')) {
        console.log('\n💡 解决建议:');
        console.log('1. 确保MySQL服务正在运行');
        console.log('2. 检查数据库连接配置(.env文件)');
        console.log('3. 确认数据库端口和用户名密码正确');
      } else if (error.message.includes('Access denied')) {
        console.log('\n💡 解决建议:');
        console.log('1. 检查数据库用户名和密码');
        console.log('2. 确认数据库用户有访问权限');
      } else if (error.message.includes('Unknown database')) {
        console.log('\n💡 解决建议:');
        console.log('1. 确认数据库名称正确');
        console.log('2. 创建数据库: CREATE DATABASE workchat_admin;');
      }
    }
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n数据库连接已关闭');
    }
  }
}

// 执行检查
checkCustomerData().catch(console.error);