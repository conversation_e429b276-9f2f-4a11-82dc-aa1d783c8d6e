/**
 * 客户参数管理Pinia状态管理
 * @description 提供客户参数管理的全局状态管理
 * <AUTHOR> Document
 * @date 2024-01-20
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  OptionCategory,
  OptionItem,
  CreateOptionItemRequest,
  UpdateOptionItemRequest,
  QueryOptionItemsParams
} from '@/types/customerOptions'
import {
  getOptionCategories,
  getOptionItems,
  createOptionItem,
  updateOptionItem,
  deleteOptionItem,
  batchDeleteOptionItems,
  getOptionsCategories,
  getOptionsByCategory,
  getAllOptions
} from '@/api/customerOptions'
import {
  CustomerOptionCategory,
  CUSTOMER_OPTION_CATEGORIES,
  CACHE_KEYS,
  CACHE_EXPIRY
} from '@/constants/customerOptions'

// 移除未使用的接口声明

/**
 * 客户参数管理Pinia Store
 */
export const useCustomerOptionsStore = defineStore('customerOptions', () => {
  // ==================== 状态定义 ====================

  /** 选项分类列表 */
  const categories = ref<OptionCategory[]>([])
  
  /** 选项数据映射 - 按分类代码存储 */
  const itemsMap = ref<Record<string, OptionItem[]>>({})
  
  /** 加载状态映射 - 按分类代码存储 */
  const loadingMap = ref<Record<string, boolean>>({})
  
  /** 最后更新时间映射 - 按分类代码存储 */
  const lastUpdateMap = ref<Record<string, number>>({})
  
  /** 全局加载状态 */
  const globalLoading = ref(false)
  
  /** 错误信息 */
  const error = ref<string | null>(null)

  // ==================== 计算属性 ====================

  /** 客户来源选项 */
  const customerSources = computed(() => 
    itemsMap.value[CustomerOptionCategory.SOURCE] || []
  )

  /** 客户等级选项 */
  const customerLevels = computed(() => 
    itemsMap.value[CustomerOptionCategory.LEVEL] || []
  )

  /** 客户状态选项 */
  const customerStatuses = computed(() => 
    itemsMap.value[CustomerOptionCategory.STATUS] || []
  )

  /** 客户标签选项 */
  const customerTags = computed(() => 
    itemsMap.value[CustomerOptionCategory.TAG] || []
  )



  /** 所有客户参数选项映射 */
  const allCustomerOptions = computed(() => ({
    [CustomerOptionCategory.SOURCE]: customerSources.value,
    [CustomerOptionCategory.LEVEL]: customerLevels.value,
    [CustomerOptionCategory.STATUS]: customerStatuses.value,
    [CustomerOptionCategory.TAG]: customerTags.value
  }))

  /** 是否有任何分类正在加载 */
  const isAnyLoading = computed(() => 
    Object.values(loadingMap.value).some(loading => loading)
  )

  /** 已加载的分类数量 */
  const loadedCategoriesCount = computed(() => 
    Object.keys(itemsMap.value).length
  )

  // ==================== 缓存管理 ====================

  /**
   * 从缓存获取数据
   */
  const getFromCache = (categoryCode: string): OptionItem[] | null => {
    try {
      const cacheKey = CACHE_KEYS.ITEMS(categoryCode)
      const cached = localStorage.getItem(cacheKey)
      if (!cached) return null
      
      const { data, timestamp } = JSON.parse(cached)
      const now = Date.now()
      
      // 检查缓存是否过期
      if (now - timestamp > CACHE_EXPIRY.MEDIUM) {
        localStorage.removeItem(cacheKey)
        return null
      }
      
      return data
    } catch (error) {
      console.warn('读取缓存失败:', error)
      return null
    }
  }

  /**
   * 保存数据到缓存
   */
  const saveToCache = (categoryCode: string, data: OptionItem[]) => {
    try {
      const cacheKey = CACHE_KEYS.ITEMS(categoryCode)
      const cacheData = {
        data,
        timestamp: Date.now()
      }
      localStorage.setItem(cacheKey, JSON.stringify(cacheData))
    } catch (error) {
      console.warn('保存缓存失败:', error)
    }
  }

  /**
   * 清除指定分类的缓存
   */
  const clearCategoryCache = (categoryCode: string) => {
    const cacheKey = CACHE_KEYS.ITEMS(categoryCode)
    localStorage.removeItem(cacheKey)
  }

  /**
   * 清除所有缓存
   */
  const clearAllCache = () => {
    Object.values(CustomerOptionCategory).forEach(categoryCode => {
      clearCategoryCache(categoryCode)
    })
  }

  // ==================== 数据加载 ====================

  /**
   * 加载选项分类列表
   */
  const loadCategories = async (): Promise<void> => {
    try {
      globalLoading.value = true
      error.value = null
      
      const response = await getOptionsCategories()
      
      if (response.success && response.data) {
        categories.value = response.data
      } else {
        throw new Error(response.message || '加载分类列表失败')
      }
    } catch (err: any) {
      console.error('加载选项分类失败:', err)
      error.value = err.message || '加载分类列表失败'
      categories.value = []
    } finally {
      globalLoading.value = false
    }
  }

  /**
   * 加载指定分类的选项数据
   */
  const loadCategoryItems = async (
    categoryCode: string,
    params?: QueryOptionItemsParams,
    useCache = true
  ): Promise<void> => {
    try {
      loadingMap.value[categoryCode] = true
      error.value = null
      
      // 尝试从缓存获取数据
      if (useCache && !params?.keyword && params?.enabled !== false) {
        const cachedData = getFromCache(categoryCode)
        if (cachedData) {
          itemsMap.value[categoryCode] = cachedData
          lastUpdateMap.value[categoryCode] = Date.now()
          return
        }
      }
      
      // 根据分类代码选择对应的API函数
      let response
      switch (categoryCode) {
        case CustomerOptionCategory.SOURCE:
        case CustomerOptionCategory.LEVEL:
        case CustomerOptionCategory.STATUS:
        case CustomerOptionCategory.TAG:
        case CustomerOptionCategory.DECORATION_TYPE:
        case CustomerOptionCategory.HOUSE_STATUS:
        case CustomerOptionCategory.PACKAGE_TYPE:
        case CustomerOptionCategory.BUDGET_RANGE:
        case CustomerOptionCategory.CONTACT_METHOD:
        case CustomerOptionCategory.FOLLOW_STATUS:
        case CustomerOptionCategory.DEAL_STATUS:
        case CustomerOptionCategory.PAYMENT_METHOD:
        case CustomerOptionCategory.REGION:
          // 使用新的API函数获取选项数据（/api/options/，返回数组）
          const { getOptionsByCategory } = await import('@/api/customerOptions')
          response = await getOptionsByCategory(categoryCode)
          break
        default:
          // 使用管理端API（/api/options-management/，返回分页对象）
          response = await getOptionItems({ ...params, category_code: categoryCode })
      }
      
      if (response.success && response.data) {
        // 兼容两种数据结构：
        // 1) 新接口 /api/options/ -> data: OptionItem[]
        // 2) 管理接口 /api/options-management/ -> data: { items: OptionItem[]; ... }
        const responseItems = Array.isArray(response.data)
          ? (response.data as OptionItem[])
          : ((response.data.items ?? []) as OptionItem[])

        itemsMap.value[categoryCode] = responseItems
        lastUpdateMap.value[categoryCode] = Date.now()
        
        // 保存到缓存（仅在无搜索和筛选条件时）
        if (!params?.keyword && params?.enabled !== false) {
          saveToCache(categoryCode, responseItems)
        }
      } else {
        throw new Error(response.message || `加载${categoryCode}数据失败`)
      }
    } catch (err: any) {
      console.error(`加载${categoryCode}选项数据失败:`, err)
      error.value = err.message || `加载${categoryCode}数据失败`
      itemsMap.value[categoryCode] = []
    } finally {
      loadingMap.value[categoryCode] = false
    }
  }

  /**
   * 加载所有客户参数选项
   */
  const loadAllCustomerOptions = async (): Promise<void> => {
    const categoryPromises = Object.values(CustomerOptionCategory).map(categoryCode =>
      loadCategoryItems(categoryCode, { categoryCode, enabled: true })
    )
    
    await Promise.allSettled(categoryPromises)
  }

  /**
   * 刷新指定分类的数据
   */
  const refreshCategoryItems = async (
    categoryCode: string,
    params?: QueryOptionItemsParams
  ): Promise<void> => {
    clearCategoryCache(categoryCode)
    await loadCategoryItems(categoryCode, params, false)
  }

  /**
   * 刷新所有数据
   */
  const refreshAllData = async (): Promise<void> => {
    clearAllCache()
    await Promise.all([
      loadCategories(),
      loadAllCustomerOptions()
    ])
  }

  /**
   * 加载选项数据（对外接口）
   */
  const loadOptions = async (): Promise<void> => {
    await loadAllCustomerOptions()
  }

  // ==================== 数据操作 ====================

  /**
   * 创建选项数据
   */
  const createItem = async (
    categoryCode: string,
    data: CreateOptionItemRequest
  ): Promise<boolean> => {
    try {
      const response = await createOptionItem(data)
      
      if (response.success) {
        // 刷新对应分类的数据
        await refreshCategoryItems(categoryCode)
        return true
      } else {
        error.value = response.message || '创建失败'
        return false
      }
    } catch (err: any) {
      console.error('创建选项数据失败:', err)
      error.value = err.message || '创建失败'
      return false
    }
  }

  /**
   * 更新选项数据
   */
  const updateItem = async (
    categoryCode: string,
    id: string,
    data: UpdateOptionItemRequest
  ): Promise<boolean> => {
    try {
      const response = await updateOptionItem(id, data)
      
      if (response.success) {
        // 刷新对应分类的数据
        await refreshCategoryItems(categoryCode)
        return true
      } else {
        error.value = response.message || '更新失败'
        return false
      }
    } catch (err: any) {
      console.error('更新选项数据失败:', err)
      error.value = err.message || '更新失败'
      return false
    }
  }

  /**
   * 删除选项数据
   */
  const deleteItem = async (
    categoryCode: string,
    id: string
  ): Promise<boolean> => {
    try {
      const response = await deleteOptionItem(id)
      
      if (response.success) {
        // 刷新对应分类的数据
        await refreshCategoryItems(categoryCode)
        return true
      } else {
        error.value = response.message || '删除失败'
        return false
      }
    } catch (err: any) {
      console.error('删除选项数据失败:', err)
      error.value = err.message || '删除失败'
      return false
    }
  }

  /**
   * 批量删除选项数据
   */
  const batchDeleteItems = async (
    categoryCode: string,
    ids: string[]
  ): Promise<boolean> => {
    try {
      const response = await batchDeleteOptionItems(ids)
      
      if (response.success) {
        // 刷新对应分类的数据
        await refreshCategoryItems(categoryCode)
        return true
      } else {
        error.value = response.message || '批量删除失败'
        return false
      }
    } catch (err: any) {
      console.error('批量删除选项数据失败:', err)
      error.value = err.message || '批量删除失败'
      return false
    }
  }

  // ==================== 工具方法 ====================

  /**
   * 获取指定分类的选项数据
   */
  const getCategoryItems = (categoryCode: string): OptionItem[] => {
    return itemsMap.value[categoryCode] || []
  }

  /**
   * 获取指定分类的加载状态
   */
  const getCategoryLoading = (categoryCode: string): boolean => {
    return loadingMap.value[categoryCode] || false
  }

  /**
   * 检查分类数据是否已加载
   */
  const isCategoryLoaded = (categoryCode: string): boolean => {
    return !!itemsMap.value[categoryCode] && itemsMap.value[categoryCode].length >= 0
  }

  /**
   * 获取分类信息
   */
  const getCategoryInfo = (categoryCode: string) => {
    return CUSTOMER_OPTION_CATEGORIES[categoryCode as keyof typeof CUSTOMER_OPTION_CATEGORIES] || null
  }

  /**
   * 根据ID查找选项数据
   */
  const findItemById = (categoryCode: string, id: string): OptionItem | null => {
    const items = getCategoryItems(categoryCode)
    return items.find(item => item.id === id) || null
  }

  /**
   * 根据代码查找选项数据
   */
  const findItemByCode = (categoryCode: string, code: string): OptionItem | null => {
    const items = getCategoryItems(categoryCode)
    return items.find(item => item.code === code) || null
  }

  /**
   * 获取启用的选项数据
   */
  const getActiveItems = (categoryCode: string): OptionItem[] => {
    const items = getCategoryItems(categoryCode)
    return items.filter(item => item.enabled)
  }

  /**
   * 根据值获取选项标签
   */
  const getOptionLabelByValue = (categoryCode: string, value: string): string => {
    const item = findItemByCode(categoryCode, value)
    return item?.name || value
  }

  /**
   * 转换为Select组件选项格式
   */
  const toSelectOptions = (categoryCode: string) => {
    const items = getActiveItems(categoryCode)
    return items.map(item => ({
      label: item.name,
      value: item.code
    }))
  }

  /**
   * 获取指定分类的选项数据（对外接口）
   */
  const getOptionsByCategory = (categoryCode: string): OptionItem[] => {
    return getCategoryItems(categoryCode)
  }

  /**
   * 清除错误信息
   */
  const clearError = () => {
    error.value = null
  }

  /**
   * 重置所有状态
   */
  const resetState = () => {
    categories.value = []
    itemsMap.value = {}
    loadingMap.value = {}
    lastUpdateMap.value = {}
    globalLoading.value = false
    error.value = null
    clearAllCache()
  }

  // ==================== 返回状态和方法 ====================

  return {
    // 状态
    categories,
    itemsMap,
    loadingMap,
    lastUpdateMap,
    globalLoading,
    error,
    
    // 计算属性
    customerSources,
    customerLevels,
    customerStatuses,
    customerTags,
    allCustomerOptions,
    isAnyLoading,
    loadedCategoriesCount,
    
    // 数据加载
    loadCategories,
    loadCategoryItems,
    loadAllCustomerOptions,
    loadOptions,
    refreshCategoryItems,
    refreshAllData,
    
    // 数据操作
    createItem,
    updateItem,
    deleteItem,
    batchDeleteItems,
    
    // 工具方法
    getCategoryItems,
    getCategoryLoading,
    isCategoryLoaded,
    getCategoryInfo,
    findItemById,
    findItemByCode,
    getActiveItems,
    getOptionLabelByValue,
    toSelectOptions,
    getOptionsByCategory,
    clearError,
    resetState,
    
    // 缓存管理
    clearCategoryCache,
    clearAllCache
  }
})

// ==================== 类型导出 ====================

export type CustomerOptionsStore = ReturnType<typeof useCustomerOptionsStore>

// ==================== 默认导出 ====================

export default useCustomerOptionsStore