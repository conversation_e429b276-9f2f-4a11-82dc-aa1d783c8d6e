<template>
  <div class="item-manager">
    <!-- 操作栏 -->
    <div class="action-bar">
      <n-space justify="space-between">
        <n-space>
          <n-select
            v-model:value="selectedCategoryCode"
            placeholder="选择分类"
            clearable
            style="width: 200px"
            :options="categoryOptions"
            @update:value="handleCategoryChange"
          />
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索选项名称或值"
            clearable
            style="width: 300px"
            @input="handleSearch"
          >
            <template #prefix>
              <n-icon :component="SearchOutline" />
            </template>
          </n-input>
          <n-select
            v-model:value="statusFilter"
            placeholder="状态筛选"
            clearable
            style="width: 120px"
            :options="statusOptions"
            @update:value="handleSearch"
          />
        </n-space>
        <n-space>
          <n-button type="primary" @click="handleCreate" :disabled="!selectedCategoryCode">
            <template #icon>
              <n-icon :component="AddOutline" />
            </template>
            新增选项
          </n-button>
          <n-button @click="handleRefresh">
            <template #icon>
              <n-icon :component="RefreshOutline" />
            </template>
            刷新
          </n-button>
        </n-space>
      </n-space>
    </div>

    <!-- 批量操作栏 -->
    <div v-if="checkedRowKeys.length > 0" class="batch-action-bar">
      <n-space>
        <span>已选择 {{ checkedRowKeys.length }} 项</span>
        <n-button size="small" type="success" @click="handleBatchEnable">
          <template #icon>
            <n-icon :component="CheckmarkOutline" />
          </template>
          批量启用
        </n-button>
        <n-button size="small" type="warning" @click="handleBatchDisable">
          <template #icon>
            <n-icon :component="CloseOutline" />
          </template>
          批量禁用
        </n-button>
        <n-popconfirm @positive-click="handleBatchDelete">
          <template #trigger>
            <n-button size="small" type="error">
              <template #icon>
                <n-icon :component="TrashOutline" />
              </template>
              批量删除
            </n-button>
          </template>
          确定删除选中的 {{ checkedRowKeys.length }} 个选项吗？
        </n-popconfirm>
        <n-button size="small" @click="checkedRowKeys = []">
          取消选择
        </n-button>
      </n-space>
    </div>

    <!-- 数据表格 -->
    <n-data-table
      :columns="columns"
      :data="filteredTableData"
      :loading="loading"
      :pagination="paginationReactive"
      :row-key="(row: OptionItem) => row.id"
      v-model:checked-row-keys="checkedRowKeys"
      @update:page="handlePageChange"
      @update:page-size="handlePageSizeChange"
    />

    <!-- 创建/编辑弹窗 -->
    <n-modal v-model:show="showModal" preset="dialog" title="选项信息" style="width: 600px">
      <template #header>
        <span>{{ isEdit ? '编辑选项' : '新增选项' }}</span>
      </template>
      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="所属分类" path="category_id">
          <n-select
            v-model:value="formData.category_id"
            placeholder="请选择分类"
            :options="categoryOptions"
            :disabled="isEdit"
          />
        </n-form-item>
        <n-form-item label="选项代码" path="code">
          <n-input
            v-model:value="formData.code"
            placeholder="请输入选项代码（英文）"
            :disabled="isEdit"
          />
        </n-form-item>
        <n-form-item label="选项值" path="value">
          <n-input v-model:value="formData.value" placeholder="请输入选项值" />
        </n-form-item>
        <n-form-item label="显示标签" path="label">
          <n-input v-model:value="formData.label" placeholder="请输入显示标签" />
        </n-form-item>
        <n-form-item label="描述" path="description">
          <n-input
            v-model:value="formData.description"
            type="textarea"
            placeholder="请输入选项描述"
            :autosize="{ minRows: 2, maxRows: 4 }"
          />
        </n-form-item>

        <n-grid :cols="2" :x-gap="16">
          <n-grid-item>
            <n-form-item label="排序" path="sort_order">
              <n-input-number
                v-model:value="formData.sort_order"
                placeholder="排序值"
                :min="0"
                style="width: 100%"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="状态" path="is_active">
              <n-switch v-model:value="formData.is_active">
                <template #checked>启用</template>
                <template #unchecked>禁用</template>
              </n-switch>
            </n-form-item>
          </n-grid-item>
        </n-grid>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showModal = false">取消</n-button>
          <n-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, h } from 'vue'
import {
  NSpace,
  NInput,
  NSelect,
  NButton,
  NDataTable,
  NModal,
  NForm,
  NFormItem,
  NInputNumber,
  NSwitch,
  NIcon,
  NTag,
  NPopconfirm,
  NGrid,
  NGridItem,
  useMessage,
  type DataTableColumns,
  type FormInst,
  type FormRules
} from 'naive-ui'
import {
  SearchOutline,
  AddOutline,
  RefreshOutline,
  CreateOutline,
  TrashOutline,
  CheckmarkOutline,
  CloseOutline
} from '@vicons/ionicons5'
import { useOptionsStore } from '@/stores/optionsStore'
import type {
  OptionItem,
  OptionCategory,
  CreateOptionItemRequest,
  UpdateOptionItemRequest
} from '@/types/options'

// 消息提示
const message = useMessage()

// Options Store
const optionsStore = useOptionsStore()

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const statusFilter = ref<string | null>(null)
const selectedCategoryCode = ref<string>('')
const tableData = ref<OptionItem[]>([])
const checkedRowKeys = ref<string[]>([])
const showModal = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const formRef = ref<FormInst | null>(null)
const currentEditId = ref<string>('')

// 分页配置
const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true,
  itemCount: 0
})

// 状态筛选选项
const statusOptions = [
  { label: '启用', value: 'true', type: 'option' as const },
  { label: '禁用', value: 'false', type: 'option' as const }
]

// 分类选项
const categoryOptions = computed(() => {
  return optionsStore.categories.map(cat => ({
    label: cat.name,
    value: cat.id
  }))
})

// 表单数据
const formData = ref<CreateOptionItemRequest>({
  category_id: '',
  code: '',
  value: '',
  label: '',
  description: '',
  sort_order: 0,
  is_active: true
})

// 表单验证规则
const formRules: FormRules = {
  category_id: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  code: [
    { required: true, message: '请输入选项代码', trigger: 'blur' },
    { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '代码只能包含字母、数字和下划线，且以字母或下划线开头', trigger: 'blur' }
  ],
  value: [
    { required: true, message: '请输入选项值', trigger: 'blur' }
  ],
  label: [
    { required: true, message: '请输入显示标签', trigger: 'blur' }
  ],
  sort_order: [
    { required: true, message: '请输入排序值', trigger: 'blur', type: 'number' }
  ]
}

// 表格列配置
const columns: DataTableColumns<OptionItem> = [
  {
    type: 'selection'
  },
  {
    title: '选项代码',
    key: 'code',
    width: 120
  },
  {
    title: '选项值',
    key: 'value',
    width: 120
  },
  {
    title: '显示标签',
    key: 'label',
    width: 120
  },
  {
    title: '描述',
    key: 'description',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '排序',
    key: 'sort_order',
    width: 80,
    sorter: true
  },
  {
    title: '状态',
    key: 'is_active',
    width: 80,
    render(row) {
      return h(
        NTag,
        {
          type: row.is_active ? 'success' : 'error'
        },
        {
          default: () => (row.is_active ? '启用' : '禁用')
        }
      )
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 180,
    render(row) {
      return new Date(row.created_at).toLocaleString()
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render(row) {
      return h(NSpace, null, {
        default: () => [
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              ghost: true,
              onClick: () => handleEdit(row)
            },
            {
              default: () => '编辑',
              icon: () => h(NIcon, { component: CreateOutline })
            }
          ),
          h(
            NPopconfirm,
            {
              onPositiveClick: () => handleDelete(row.id)
            },
            {
              default: () => '确定删除此选项吗？',
              trigger: () =>
                h(
                  NButton,
                  {
                    size: 'small',
                    type: 'error',
                    ghost: true
                  },
                  {
                    default: () => '删除',
                    icon: () => h(NIcon, { component: TrashOutline })
                  }
                )
            }
          )
        ]
      })
    }
  }
]

// 过滤后的表格数据
const filteredTableData = computed(() => {
  let data = tableData.value
  
  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    data = data.filter(item => 
      item.value.toLowerCase().includes(keyword) ||
      item.label.toLowerCase().includes(keyword) ||
      item.code.toLowerCase().includes(keyword)
    )
  }
  
  // 状态过滤
  if (statusFilter.value !== null) {
    const filterValue = statusFilter.value === 'true'
    data = data.filter(item => item.is_active === filterValue)
  }
  
  return data
})

// 获取数据
const fetchData = async () => {
  if (!selectedCategoryCode.value) {
    tableData.value = []
    paginationReactive.itemCount = 0
    return
  }

  try {
    loading.value = true
    
    // 找到对应的分类
    const currentCategory = optionsStore.categories.find(cat => cat.id === selectedCategoryCode.value)
    if (!currentCategory) {
      tableData.value = []
      paginationReactive.itemCount = 0
      return
    }
    
    // 使用分类的code来获取选项项
    const response = await optionsStore.fetchItemsByCategoryCode(currentCategory.code)
    
    // 过滤当前分类的数据
    tableData.value = optionsStore.items.filter(item => item.category_id === currentCategory.id)
    paginationReactive.itemCount = tableData.value.length
  } catch (error) {
    message.error('获取选项数据失败')
    console.error('获取选项数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 分类变化处理
const handleCategoryChange = () => {
  paginationReactive.page = 1
  fetchData()
}

// 搜索处理
const handleSearch = () => {
  paginationReactive.page = 1
  fetchData()
}

// 刷新数据
const handleRefresh = () => {
  fetchData()
}

// 分页处理
const handlePageChange = (page: number) => {
  paginationReactive.page = page
  fetchData()
}

const handlePageSizeChange = (pageSize: number) => {
  paginationReactive.pageSize = pageSize
  paginationReactive.page = 1
  fetchData()
}

// 新增选项
const handleCreate = () => {
  isEdit.value = false
  formData.value = {
    category_id: selectedCategoryCode.value,
    code: '',
    value: '',
    label: '',
    description: '',
    sort_order: 0,
    is_active: true,
    color: '',
    icon: ''
  }
  showModal.value = true
}

// 编辑选项
const handleEdit = (row: OptionItem) => {
  isEdit.value = true
  currentEditId.value = row.id
  formData.value = {
    category_id: row.category_id,
    code: row.code,
    value: row.value,
    label: row.label,
    description: row.description || '',
    sort_order: row.sort_order,
    is_active: row.is_active,
    color: row.color || '',
    icon: row.icon || ''
  }
  showModal.value = true
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    if (isEdit.value) {
      await optionsStore.updateItem(currentEditId.value, formData.value as UpdateOptionItemRequest)
      message.success('选项更新成功')
    } else {
      await optionsStore.createItem(formData.value)
      message.success('选项创建成功')
    }
    
    showModal.value = false
    await fetchData()
  } catch (error) {
    message.error(isEdit.value ? '选项更新失败' : '选项创建失败')
    console.error('提交失败:', error)
  } finally {
    submitting.value = false
  }
}

// 删除选项
const handleDelete = async (id: string) => {
  try {
    await optionsStore.deleteItem(id)
    message.success('选项删除成功')
    await fetchData()
  } catch (error) {
    message.error('选项删除失败')
    console.error('删除失败:', error)
  }
}

// 批量启用
const handleBatchEnable = async () => {
  try {
    const ids = checkedRowKeys.value as string[]
    await optionsStore.batchUpdateItemsStatus(ids, true)
    message.success(`成功启用 ${ids.length} 个选项`)
    checkedRowKeys.value = []
    await fetchData()
  } catch (error) {
    message.error('批量启用失败')
    console.error('批量启用失败:', error)
  }
}

// 批量禁用
const handleBatchDisable = async () => {
  try {
    const ids = checkedRowKeys.value as string[]
    await optionsStore.batchUpdateItemsStatus(ids, false)
    message.success(`成功禁用 ${ids.length} 个选项`)
    checkedRowKeys.value = []
    await fetchData()
  } catch (error) {
    message.error('批量禁用失败')
    console.error('批量禁用失败:', error)
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    const ids = checkedRowKeys.value as string[]
    await optionsStore.batchDeleteItems(ids)
    message.success(`成功删除 ${ids.length} 个选项`)
    checkedRowKeys.value = []
    await fetchData()
  } catch (error) {
    message.error('批量删除失败')
    console.error('批量删除失败:', error)
  }
}

// 生命周期
onMounted(async () => {
  // 先加载分类数据
  await optionsStore.fetchCategories()
})
</script>

<style scoped>
.item-manager {
  padding: 16px;
}

.action-bar {
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.batch-action-bar {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 8px;
  color: #0369a1;
}
</style>