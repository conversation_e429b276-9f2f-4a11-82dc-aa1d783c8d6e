import { request } from '@/utils/request'
import { mockCampaigns, mockParticipants } from '@/mock/marketingData'

// API 响应解包工具函数
const unwrapResponse = <T>(response: any): T => {
  // 后端返回格式: {success: boolean, data: T}
  if (response.data && response.data.success !== undefined) {
    return response.data.data || response.data
  }
  return response.data
}

// 开发环境模拟延迟
const mockDelay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms))

// 判断是否为开发环境
const isDev = import.meta.env.DEV

// 营销活动接口
export interface Campaign {
  id?: number
  name: string
  type: string
  description?: string
  status: string
  start_time: string
  end_time: string
  target_audience?: string
  budget?: number
  participants_count?: number
  conversion_rate?: number
  created_at?: string
  updated_at?: string
  config?: any
}

// 活动参与者接口
export interface Participant {
  id: number
  campaign_id: number
  customer_id: number
  customer_name: string
  customer_phone: string
  customer_wechat?: string
  participation_time: string
  status: string
  result?: string
  reward?: string
  notes?: string
}

// 活动分享记录接口
export interface CampaignShare {
  id: number
  campaign_id: number
  customer_id: number
  share_platform: string
  share_time: string
  share_content?: string
  click_count: number
  conversion_count: number
}

// 查询参数接口
export interface CampaignQuery {
  page?: number
  pageSize?: number
  search?: string
  type?: string
  status?: string
  start_date?: string
  end_date?: string
}

export interface ParticipantQuery {
  page?: number
  pageSize?: number
  search?: string
  status?: string
  start_date?: string
  end_date?: string
}

// 营销活动服务
export const marketingService = {
  // 获取活动列表
  async getCampaigns(params: CampaignQuery = {}) {
    if (isDev) {
      await mockDelay()
      let filteredCampaigns = [...mockCampaigns]
      
      // 模拟搜索过滤
      if (params.search) {
        filteredCampaigns = filteredCampaigns.filter(campaign => 
          campaign.name.includes(params.search!) || 
          campaign.description?.includes(params.search!)
        )
      }
      
      // 模拟类型过滤
      if (params.type) {
        filteredCampaigns = filteredCampaigns.filter(campaign => campaign.type === params.type)
      }
      
      // 模拟状态过滤
      if (params.status) {
        filteredCampaigns = filteredCampaigns.filter(campaign => campaign.status === params.status)
      }
      
      // 模拟分页
      const page = params.page || 1
      const pageSize = params.pageSize || 20
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const items = filteredCampaigns.slice(start, end)
      
      return {
        data: items,
        total: filteredCampaigns.length,
        page,
        pageSize
      }
    }
    const response = await request.get('/api/campaigns', { params })
    return unwrapResponse(response)
  },

  // 获取活动详情
  async getCampaign(id: number) {
    return request.get(`/api/campaigns/${id}`)
  },

  // 创建活动
  async createCampaign(data: Campaign) {
    return request.post('/api/campaigns', data)
  },

  // 更新活动
  async updateCampaign(id: number, data: Partial<Campaign>) {
    return request.put(`/api/campaigns/${id}`, data)
  },

  // 删除活动
  async deleteCampaign(id: number) {
    return request.delete(`/api/campaigns/${id}`)
  },

  // 启动活动
  async startCampaign(id: number) {
    return request.post(`/api/campaigns/${id}/start`)
  },

  // 暂停活动
  async pauseCampaign(id: number) {
    return request.post(`/api/campaigns/${id}/pause`)
  },

  // 结束活动
  async endCampaign(id: number) {
    return request.post(`/api/campaigns/${id}/end`)
  },

  // 获取活动参与者
  async getParticipants(campaignId: number, params: ParticipantQuery = {}) {
    if (isDev) {
      await mockDelay()
      let filteredParticipants = mockParticipants.filter(p => p.campaign_id === campaignId)
      
      // 模拟搜索过滤
      if (params.search) {
        filteredParticipants = filteredParticipants.filter(participant => 
          participant.customer_name.includes(params.search!) || 
          participant.customer_phone.includes(params.search!)
        )
      }
      
      // 模拟状态过滤
      if (params.status) {
        filteredParticipants = filteredParticipants.filter(participant => participant.status === params.status)
      }
      
      // 模拟分页
      const page = params.page || 1
      const pageSize = params.pageSize || 20
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const items = filteredParticipants.slice(start, end)
      
      return {
        data: items,
        total: filteredParticipants.length,
        page,
        pageSize
      }
    }
    const response = await request.get(`/api/campaigns/${campaignId}/participants`, { params })
    return unwrapResponse(response)
  },

  // 添加参与者
  async addParticipant(campaignId: number, data: Partial<Participant>) {
    return request.post(`/api/campaigns/${campaignId}/participants`, data)
  },

  // 更新参与者状态
  async updateParticipant(campaignId: number, participantId: number, data: Partial<Participant>) {
    return request.put(`/api/campaigns/${campaignId}/participants/${participantId}`, data)
  },

  // 删除参与者
  async deleteParticipant(campaignId: number, participantId: number) {
    return request.delete(`/api/campaigns/${campaignId}/participants/${participantId}`)
  },

  // 获取活动分享记录
  async getCampaignShares(campaignId: number, params: any = {}) {
    return request.get(`/api/campaigns/${campaignId}/shares`, { params })
  },

  // 记录分享
  async recordShare(data: Partial<CampaignShare>) {
    return request.post('/api/campaign-shares', data)
  },

  // 获取活动统计数据
  async getCampaignStats(campaignId: number) {
    return request.get(`/api/campaigns/${campaignId}/stats`)
  },

  // 获取活动数据趋势
  async getCampaignTrends(campaignId: number, params: any = {}) {
    return request.get(`/api/campaigns/${campaignId}/trends`, { params })
  },

  // 导出活动数据
  async exportCampaignData(campaignId: number, type: string = 'participants') {
    return request.get(`/api/campaigns/${campaignId}/export`, {
      params: { type },
      responseType: 'blob'
    })
  },

  // 批量操作活动
  async batchUpdateCampaigns(ids: number[], action: string, data?: any) {
    return request.post('/api/campaigns/batch', {
      ids,
      action,
      data
    })
  },

  // 复制活动
  async copyCampaign(id: number, data: Partial<Campaign>) {
    return request.post(`/api/campaigns/${id}/copy`, data)
  },

  // 预览活动
  async previewCampaign(data: Campaign) {
    return request.post('/api/campaigns/preview', data)
  },

  // 获取活动模板
  async getCampaignTemplates(type?: string) {
    return request.get('/api/campaign-templates', {
      params: { type }
    })
  },

  // 从模板创建活动
  async createFromTemplate(templateId: number, data: Partial<Campaign>) {
    return request.post(`/api/campaign-templates/${templateId}/create`, data)
  },

  // 抽奖相关接口
  lottery: {
    // 执行抽奖
    async draw(campaignId: number, participantId: number) {
      return request.post(`/api/campaigns/${campaignId}/lottery/draw`, {
        participant_id: participantId
      })
    },

    // 获取中奖记录
    async getWinners(campaignId: number, params: any = {}) {
      return request.get(`/api/campaigns/${campaignId}/lottery/winners`, { params })
    },

    // 发放奖品
    async deliverPrize(campaignId: number, winnerId: number, data: any) {
      return request.post(`/api/campaigns/${campaignId}/lottery/deliver`, {
        winner_id: winnerId,
        ...data
      })
    }
  },

  // 秒杀相关接口
  flashSale: {
    // 参与秒杀
    async participate(campaignId: number, data: any) {
      return request.post(`/api/campaigns/${campaignId}/flash-sale/participate`, data)
    },

    // 获取秒杀订单
    async getOrders(campaignId: number, params: any = {}) {
      return request.get(`/api/campaigns/${campaignId}/flash-sale/orders`, { params })
    },

    // 更新库存
    async updateStock(campaignId: number, quantity: number) {
      return request.put(`/api/campaigns/${campaignId}/flash-sale/stock`, {
        quantity
      })
    }
  },

  // 优惠券相关接口
  coupon: {
    // 领取优惠券
    async claim(campaignId: number, customerId: number) {
      return request.post(`/api/campaigns/${campaignId}/coupon/claim`, {
        customer_id: customerId
      })
    },

    // 使用优惠券
    async use(campaignId: number, couponId: number, orderId: number) {
      return request.post(`/api/campaigns/${campaignId}/coupon/use`, {
        coupon_id: couponId,
        order_id: orderId
      })
    },

    // 获取优惠券使用记录
    async getUsageRecords(campaignId: number, params: any = {}) {
      return request.get(`/api/campaigns/${campaignId}/coupon/usage`, { params })
    }
  },

  // 积分活动相关接口
  points: {
    // 获得积分
    async earn(campaignId: number, customerId: number, action: string, amount: number) {
      return request.post(`/api/campaigns/${campaignId}/points/earn`, {
        customer_id: customerId,
        action,
        amount
      })
    },

    // 消费积分
    async spend(campaignId: number, customerId: number, amount: number, reason: string) {
      return request.post(`/api/campaigns/${campaignId}/points/spend`, {
        customer_id: customerId,
        amount,
        reason
      })
    },

    // 获取积分记录
    async getRecords(campaignId: number, customerId?: number, params: any = {}) {
      return request.get(`/api/campaigns/${campaignId}/points/records`, {
        params: { customer_id: customerId, ...params }
      })
    }
  }
}

export default marketingService