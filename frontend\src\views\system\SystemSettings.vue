<template>
  <div class="system-settings">
    <div class="page-header">
      <h1 class="page-title">系统设置</h1>
      <p class="page-description">管理系统选项数据，包括客户来源、客户等级、装修类型等配置项</p>
    </div>

    <div class="settings-content">
      <n-card class="settings-card">
        <template #header>
          <div class="card-header">
            <n-icon size="20" class="header-icon">
              <SettingsOutline />
            </n-icon>
            <span>选项数据管理</span>
          </div>
        </template>

        <div class="options-management">
          <!-- 选项分类列表 -->
          <div class="categories-section">
            <div class="section-header">
              <h3>选项分类</h3>
              <n-button type="primary" @click="showAddCategoryModal = true">
                <template #icon>
                  <n-icon><AddOutline /></n-icon>
                </template>
                新增分类
              </n-button>
            </div>

            <n-data-table
              :columns="categoryColumns"
              :data="categories"
              :loading="categoriesLoading"
              :pagination="false"
              size="small"
              class="categories-table"
            />
          </div>

          <!-- 选项项列表 -->
          <div class="items-section" v-if="selectedCategory">
            <div class="section-header">
              <h3>{{ selectedCategory.name }} - 选项项</h3>
              <n-button type="primary" @click="showAddItemModal = true">
                <template #icon>
                  <n-icon><AddOutline /></n-icon>
                </template>
                新增选项
              </n-button>
            </div>

            <n-data-table
              :columns="itemColumns"
              :data="items"
              :loading="itemsLoading"
              :pagination="false"
              size="small"
              class="items-table"
            />
          </div>
        </div>
      </n-card>
    </div>

    <!-- 新增/编辑分类模态框 -->
    <n-modal v-model:show="showAddCategoryModal" preset="dialog" title="新增分类">
      <n-form ref="categoryFormRef" :model="categoryForm" :rules="categoryRules">
        <n-form-item label="分类名称" path="name">
          <n-input v-model:value="categoryForm.name" placeholder="请输入分类名称" />
        </n-form-item>
        <n-form-item label="分类代码" path="code">
          <n-input v-model:value="categoryForm.code" placeholder="请输入分类代码" />
        </n-form-item>
        <n-form-item label="描述" path="description">
          <n-input
            v-model:value="categoryForm.description"
            type="textarea"
            placeholder="请输入分类描述"
            :rows="3"
          />
        </n-form-item>
      </n-form>
      <template #action>
          <n-space>
            <n-button @click="showAddCategoryModal = false" :disabled="categorySaving">取消</n-button>
            <n-button type="primary" @click="handleSaveCategory" :loading="categorySaving">保存</n-button>
          </n-space>
        </template>
    </n-modal>

    <!-- 新增/编辑选项项模态框 -->
    <n-modal v-model:show="showAddItemModal" preset="dialog" title="新增选项项">
      <n-form ref="itemFormRef" :model="itemForm" :rules="itemRules">
        <n-form-item label="选项名称" path="label">
          <n-input v-model:value="itemForm.label" placeholder="请输入选项名称" />
        </n-form-item>
        <n-form-item label="选项值" path="value">
          <n-input v-model:value="itemForm.value" placeholder="请输入选项值" />
        </n-form-item>
        <n-form-item label="描述" path="description">
          <n-input
            v-model:value="itemForm.description"
            type="textarea"
            placeholder="请输入选项描述"
            :rows="3"
          />
        </n-form-item>
        <n-form-item label="排序" path="sort_order">
          <n-input-number v-model:value="itemForm.sort_order" :min="0" placeholder="排序值" />
        </n-form-item>
      </n-form>
      <template #action>
          <n-space>
            <n-button @click="showAddItemModal = false" :disabled="itemSaving">取消</n-button>
            <n-button type="primary" @click="handleSaveItem" :loading="itemSaving">保存</n-button>
          </n-space>
        </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, h } from 'vue'
import { useMessage, useDialog } from 'naive-ui'
import { SettingsOutline, AddOutline, CreateOutline, TrashOutline } from '@vicons/ionicons5'
import { useOptionsStore } from '@/stores/optionsStore'
import { optionCategoriesApi, optionItemsApi } from '@/api/options'
import type { OptionCategory, OptionItem } from '@/types/options'

const message = useMessage()
const dialog = useDialog()
const optionsStore = useOptionsStore()

// 表单引用
const categoryFormRef = ref()
const itemFormRef = ref()

// 响应式数据
const categoriesLoading = ref(false)
const itemsLoading = ref(false)
const categorySaving = ref(false)
const itemSaving = ref(false)
const deletingCategoryId = ref<string | null>(null)
const deletingItemId = ref<string | null>(null)
const togglingItemId = ref<string | null>(null)
const categories = ref<OptionCategory[]>([])
const items = ref<OptionItem[]>([])
const selectedCategory = ref<OptionCategory | null>(null)

// 模态框状态
const showAddCategoryModal = ref(false)
const showAddItemModal = ref(false)

// 表单数据
const categoryForm = reactive({
  name: '',
  code: '',
  description: ''
})

const itemForm = reactive({
  label: '',
  value: '',
  description: '',
  sort_order: 0
})

// 表单验证规则
const categoryRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 50, message: '分类名称长度应在2-50个字符之间', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入分类代码', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '分类代码必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' },
    { min: 2, max: 30, message: '分类代码长度应在2-30个字符之间', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '描述长度不能超过200个字符', trigger: 'blur' }
  ]
}

const itemRules = {
  label: [
    { required: true, message: '请输入选项名称', trigger: 'blur' },
    { min: 1, max: 50, message: '选项名称长度应在1-50个字符之间', trigger: 'blur' }
  ],
  value: [
    { required: true, message: '请输入选项值', trigger: 'blur' },
    { min: 1, max: 100, message: '选项值长度应在1-100个字符之间', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '描述长度不能超过200个字符', trigger: 'blur' }
  ],
  sort_order: [
    { type: 'number', min: 0, max: 9999, message: '排序值应在0-9999之间', trigger: 'blur' }
  ]
}

// 分类表格列配置
const categoryColumns = [
  {
    title: '分类名称',
    key: 'name',
    width: 150
  },
  {
    title: '分类代码',
    key: 'code',
    width: 150
  },
  {
    title: '描述',
    key: 'description',
    ellipsis: true
  },
  {
    title: '选项数量',
    key: 'item_count',
    width: 100,
    render: (row: OptionCategory) => 0
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    render: (row: OptionCategory) => {
      return [
        h('n-button', {
          size: 'small',
          type: 'primary',
          ghost: true,
          onClick: () => handleSelectCategory(row)
        }, { default: () => '管理选项' }),
        h('n-button', {
          size: 'small',
          type: 'error',
          ghost: true,
          loading: deletingCategoryId.value === row.id,
          disabled: deletingCategoryId.value === row.id,
          style: { marginLeft: '8px' },
          onClick: () => handleDeleteCategory(row)
        }, { default: () => '删除' })
      ]
    }
  }
]

// 选项项表格列配置
const itemColumns = [
  {
    title: '选项名称',
    key: 'label',
    width: 150
  },
  {
    title: '选项值',
    key: 'value',
    width: 150
  },
  {
    title: '描述',
    key: 'description',
    ellipsis: true
  },
  {
    title: '排序',
    key: 'sort_order',
    width: 80
  },
  {
    title: '状态',
    key: 'is_active',
    width: 80,
    render: (row: OptionItem) => {
      return h('n-tag', {
        type: row.is_active ? 'success' : 'default'
      }, { default: () => row.is_active ? '启用' : '禁用' })
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render: (row: OptionItem) => {
      return [
        h('n-button', {
          size: 'small',
          type: 'primary',
          ghost: true,
          loading: togglingItemId.value === row.id,
          disabled: togglingItemId.value === row.id,
          onClick: () => handleToggleItemStatus(row)
        }, { default: () => row.is_active ? '禁用' : '启用' }),
        h('n-button', {
          size: 'small',
          type: 'error',
          ghost: true,
          loading: deletingItemId.value === row.id,
          disabled: deletingItemId.value === row.id,
          style: { marginLeft: '8px' },
          onClick: () => handleDeleteItem(row)
        }, { default: () => '删除' })
      ]
    }
  }
]

// 方法
const loadCategories = async () => {
  try {
    categoriesLoading.value = true
    const response = await optionCategoriesApi.getList()
    categories.value = response.data || []
  } catch (error) {
    console.error('加载分类列表失败:', error)
    message.error('加载分类列表失败')
  } finally {
    categoriesLoading.value = false
  }
}

const loadItems = async (categoryId: string) => {
  try {
    itemsLoading.value = true
    const response = await optionItemsApi.getByCategory(categoryId)
    items.value = response || []
  } catch (error) {
    console.error('加载选项项列表失败:', error)
    message.error('加载选项项列表失败')
  } finally {
    itemsLoading.value = false
  }
}

const handleSelectCategory = (category: OptionCategory) => {
  selectedCategory.value = category
  loadItems(category.id)
}

const handleSaveCategory = async () => {
  if (categorySaving.value) return
  
  try {
    categorySaving.value = true
    
    // 表单验证
    await categoryFormRef.value?.validate()
    
    // 检查分类代码是否重复
    const existingCategory = categories.value.find(cat => cat.code === categoryForm.code)
    if (existingCategory) {
      message.error('分类代码已存在，请使用其他代码')
      return
    }
    
    const categoryData = {
      name: categoryForm.name,
      code: categoryForm.code,
      description: categoryForm.description,
      is_active: true,
      sort_order: 1
    }
    
    await optionCategoriesApi.create(categoryData)
    message.success('分类保存成功')
    showAddCategoryModal.value = false
    Object.assign(categoryForm, { name: '', code: '', description: '' })
    await loadCategories()
  } catch (error) {
    console.error('分类保存失败:', error)
    if ((error as any).response?.status === 409) {
      message.error('分类代码已存在，请使用其他代码')
    } else {
      message.error('分类保存失败，请稍后重试')
    }
  } finally {
    categorySaving.value = false
  }
}

const handleSaveItem = async () => {
  if (!selectedCategory.value || itemSaving.value) return
  
  try {
    itemSaving.value = true
    
    // 表单验证
    await itemFormRef.value?.validate()
    
    // 检查选项值是否在当前分类中重复
    const existingItem = items.value.find(item => item.value === itemForm.value)
    if (existingItem) {
      message.error('选项值在当前分类中已存在，请使用其他值')
      return
    }
    
    const itemData = {
      category_id: selectedCategory.value.id,
      code: itemForm.value,
      label: itemForm.label,
      value: itemForm.value,
      description: itemForm.description,
      sort_order: itemForm.sort_order,
      is_active: true
    }
    
    await optionItemsApi.create(itemData)
    message.success('选项保存成功')
    showAddItemModal.value = false
    Object.assign(itemForm, { label: '', value: '', description: '', sort_order: 0 })
    await loadItems(selectedCategory.value.id)
  } catch (error) {
    console.error('选项保存失败:', error)
    if ((error as any).response?.status === 409) {
      message.error('选项值已存在，请使用其他值')
    } else {
      message.error('选项保存失败，请稍后重试')
    }
  } finally {
    itemSaving.value = false
  }
}

const handleDeleteCategory = async (category: OptionCategory) => {
  if (deletingCategoryId.value === category.id) return
  
  dialog.warning({
    title: '确认删除',
    content: `确定要删除分类「${category.name}」吗？删除后该分类下的所有选项项也将被删除，此操作不可恢复。`,
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        deletingCategoryId.value = category.id
        await optionCategoriesApi.delete(category.id)
        message.success('分类删除成功')
        await loadCategories()
        if (selectedCategory.value?.id === category.id) {
          selectedCategory.value = null
          items.value = []
        }
      } catch (error) {
        console.error('分类删除失败:', error)
        message.error('分类删除失败')
      } finally {
        deletingCategoryId.value = null
      }
    }
  })
}

const handleDeleteItem = async (item: OptionItem) => {
  if (deletingItemId.value === item.id) return
  
  dialog.warning({
    title: '确认删除',
    content: `确定要删除选项「${item.label}」吗？此操作不可恢复。`,
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        deletingItemId.value = item.id
        await optionItemsApi.delete(item.id)
        message.success('选项删除成功')
        if (selectedCategory.value) {
          await loadItems(selectedCategory.value.id)
        }
      } catch (error) {
        console.error('选项删除失败:', error)
        message.error('选项删除失败')
      } finally {
        deletingItemId.value = null
      }
    }
  })
}

const handleToggleItemStatus = async (item: OptionItem) => {
  if (togglingItemId.value === item.id) return
  
  try {
    togglingItemId.value = item.id
    
    const newStatus = !item.is_active
    const updateData = {
      category_id: item.category_id,
      code: item.code,
      label: item.label,
      value: item.value,
      description: item.description || '',
      sort_order: item.sort_order,
      is_active: newStatus
    }
    
    await optionItemsApi.update(item.id, updateData)
    message.success(`选项已${newStatus ? '启用' : '禁用'}`)
    if (selectedCategory.value) {
      await loadItems(selectedCategory.value.id)
    }
  } catch (error) {
    console.error('状态切换失败:', error)
    message.error('状态切换失败')
  } finally {
    togglingItemId.value = null
  }
}

// 生命周期
onMounted(() => {
  loadCategories()
})
</script>

<style scoped>
.system-settings {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
}

.page-description {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.settings-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.header-icon {
  color: #1890ff;
}

.options-management {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.categories-table,
.items-table {
  border-radius: 6px;
  overflow: hidden;
}

.categories-section {
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.items-section {
  padding-top: 24px;
}
</style>