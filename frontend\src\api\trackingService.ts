import request from '@/utils/request'

// API 响应解包工具函数
const unwrapResponse = <T>(response: any): T => {
  // 后端返回格式: {success: boolean, data: T}
  if (response.data && response.data.success !== undefined) {
    return response.data.data || response.data
  }
  return response.data
}

// 异步响应解包工具函数
const unwrapAsyncResponse = async <T>(response: Promise<any>): Promise<T> => {
  const result = await response
  return result.data
}

// 跟踪记录接口
export interface TrackingRecord {
  id: number
  customer_id: number
  user_id: number
  type: 'call' | 'visit' | 'email' | 'wechat' | 'meeting' | 'other'
  content: string
  result?: string
  next_follow_time?: string
  status: 'pending' | 'completed' | 'cancelled'
  created_at: string
  updated_at: string
  customer_name?: string
  user_name?: string
  attachments?: string[]
}

// 自动提醒接口
export interface AutoReminder {
  id: number
  customer_id: number
  user_id: number
  title: string
  content: string
  remind_time: string
  type: 'follow_up' | 'birthday' | 'contract_renewal' | 'payment_due' | 'custom'
  status: 'pending' | 'sent' | 'read' | 'dismissed'
  created_at: string
  updated_at: string
  customer_name?: string
  user_name?: string
}

// 客户行为分析接口
export interface CustomerBehavior {
  id: number
  customer_id: number
  action: string
  page_url?: string
  duration?: number
  device_info?: string
  ip_address?: string
  created_at: string
  customer_name?: string
}

// 跟踪统计接口
export interface TrackingStats {
  total_records: number
  completed_records: number
  pending_records: number
  overdue_records: number
  this_week_records: number
  this_month_records: number
  completion_rate: number
  avg_response_time: number
}

// 跟踪记录相关API
export const trackingService = {
  // 获取跟踪记录列表
  getTrackingRecords: async (params: {
    page?: number
    pageSize?: number
    customer_id?: number
    user_id?: number
    type?: string
    status?: string
    start_date?: string
    end_date?: string
    keyword?: string
  }) => {
    const response = request.get<{
      data: TrackingRecord[]
      total: number
      page: number
      pageSize: number
    }>('/api/tracking/records', { params })
    return unwrapAsyncResponse<{
      data: TrackingRecord[]
      total: number
      page: number
      pageSize: number
    }>(response)
  },

  // 获取单个跟踪记录
  getTrackingRecord: (id: number) => {
    return request.get<TrackingRecord>(`/api/tracking/records/${id}`)
  },

  // 创建跟踪记录
  createTrackingRecord: (data: Omit<TrackingRecord, 'id' | 'created_at' | 'updated_at'>) => {
    return request.post<TrackingRecord>('/api/tracking/records', data)
  },

  // 更新跟踪记录
  updateTrackingRecord: (id: number, data: Partial<TrackingRecord>) => {
    return request.put<TrackingRecord>(`/api/tracking/records/${id}`, data)
  },

  // 删除跟踪记录
  deleteTrackingRecord: (id: number) => {
    return request.delete(`/api/tracking/records/${id}`)
  },

  // 批量删除跟踪记录
  batchDeleteTrackingRecords: (ids: number[]) => {
    return request.delete('/api/tracking/records/batch', { data: { ids } })
  },

  // 完成跟踪记录
  completeTrackingRecord: (id: number, result: string) => {
    return request.patch<TrackingRecord>(`/api/tracking/records/${id}/complete`, { result })
  },

  // 获取客户的跟踪记录
  getCustomerTrackingRecords: async (customerId: number, params?: {
    page?: number
    pageSize?: number
    type?: string
    status?: string
  }) => {
    const response = request.get<{
      data: TrackingRecord[]
      total: number
    }>(`/api/customers/${customerId}/tracking-records`, { params })
    return unwrapAsyncResponse<{
      data: TrackingRecord[]
      total: number
    }>(response)
  }
}

// 自动提醒相关API
export const reminderService = {
  // 获取提醒列表
  getReminders: async (params: {
    page?: number
    pageSize?: number
    user_id?: number
    customer_id?: number
    type?: string
    status?: string
    start_date?: string
    end_date?: string
  }) => {
    const response = request.get<{
      data: AutoReminder[]
      total: number
    }>('/api/reminders', { params })
    return unwrapAsyncResponse<{
      data: AutoReminder[]
      total: number
    }>(response)
  },

  // 获取单个提醒
  getReminder: (id: number) => {
    return request.get<AutoReminder>(`/api/reminders/${id}`)
  },

  // 创建提醒
  createReminder: (data: Omit<AutoReminder, 'id' | 'created_at' | 'updated_at'>) => {
    return request.post<AutoReminder>('/api/reminders', data)
  },

  // 更新提醒
  updateReminder: (id: number, data: Partial<AutoReminder>) => {
    return request.put<AutoReminder>(`/api/reminders/${id}`, data)
  },

  // 删除提醒
  deleteReminder: (id: number) => {
    return request.delete(`/api/reminders/${id}`)
  },

  // 标记提醒为已读
  markReminderAsRead: (id: number) => {
    return request.patch<AutoReminder>(`/api/reminders/${id}/read`)
  },

  // 忽略提醒
  dismissReminder: (id: number) => {
    return request.patch<AutoReminder>(`/api/reminders/${id}/dismiss`)
  },

  // 获取今日提醒
  getTodayReminders: () => {
    return request.get<AutoReminder[]>('/api/reminders/today')
  },

  // 获取逾期提醒
  getOverdueReminders: () => {
    return request.get<AutoReminder[]>('/api/reminders/overdue')
  }
}

// 客户行为分析相关API
export const behaviorService = {
  // 获取客户行为记录
  getCustomerBehaviors: async (customerId: number, params?: {
    page?: number
    pageSize?: number
    start_date?: string
    end_date?: string
    action?: string
  }) => {
    const response = request.get<{
      data: CustomerBehavior[]
      total: number
    }>(`/api/customers/${customerId}/behaviors`, { params })
    return unwrapAsyncResponse<{
      data: CustomerBehavior[]
      total: number
    }>(response)
  },

  // 记录客户行为
  recordCustomerBehavior: (data: Omit<CustomerBehavior, 'id' | 'created_at'>) => {
    return request.post<CustomerBehavior>('/api/customer-behaviors', data)
  },

  // 获取客户行为统计
  getCustomerBehaviorStats: (customerId: number, params?: {
    start_date?: string
    end_date?: string
  }) => {
    return request.get<{
      total_actions: number
      page_views: number
      avg_duration: number
      most_visited_pages: Array<{ page: string; count: number }>
      activity_timeline: Array<{ date: string; count: number }>
    }>(`/api/customers/${customerId}/behavior-stats`, { params })
  },

  // 获取所有客户行为概览
  getAllCustomerBehaviors: async (params?: {
    page?: number
    pageSize?: number
    start_date?: string
    end_date?: string
  }) => {
    const response = request.get<{
      data: CustomerBehavior[]
      total: number
    }>('/api/customer-behaviors', { params })
    return unwrapAsyncResponse<{
      data: CustomerBehavior[]
      total: number
    }>(response)
  }
}

// 跟踪统计相关API
export const trackingStatsService = {
  // 获取跟踪统计数据
  async getTrackingStats(params?: {
    user_id?: number
    start_date?: string
    end_date?: string
  }): Promise<TrackingStats> {
    const response = await request.get('/api/tracking/stats', { params })
    return response.data
  },

  // 获取跟踪趋势数据
  async getTrackingTrends(params?: {
    user_id?: number
    start_date?: string
    end_date?: string
    period?: 'day' | 'week' | 'month'
  }): Promise<Array<{
    date: string
    total_records: number
    completed_records: number
    completion_rate: number
  }>> {
    const response = await request.get('/api/tracking/trends', { params })
    return response.data
  },

  // 获取用户跟踪排行
  async getUserTrackingRanking(params?: {
    start_date?: string
    end_date?: string
    limit?: number
  }): Promise<Array<{
    user_id: number
    user_name: string
    total_records: number
    completed_records: number
    completion_rate: number
  }>> {
    const response = await request.get('/api/tracking/user-ranking', { params })
    return response.data
  }
}

export default {
  trackingService,
  reminderService,
  behaviorService,
  trackingStatsService
}