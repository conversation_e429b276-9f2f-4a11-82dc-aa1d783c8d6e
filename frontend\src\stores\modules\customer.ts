import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { CustomerService, type Customer, type CustomerFilters } from '@/api/customerService'
import { createDiscreteApi } from 'naive-ui'

// 创建独立的message API，用于在store中使用
const { message } = createDiscreteApi(['message'])

export const useCustomerStore = defineStore('customer', () => {
  
  // 状态
  const customers = ref<Customer[]>([])
  const currentCustomer = ref<Customer | null>(null)
  const loading = ref(false)
  const pagination = ref({
    page: 1,
    pageSize: 20,
    total: 0
  })
  
  // 筛选条件
  const filters = ref({
    keyword: '',
    status: '',
    source: '',
    assignedTo: '',
    dateRange: null as [string, string] | null
  })
  
  // 计算属性
  const totalCustomers = computed(() => pagination.value.total)
  const hasCustomers = computed(() => customers.value.length > 0)
  
  // 获取客户列表
  const fetchCustomers = async (params?: any) => {
    try {
      loading.value = true
      const queryParams: CustomerFilters = {
        page: pagination.value.page,
        page_size: pagination.value.pageSize,
        search: filters.value.keyword,
        status: filters.value.status ? [filters.value.status] : undefined,
        source: filters.value.source ? [filters.value.source] : undefined,
        assigned_to: filters.value.assignedTo ? [filters.value.assignedTo] : undefined,
        date_range: filters.value.dateRange || undefined,
        ...params
      }
      
      console.log('fetchCustomers queryParams:', queryParams)
      const response = await CustomerService.getCustomers(queryParams)
      console.log('fetchCustomers response:', response)
      
      // 确保customers始终是数组
      const customerData = Array.isArray(response.data) ? response.data : []
      customers.value = customerData
      
      pagination.value = {
        page: response.page || 1,
        pageSize: response.page_size || 20,
        total: response.total || 0
      }
      
      console.log('customers.value after update:', customers.value)
      console.log('customers.value is array:', Array.isArray(customers.value))
      
      return { success: true, data: response }
    } catch (error: any) {
      console.error('fetchCustomers error:', error)
      message.error(error.message || '获取客户列表失败')
      customers.value = []
      pagination.value.total = 0
      return { success: false, message: error.message || '获取客户列表失败' }
    } finally {
      loading.value = false
    }
  }
  
  // 获取客户详情
  const fetchCustomerDetail = async (id: number | string) => {
    try {
      loading.value = true
      const response = await CustomerService.getCustomer(Number(id))
      
      currentCustomer.value = response || null
      return { success: true, data: response }
    } catch (error: any) {
      message.error(error.message || '获取客户详情失败')
      currentCustomer.value = null
      return { success: false, message: error.message || '获取客户详情失败' }
    } finally {
      loading.value = false
    }
  }
  
  // 创建客户
  const createCustomer = async (customerData: Omit<Customer, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      loading.value = true
      const response = await CustomerService.createCustomer(customerData)
      
      // 刷新列表
      await fetchCustomers()
      message.success('客户创建成功')
      return { success: true, data: response, message: '客户创建成功' }
    } catch (error: any) {
      message.error(error.message || '客户创建失败')
      return { success: false, message: error.message || '客户创建失败' }
    } finally {
      loading.value = false
    }
  }
  
  // 更新客户
  const updateCustomer = async (id: number | string, customerData: Partial<Customer>) => {
    try {
      loading.value = true
      const response = await CustomerService.updateCustomer(Number(id), customerData)
      
      // 更新当前客户信息
      if (currentCustomer.value?.id === Number(id)) {
        currentCustomer.value = { ...currentCustomer.value, ...customerData }
      }
      // 更新列表中的客户信息
      const index = customers.value.findIndex(c => c.id === Number(id))
      if (index !== -1) {
        customers.value[index] = { ...customers.value[index], ...customerData }
      }
      message.success('客户更新成功')
      return { success: true, data: response, message: '客户更新成功' }
    } catch (error: any) {
      message.error(error.message || '客户更新失败')
      return { success: false, message: error.message || '客户更新失败' }
    } finally {
      loading.value = false
    }
  }
  
  // 删除客户
  const deleteCustomer = async (id: number | string) => {
    try {
      loading.value = true
      await CustomerService.deleteCustomer(Number(id))
      
      // 从列表中移除
      customers.value = customers.value.filter(c => c.id !== Number(id))
      // 如果删除的是当前客户，清空当前客户
      if (currentCustomer.value?.id === Number(id)) {
        currentCustomer.value = null
      }
      message.success('客户删除成功')
      return { success: true, message: '客户删除成功' }
    } catch (error: any) {
      message.error(error.message || '客户删除失败')
      return { success: false, message: error.message || '客户删除失败' }
    } finally {
      loading.value = false
    }
  }
  
  // 分配客户
  const assignCustomer = async (customerId: number | string, userId: string) => {
    try {
      loading.value = true
      await CustomerService.assignCustomer(Number(customerId), userId)
      
      // 更新客户信息
      const customer = customers.value.find(c => c.id === Number(customerId))
      if (customer) {
        customer.assigned_to = Number(userId)
      }
      if (currentCustomer.value?.id === Number(customerId)) {
        currentCustomer.value.assigned_to = Number(userId)
      }
      message.success('客户分配成功')
      return { success: true, message: '客户分配成功' }
    } catch (error: any) {
      message.error(error.message || '客户分配失败')
      return { success: false, message: error.message || '客户分配失败' }
    } finally {
      loading.value = false
    }
  }

  // 批量分配客户
  const batchAssignCustomers = async (customerIds: number[], userId: string) => {
    try {
      loading.value = true
      await CustomerService.batchAssignCustomers(customerIds, userId)
      
      // 更新客户信息
      customerIds.forEach(id => {
        const customer = customers.value.find(c => c.id === id)
        if (customer) {
          customer.assigned_to = Number(userId)
        }
      })
      
      if (currentCustomer.value && customerIds.includes(currentCustomer.value.id)) {
        currentCustomer.value.assigned_to = Number(userId)
      }
      
      message.success(`成功分配 ${customerIds.length} 个客户`)
      return { success: true, message: `成功分配 ${customerIds.length} 个客户` }
    } catch (error: any) {
      message.error(error.message || '批量分配客户失败')
      return { success: false, message: error.message || '批量分配客户失败' }
    } finally {
      loading.value = false
    }
  }

  // 批量删除客户
  const batchDeleteCustomers = async (customerIds: number[]) => {
    try {
      loading.value = true
      await CustomerService.batchDeleteCustomers(customerIds)
      
      // 从列表中移除
      customers.value = customers.value.filter(c => !customerIds.includes(c.id))
      
      // 如果删除的包含当前客户，清空当前客户
      if (currentCustomer.value && customerIds.includes(currentCustomer.value.id)) {
        currentCustomer.value = null
      }
      
      message.success(`成功删除 ${customerIds.length} 个客户`)
      return { success: true, message: `成功删除 ${customerIds.length} 个客户` }
    } catch (error: any) {
      message.error(error.message || '批量删除客户失败')
      return { success: false, message: error.message || '批量删除客户失败' }
    } finally {
      loading.value = false
    }
  }

  // 移入公海（批量更新客户状态）
  const moveToPool = async (customerIds: number[]) => {
    try {
      loading.value = true
      await CustomerService.batchUpdateCustomers(customerIds, { assigned_to: undefined })
      
      // 更新客户信息
      customerIds.forEach(id => {
        const customer = customers.value.find(c => c.id === id)
        if (customer) {
          customer.assigned_to = undefined
        }
      })
      
      if (currentCustomer.value && customerIds.includes(currentCustomer.value.id)) {
        currentCustomer.value.assigned_to = undefined
      }
      
      message.success(`成功将 ${customerIds.length} 个客户移入公海`)
      return { success: true, message: `成功将 ${customerIds.length} 个客户移入公海` }
    } catch (error: any) {
      message.error(error.message || '移入公海失败')
      return { success: false, message: error.message || '移入公海失败' }
    } finally {
      loading.value = false
    }
  }
  
  // 设置筛选条件
  const setFilters = (newFilters: Partial<typeof filters.value>) => {
    filters.value = { ...filters.value, ...newFilters }
  }
  
  // 重置筛选条件
  const resetFilters = () => {
    filters.value = {
      keyword: '',
      status: '',
      source: '',
      assignedTo: '',
      dateRange: null
    }
  }
  
  // 设置分页
  const setPagination = (page: number, pageSize?: number) => {
    pagination.value.page = page
    if (pageSize) {
      pagination.value.pageSize = pageSize
    }
  }
  
  // 根据ID获取客户
  const getCustomerById = (id: number | string) => {
    return customers.value.find(c => c.id === Number(id)) || null
  }
  
  // 清空当前客户
  const clearCurrentCustomer = () => {
    currentCustomer.value = null
  }

  // 导出客户
  const exportCustomers = async (filters?: CustomerFilters) => {
    try {
      loading.value = true
      const response = await CustomerService.exportCustomers(filters)
      message.success('客户导出成功')
      return { success: true, data: response, message: '客户导出成功' }
    } catch (error: any) {
      message.error(error.message || '客户导出失败')
      return { success: false, message: error.message || '客户导出失败' }
    } finally {
      loading.value = false
    }
  }

  // 导入客户
  const importCustomers = async (file: File) => {
    try {
      loading.value = true
      const response = await CustomerService.importCustomers(file)
      // 导入成功后刷新客户列表
      await fetchCustomers()
      message.success('客户导入成功')
      return { success: true, data: response, message: '客户导入成功' }
    } catch (error: any) {
      message.error(error.message || '客户导入失败')
      return { success: false, message: error.message || '客户导入失败' }
    } finally {
      loading.value = false
    }
  }
  
  return {
      // 状态
      customers,
      currentCustomer,
      loading,
      pagination,
      filters,
      
      // 计算属性
      totalCustomers,
      hasCustomers,
      
      // 方法
      fetchCustomers,
      fetchCustomerDetail,
      getCustomerById,
      createCustomer,
      updateCustomer,
      deleteCustomer,
      assignCustomer,
      batchAssignCustomers,
      batchDeleteCustomers,
      moveToPool,
      setFilters,
      resetFilters,
      setPagination,
      clearCurrentCustomer,
      exportCustomers,
      importCustomers
    }
})