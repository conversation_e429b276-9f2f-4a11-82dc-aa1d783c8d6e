<template>
  <div class="options-management">
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">选项管理</h1>
          <p class="page-description">管理系统中的各种选项配置，包括分类和具体选项值</p>
        </div>
        <div class="stats-section">
          <n-space>
            <div class="stat-card">
              <div class="stat-number">{{ optionsStore.categoryCount }}</div>
              <div class="stat-label">选项分类</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{ optionsStore.itemCount }}</div>
              <div class="stat-label">选项数据</div>
            </div>
          </n-space>
        </div>
      </div>
    </div>
    
    <n-card :bordered="false" class="main-content">
      <n-tabs v-model:value="activeTab" type="line" animated>
        <n-tab-pane name="categories" tab="选项分类管理">
          <CategoryManager />
        </n-tab-pane>
        <n-tab-pane name="items" tab="选项数据管理">
          <ItemManager />
        </n-tab-pane>
      </n-tabs>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { NCard, NTabs, NTabPane, NSpace, NIcon } from 'naive-ui'
import { FolderOutline, ListOutline } from '@vicons/ionicons5'
import { useOptionsStore } from '@/stores/optionsStore'
import CategoryManager from '@/components/settings/CategoryManager.vue'
import ItemManager from '@/components/settings/ItemManager.vue'

// 路由和消息
const route = useRoute()
const router = useRouter()
const message = useMessage()

// Options Store
const optionsStore = useOptionsStore()

const activeTab = ref('categories')

// 支持的标签页类型
type TabType = 'categories' | 'items'

// 标签页切换处理
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  
  // 更新URL参数
  const query = { ...route.query }
  if (tabName === 'categories') {
    delete query.tab
  } else {
    query.tab = tabName
  }
  
  router.replace({ query })
}

// 初始化标签页
const initializeTab = () => {
  const tabParam = route.query.tab as string
  const validTabs: TabType[] = ['categories', 'items']
  
  if (tabParam && validTabs.includes(tabParam as TabType)) {
    activeTab.value = tabParam
  } else {
    activeTab.value = 'categories'
  }
}

// 监听路由变化
watch(
  () => route.query.tab,
  (newTab) => {
    if (newTab && typeof newTab === 'string') {
      const validTabs: TabType[] = ['categories', 'items']
      if (validTabs.includes(newTab as TabType)) {
        activeTab.value = newTab
      }
    } else {
      activeTab.value = 'categories'
    }
  }
)

// 生命周期
onMounted(() => {
  initializeTab()
})
</script>

<style scoped>
.options-management {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: white;
}

.page-description {
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
}

.stats-section {
  flex-shrink: 0;
}

.stat-card {
  background: rgba(255, 255, 255, 0.2);
  padding: 16px 20px;
  border-radius: 8px;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: white;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.main-content {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-radius: 12px;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .options-management {
    padding: 16px;
  }
  
  .page-header {
    padding: 20px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .page-description {
    font-size: 14px;
  }
  
  .stats-section {
    width: 100%;
  }
  
  .stat-card {
    padding: 12px 16px;
  }
  
  .stat-number {
    font-size: 20px;
  }
}
</style>