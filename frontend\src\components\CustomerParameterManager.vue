<template>
  <div class="parameter-manager">
    <!-- 操作栏 -->
    <div class="toolbar">
      <n-input
        v-model:value="searchKeyword"
        placeholder="搜索选项..."
        class="search-input"
      >
        <template #prefix>
          <n-icon><SearchOutline /></n-icon>
        </template>
      </n-input>
      
      <n-button type="primary" @click="handleAdd">
        <template #icon>
          <n-icon><AddOutline /></n-icon>
        </template>
        添加选项
      </n-button>
    </div>

    <!-- 数据列表 -->
    <div class="data-list">
      <div v-if="loading" class="loading">
        加载中...
      </div>
      
      <div v-else-if="filteredData.length === 0" class="empty">
        <n-empty description="暂无数据" />
      </div>
      
      <div v-else class="list-items">
        <div 
          v-for="item in filteredData" 
          :key="item.id" 
          class="list-item"
        >
          <div class="item-content">
            <div class="item-main">
              <div class="item-title">{{ item.name || item.value }}</div>
              <div class="item-code">{{ item.code }}</div>
              <div v-if="item.description" class="item-desc">{{ item.description }}</div>
            </div>
            
            <div class="item-actions">
              <n-button size="small" @click="handleEdit(item)">
                <template #icon>
                  <n-icon><CreateOutline /></n-icon>
                </template>
                编辑
              </n-button>
              
              <n-button size="small" type="error" @click="handleDelete(item.id!)">
                <template #icon>
                  <n-icon><TrashOutline /></n-icon>
                </template>
                删除
              </n-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑模态框 -->
    <n-modal v-model:show="showModal" :title="modalTitle">
      <n-card style="width: 500px" :title="modalTitle">
        <n-form ref="formRef" :model="formData" :rules="formRules">
          <n-form-item label="选项代码" path="code">
            <n-input v-model:value="formData.code" placeholder="请输入选项代码" />
          </n-form-item>
          
          <n-form-item label="选项值" path="value">
            <n-input v-model:value="formData.value" placeholder="请输入选项值" />
          </n-form-item>
          
          <n-form-item label="显示标签" path="name">
            <n-input v-model:value="formData.name" placeholder="请输入显示标签" />
          </n-form-item>
          
          <n-form-item label="描述">
            <n-input 
              v-model:value="formData.description" 
              type="textarea" 
              placeholder="请输入描述（可选）"
              :rows="3"
            />
          </n-form-item>
        </n-form>
        
        <template #footer>
          <n-space justify="end">
            <n-button @click="handleCancel">取消</n-button>
            <n-button type="primary" :loading="submitting" @click="handleSubmit">
              {{ isEditMode ? '更新' : '创建' }}
            </n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import {
  NInput,
  NButton,
  NModal,
  NForm,
  NFormItem,
  NIcon,
  NEmpty,
  NSpace,
  NCard,
  useMessage
} from 'naive-ui'
import {
  SearchOutline,
  AddOutline,
  CreateOutline,
  TrashOutline
} from '@vicons/ionicons5'
import { useCustomerOptions } from '@/composables/useCustomerOptions'
import type { OptionItem } from '@/types/customerOptions'

// Props
interface Props {
  categoryCode: string
  categoryName: string
}

const props = defineProps<Props>()

// 消息
const message = useMessage()

// 使用客户选项组合函数
const {
  items,
  loading,
  loadItems,
  createItem,
  updateItem,
  deleteItem
} = useCustomerOptions({ categoryCode: props.categoryCode })

// 响应式数据
const formRef = ref()
const showModal = ref(false)
const isEditMode = ref(false)
const currentEditId = ref<number | null>(null)
const searchKeyword = ref('')
const submitting = ref(false)

// 表单数据
const formData = ref({
  code: '',
  value: '',
  name: '',
  description: ''
})

// 表单验证规则
const formRules = {
  code: {
    required: true,
    message: '请输入选项代码',
    trigger: 'blur'
  },
  value: {
    required: true,
    message: '请输入选项值',
    trigger: 'blur'
  },
  name: {
    required: true,
    message: '请输入显示名称',
    trigger: 'blur'
  }
}

// 计算属性
const modalTitle = computed(() => {
  return isEditMode.value ? '编辑选项' : '添加选项'
})

// 过滤数据
const filteredData = computed(() => {
  let data = items.value
  
  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    data = data.filter(item => 
      item.code?.toLowerCase().includes(keyword) ||
      item.value?.toLowerCase().includes(keyword) ||
      item.name?.toLowerCase().includes(keyword)
    )
  }
  
  return data
})

// 简化的方法
const handleAdd = () => {
  resetForm()
  isEditMode.value = false
  currentEditId.value = null
  showModal.value = true
}

const handleEdit = (item: OptionItem) => {
  formData.value = {
    code: item.code || '',
    value: item.value || '',
    name: item.name || '',
    description: item.description || ''
  }
  isEditMode.value = true
  currentEditId.value = item.id!
  showModal.value = true
}

const handleDelete = async (id: number) => {
  try {
    await deleteItem(id)
    message.success('删除成功')
    await loadItems()
  } catch (error) {
    message.error('删除失败')
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    const submitData = {
      ...formData.value,
      categoryCode: props.categoryCode,
      status: 1,
      sortOrder: 0
    }
    
    if (isEditMode.value && currentEditId.value) {
      await updateItem(currentEditId.value, submitData)
      message.success('更新成功')
    } else {
      await createItem(submitData)
      message.success('创建成功')
    }
    
    showModal.value = false
    await loadItems()
  } catch (error) {
    console.error('提交失败:', error)
    message.error('操作失败，请重试')
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  showModal.value = false
  resetForm()
}

const resetForm = () => {
  formData.value = {
    code: '',
    value: '',
    name: '',
    description: ''
  }
  currentEditId.value = null
  isEditMode.value = false
}

// 监听分类代码变化
watch(() => props.categoryCode, async (newCode) => {
  if (newCode) {
    await loadItems()
  }
}, { immediate: true })

// 组件挂载
onMounted(async () => {
  if (props.categoryCode) {
    await loadItems()
  }
})
</script>

<style scoped>
.parameter-manager {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.search-input {
  width: 300px;
}

.data-list {
  flex: 1;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #64748b;
}

.empty {
  padding: 40px;
}

.list-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.list-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.list-item:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
}

.item-main {
  flex: 1;
}

.item-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.item-code {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
  margin-bottom: 4px;
}

.item-desc {
  font-size: 14px;
  color: #64748b;
  line-height: 1.4;
}

.item-actions {
  display: flex;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input {
    width: 100%;
  }
  
  .item-content {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .item-actions {
    justify-content: center;
  }
}
</style>