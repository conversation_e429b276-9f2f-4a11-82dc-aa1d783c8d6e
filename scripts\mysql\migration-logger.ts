import fs from 'fs/promises';
import path from 'path';
import { MySQLManager } from '../../src/database/MySQLManager';

// 日志级别枚举
export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
  CRITICAL = 'CRITICAL'
}

// 日志条目接口
export interface LogEntry {
  id?: string;
  timestamp: Date;
  level: LogLevel;
  category: string;
  message: string;
  details?: any;
  table_name?: string;
  operation?: string;
  affected_rows?: number;
  execution_time?: number;
  error_code?: string;
  stack_trace?: string;
}

// 日志统计接口
export interface LogStatistics {
  total_entries: number;
  by_level: Record<LogLevel, number>;
  by_category: Record<string, number>;
  by_table: Record<string, number>;
  error_rate: number;
  average_execution_time: number;
  start_time: Date;
  end_time: Date;
}

// 日志配置接口
export interface LoggerConfig {
  log_to_file: boolean;
  log_to_database: boolean;
  log_to_console: boolean;
  file_path?: string;
  max_file_size?: number; // MB
  max_files?: number;
  min_level: LogLevel;
  include_stack_trace: boolean;
  buffer_size: number;
  flush_interval: number; // seconds
}

/**
 * 迁移日志记录器类
 * 提供完整的迁移过程日志记录和管理功能
 */
export class MigrationLogger {
  private config: LoggerConfig;
  private mysqlManager: MySQLManager;
  private logBuffer: LogEntry[] = [];
  private flushTimer?: NodeJS.Timeout;
  private sessionId: string;
  private startTime: Date;

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      log_to_file: true,
      log_to_database: true,
      log_to_console: true,
      file_path: './logs/migration.log',
      max_file_size: 100, // 100MB
      max_files: 10,
      min_level: LogLevel.INFO,
      include_stack_trace: true,
      buffer_size: 100,
      flush_interval: 30,
      ...config
    };

    this.mysqlManager = new MySQLManager({
      host: process.env.MYSQL_HOST || 'localhost',
      port: parseInt(process.env.MYSQL_PORT || '3306'),
      user: process.env.MYSQL_USER || 'root',
      password: process.env.MYSQL_PASSWORD || '',
      database: process.env.MYSQL_DATABASE || 'yysh_crm',
      connectionLimit: 10
    });
    this.sessionId = this.generateSessionId();
    this.startTime = new Date();

    // 启动定时刷新
    this.startFlushTimer();

    // 进程退出时确保日志被刷新
    process.on('exit', () => this.flush());
    process.on('SIGINT', () => {
      this.flush();
      process.exit(0);
    });
  }

  /**
   * 初始化日志系统
   */
  async initialize(): Promise<void> {
    try {
      // 创建日志目录
      if (this.config.log_to_file && this.config.file_path) {
        const logDir = path.dirname(this.config.file_path);
        await fs.mkdir(logDir, { recursive: true });
      }

      // 初始化数据库日志表
      if (this.config.log_to_database) {
        await this.initializeLogTable();
      }

      await this.info('SYSTEM', '迁移日志系统初始化完成', {
        session_id: this.sessionId,
        config: this.config
      });
    } catch (error) {
      console.error('日志系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 记录调试级别日志
   */
  async debug(category: string, message: string, details?: any): Promise<void> {
    await this.log(LogLevel.DEBUG, category, message, details);
  }

  /**
   * 记录信息级别日志
   */
  async info(category: string, message: string, details?: any): Promise<void> {
    await this.log(LogLevel.INFO, category, message, details);
  }

  /**
   * 记录警告级别日志
   */
  async warn(category: string, message: string, details?: any): Promise<void> {
    await this.log(LogLevel.WARN, category, message, details);
  }

  /**
   * 记录错误级别日志
   */
  async error(category: string, message: string, details?: any, error?: Error): Promise<void> {
    const logDetails = {
      ...details,
      error_message: error?.message,
      error_name: error?.name
    };

    await this.log(LogLevel.ERROR, category, message, logDetails, error);
  }

  /**
   * 记录严重错误级别日志
   */
  async critical(category: string, message: string, details?: any, error?: Error): Promise<void> {
    const logDetails = {
      ...details,
      error_message: error?.message,
      error_name: error?.name
    };

    await this.log(LogLevel.CRITICAL, category, message, logDetails, error);
  }

  /**
   * 记录表操作日志
   */
  async logTableOperation(
    table: string,
    operation: string,
    affectedRows: number,
    executionTime: number,
    details?: any
  ): Promise<void> {
    await this.log(LogLevel.INFO, 'TABLE_OPERATION', 
      `${operation} 操作完成: ${table}`, {
        table_name: table,
        operation,
        affected_rows: affectedRows,
        execution_time: executionTime,
        ...details
      });
  }

  /**
   * 记录迁移进度
   */
  async logProgress(
    table: string,
    current: number,
    total: number,
    details?: any
  ): Promise<void> {
    const percentage = Math.round((current / total) * 100);
    await this.log(LogLevel.INFO, 'MIGRATION_PROGRESS',
      `${table} 迁移进度: ${current}/${total} (${percentage}%)`, {
        table_name: table,
        current_count: current,
        total_count: total,
        percentage,
        ...details
      });
  }

  /**
   * 记录性能指标
   */
  async logPerformance(
    operation: string,
    executionTime: number,
    memoryUsage: NodeJS.MemoryUsage,
    details?: any
  ): Promise<void> {
    await this.log(LogLevel.INFO, 'PERFORMANCE',
      `性能指标: ${operation}`, {
        operation,
        execution_time: executionTime,
        memory_usage: {
          rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
          heap_used: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
          heap_total: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
          external: Math.round(memoryUsage.external / 1024 / 1024) // MB
        },
        ...details
      });
  }

  /**
   * 核心日志记录方法
   */
  private async log(
    level: LogLevel,
    category: string,
    message: string,
    details?: any,
    error?: Error
  ): Promise<void> {
    // 检查日志级别
    if (!this.shouldLog(level)) {
      return;
    }

    const logEntry: LogEntry = {
      id: this.generateLogId(),
      timestamp: new Date(),
      level,
      category,
      message,
      details,
      table_name: details?.table_name,
      operation: details?.operation,
      affected_rows: details?.affected_rows,
      execution_time: details?.execution_time,
      error_code: details?.error_code,
      stack_trace: error && this.config.include_stack_trace ? error.stack : undefined
    };

    // 控制台输出
    if (this.config.log_to_console) {
      this.logToConsole(logEntry);
    }

    // 添加到缓冲区
    this.logBuffer.push(logEntry);

    // 如果缓冲区满了，立即刷新
    if (this.logBuffer.length >= this.config.buffer_size) {
      await this.flush();
    }
  }

  /**
   * 刷新日志缓冲区
   */
  async flush(): Promise<void> {
    if (this.logBuffer.length === 0) {
      return;
    }

    const logsToFlush = [...this.logBuffer];
    this.logBuffer = [];

    try {
      // 写入文件
      if (this.config.log_to_file) {
        await this.writeToFile(logsToFlush);
      }

      // 写入数据库
      if (this.config.log_to_database) {
        await this.writeToDatabase(logsToFlush);
      }
    } catch (error) {
      console.error('日志刷新失败:', error);
      // 将日志重新放回缓冲区
      this.logBuffer.unshift(...logsToFlush);
    }
  }

  /**
   * 获取日志统计信息
   */
  async getStatistics(sessionId?: string): Promise<LogStatistics> {
    const sql = `
      SELECT 
        COUNT(*) as total_entries,
        level,
        category,
        table_name,
        MIN(timestamp) as start_time,
        MAX(timestamp) as end_time,
        AVG(execution_time) as avg_execution_time
      FROM migration_logs 
      WHERE session_id = ?
      GROUP BY level, category, table_name
    `;

    const results = await this.mysqlManager.query(sql, [sessionId || this.sessionId]);
    
    // 处理统计数据
    const stats: LogStatistics = {
      total_entries: 0,
      by_level: {} as Record<LogLevel, number>,
      by_category: {},
      by_table: {},
      error_rate: 0,
      average_execution_time: 0,
      start_time: this.startTime,
      end_time: new Date()
    };

    // 初始化计数器
    Object.values(LogLevel).forEach(level => {
      stats.by_level[level] = 0;
    });

    let totalErrors = 0;
    let totalExecutionTime = 0;
    let executionTimeCount = 0;

    (results as unknown as any[]).forEach((row: any) => {
      stats.total_entries += row.total_entries;
      stats.by_level[row.level as LogLevel] = (stats.by_level[row.level as LogLevel] || 0) + row.total_entries;
      stats.by_category[row.category] = (stats.by_category[row.category] || 0) + row.total_entries;
      
      if (row.table_name) {
        stats.by_table[row.table_name] = (stats.by_table[row.table_name] || 0) + row.total_entries;
      }

      if (row.level === LogLevel.ERROR || row.level === LogLevel.CRITICAL) {
        totalErrors += row.total_entries;
      }

      if (row.avg_execution_time) {
        totalExecutionTime += row.avg_execution_time * row.total_entries;
        executionTimeCount += row.total_entries;
      }

      if (row.start_time < stats.start_time) {
        stats.start_time = new Date(row.start_time);
      }
      if (row.end_time > stats.end_time) {
        stats.end_time = new Date(row.end_time);
      }
    });

    stats.error_rate = stats.total_entries > 0 ? (totalErrors / stats.total_entries) * 100 : 0;
    stats.average_execution_time = executionTimeCount > 0 ? totalExecutionTime / executionTimeCount : 0;

    return stats;
  }

  /**
   * 查询日志
   */
  async queryLogs(filters: {
    session_id?: string;
    level?: LogLevel;
    category?: string;
    table_name?: string;
    start_time?: Date;
    end_time?: Date;
    limit?: number;
    offset?: number;
  }): Promise<LogEntry[]> {
    let sql = 'SELECT * FROM migration_logs WHERE 1=1';
    const params: any[] = [];

    if (filters.session_id) {
      sql += ' AND session_id = ?';
      params.push(filters.session_id);
    }

    if (filters.level) {
      sql += ' AND level = ?';
      params.push(filters.level);
    }

    if (filters.category) {
      sql += ' AND category = ?';
      params.push(filters.category);
    }

    if (filters.table_name) {
      sql += ' AND table_name = ?';
      params.push(filters.table_name);
    }

    if (filters.start_time) {
      sql += ' AND timestamp >= ?';
      params.push(filters.start_time);
    }

    if (filters.end_time) {
      sql += ' AND timestamp <= ?';
      params.push(filters.end_time);
    }

    sql += ' ORDER BY timestamp DESC';

    if (filters.limit) {
      sql += ' LIMIT ?';
      params.push(filters.limit);

      if (filters.offset) {
        sql += ' OFFSET ?';
        params.push(filters.offset);
      }
    }

    const result = await this.mysqlManager.query(sql, params);
    return result.success ? (result.data as LogEntry[]) : [];
  }

  /**
   * 清理旧日志
   */
  async cleanupOldLogs(daysToKeep: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const sql = 'DELETE FROM migration_logs WHERE timestamp < ?';
    const result = await this.mysqlManager.query(sql, [cutoffDate]);
    
    const affectedRows = result.affectedRows || 0;
    await this.info('CLEANUP', `清理了 ${affectedRows} 条旧日志记录`, {
      cutoff_date: cutoffDate,
      days_kept: daysToKeep
    });

    return affectedRows;
  }

  /**
   * 关闭日志系统
   */
  async close(): Promise<void> {
    // 停止定时器
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }

    // 刷新剩余日志
    await this.flush();

    // 关闭数据库连接
    await this.mysqlManager.close();

    await this.info('SYSTEM', '迁移日志系统已关闭');
  }

  // 私有方法

  private shouldLog(level: LogLevel): boolean {
    const levels = [LogLevel.DEBUG, LogLevel.INFO, LogLevel.WARN, LogLevel.ERROR, LogLevel.CRITICAL];
    const currentLevelIndex = levels.indexOf(level);
    const minLevelIndex = levels.indexOf(this.config.min_level);
    return currentLevelIndex >= minLevelIndex;
  }

  private logToConsole(entry: LogEntry): void {
    const timestamp = entry.timestamp.toISOString();
    const message = `[${timestamp}] [${entry.level}] [${entry.category}] ${entry.message}`;
    
    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(message, entry.details);
        break;
      case LogLevel.INFO:
        console.info(message, entry.details);
        break;
      case LogLevel.WARN:
        console.warn(message, entry.details);
        break;
      case LogLevel.ERROR:
      case LogLevel.CRITICAL:
        console.error(message, entry.details);
        if (entry.stack_trace) {
          console.error(entry.stack_trace);
        }
        break;
    }
  }

  private async writeToFile(logs: LogEntry[]): Promise<void> {
    if (!this.config.file_path) return;

    const logLines = logs.map(log => {
      const logData = {
        timestamp: log.timestamp.toISOString(),
        level: log.level,
        category: log.category,
        message: log.message,
        session_id: this.sessionId,
        ...log.details
      };
      return JSON.stringify(logData);
    }).join('\n') + '\n';

    await fs.appendFile(this.config.file_path, logLines, 'utf8');

    // 检查文件大小并轮转
    await this.rotateLogFile();
  }

  private async writeToDatabase(logs: LogEntry[]): Promise<void> {
    const sql = `
      INSERT INTO migration_logs (
        id, timestamp, level, category, message, details,
        table_name, operation, affected_rows, execution_time,
        error_code, stack_trace, session_id
      ) VALUES ?
    `;

    const values = logs.map(log => [
      log.id,
      log.timestamp,
      log.level,
      log.category,
      log.message,
      log.details ? JSON.stringify(log.details) : null,
      log.table_name,
      log.operation,
      log.affected_rows,
      log.execution_time,
      log.error_code,
      log.stack_trace,
      this.sessionId
    ]);

    await this.mysqlManager.query(sql, [values]);
  }

  private async rotateLogFile(): Promise<void> {
    if (!this.config.file_path || !this.config.max_file_size) return;

    try {
      const stats = await fs.stat(this.config.file_path);
      const fileSizeMB = stats.size / (1024 * 1024);

      if (fileSizeMB > this.config.max_file_size) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const rotatedPath = this.config.file_path.replace(/\.log$/, `-${timestamp}.log`);
        
        await fs.rename(this.config.file_path, rotatedPath);
        
        // 清理旧文件
        await this.cleanupOldLogFiles();
      }
    } catch (error) {
      console.error('日志文件轮转失败:', error);
    }
  }

  private async cleanupOldLogFiles(): Promise<void> {
    if (!this.config.file_path || !this.config.max_files) return;

    try {
      const logDir = path.dirname(this.config.file_path);
      const baseName = path.basename(this.config.file_path, '.log');
      const files = await fs.readdir(logDir);
      
      const logFiles = files
        .filter(file => file.startsWith(baseName) && file.endsWith('.log'))
        .map(file => ({
          name: file,
          path: path.join(logDir, file),
          stat: null as any
        }));

      // 获取文件统计信息
      for (const file of logFiles) {
        file.stat = await fs.stat(file.path);
      }

      // 按修改时间排序，保留最新的文件
      logFiles.sort((a, b) => b.stat.mtime.getTime() - a.stat.mtime.getTime());
      
      const filesToDelete = logFiles.slice(this.config.max_files);
      for (const file of filesToDelete) {
        await fs.unlink(file.path);
      }
    } catch (error) {
      console.error('清理旧日志文件失败:', error);
    }
  }

  private async initializeLogTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS migration_logs (
        id VARCHAR(36) PRIMARY KEY,
        timestamp DATETIME NOT NULL,
        level ENUM('DEBUG', 'INFO', 'WARN', 'ERROR', 'CRITICAL') NOT NULL,
        category VARCHAR(100) NOT NULL,
        message TEXT NOT NULL,
        details JSON,
        table_name VARCHAR(100),
        operation VARCHAR(50),
        affected_rows INT,
        execution_time DECIMAL(10,3),
        error_code VARCHAR(50),
        stack_trace TEXT,
        session_id VARCHAR(36) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_timestamp (timestamp),
        INDEX idx_level (level),
        INDEX idx_category (category),
        INDEX idx_table_name (table_name),
        INDEX idx_session_id (session_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;

    await this.mysqlManager.query(sql);
  }

  private startFlushTimer(): void {
    this.flushTimer = setInterval(async () => {
      try {
        await this.flush();
      } catch (error) {
        console.error('定时刷新日志失败:', error);
      }
    }, this.config.flush_interval * 1000);
  }

  private generateSessionId(): string {
    return `migration-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateLogId(): string {
    return `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 导出默认配置
export const defaultLoggerConfig: LoggerConfig = {
  log_to_file: true,
  log_to_database: true,
  log_to_console: true,
  file_path: './logs/migration.log',
  max_file_size: 100,
  max_files: 10,
  min_level: LogLevel.INFO,
  include_stack_trace: true,
  buffer_size: 100,
  flush_interval: 30
};

// 创建全局日志实例
export const migrationLogger = new MigrationLogger(defaultLoggerConfig);