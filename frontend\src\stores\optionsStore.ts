import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { optionCategoriesApi, optionItemsApi } from '@/api/options'
import type {
  OptionCategory,
  OptionItem,
  OptionCategoryWithItems,
  CreateOptionCategoryRequest,
  UpdateOptionCategoryRequest,
  CreateOptionItemRequest,
  UpdateOptionItemRequest
} from '@/types/options'

export const useOptionsStore = defineStore('options', () => {
  // 状态
  const categories = ref<OptionCategory[]>([])
  const items = ref<OptionItem[]>([])
  const categoriesWithItems = ref<OptionCategoryWithItems[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // 计算属性
  const categoryCount = computed(() => categories.value.length)
  const itemCount = computed(() => items.value.length)
  
  // 根据分类名称获取选项项
  const getItemsByCategory = computed(() => {
    return (categoryName: string) => {
      const category = categories.value.find(cat => cat.name === categoryName)
      if (!category) return []
      return items.value.filter(item => item.category_id === category.id)
    }
  })
  
  // 根据分类名称或分类代码获取选项项（格式化为前端使用的格式）
  const getFormattedOptions = computed(() => {
    return (categoryNameOrCode: string) => {
      // 先尝试按分类名称查找
      let category = categories.value.find(cat => cat.name === categoryNameOrCode)
      // 如果没找到，再尝试按分类代码查找
      if (!category) {
        category = categories.value.find(cat => cat.code === categoryNameOrCode)
      }
      
      if (!category) return []
      
      const categoryItems = items.value.filter(item => item.category_id === category.id && item.is_active)
      return categoryItems.map(item => ({
        value: item.value,
        label: item.label,
        color: item.color,
        icon: item.icon,
        desc: item.description
      }))
    }
  })
  
  // 转换为Naive UI Select组件的选项格式
  const toSelectOptions = computed(() => {
    return (categoryNameOrCode: string) => {
      const formattedOptions = getFormattedOptions.value(categoryNameOrCode)
      return formattedOptions.map(option => ({
        label: option.label,
        value: option.value
      }))
    }
  })
  
  // 清除错误
  const clearError = () => {
    error.value = null
  }
  
  // 设置加载状态
  const setLoading = (isLoading: boolean) => {
    loading.value = isLoading
  }
  
  // 获取所有选项分类
  const fetchCategories = async () => {
    setLoading(true)
    clearError()
    try {
      const response = await optionCategoriesApi.getList()
      if (response.data) {
        categories.value = response.data
      }
    } catch (err: any) {
      error.value = err.response?.data?.error || err.message || '获取选项分类失败'
      console.error('获取选项分类失败:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }
  
  // 根据分类ID获取选项项
  const fetchItemsByCategory = async (categoryId: string) => {
    try {
      loading.value = true
      const response = await optionItemsApi.getList({ 
        category_code: categoryId,
        page: 1,
        page_size: 1000
      })
      const categoryItems = response.data || []
      
      // 更新items数组，替换该分类的数据
      items.value = items.value.filter(item => item.category_id !== categoryId).concat(categoryItems)
      
      return categoryItems
    } catch (error) {
      console.error('获取分类选项项失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 根据分类代码获取选项项
  const fetchItemsByCategoryCode = async (categoryCode: string) => {
    setLoading(true)
    clearError()
    try {
      const response = await optionItemsApi.getByCategoryCode(categoryCode)
      // 确保response是数组类型
      const categoryItems = Array.isArray(response) ? response : []
      // 找到对应的分类ID
      const category = categories.value.find(cat => cat.code === categoryCode)
      if (category && categoryItems.length > 0) {
        // 更新对应分类的选项项
        items.value = items.value.filter(item => item.category_id !== category.id).concat(categoryItems)
      }
      return categoryItems
    } catch (err: any) {
      error.value = err.response?.data?.message || err.message || '获取选项项失败'
      console.error('获取选项项失败:', err)
      // 返回空数组而不是抛出错误，避免中断其他操作
      return []
    } finally {
      setLoading(false)
    }
  }
  
  // 获取所有选项数据（包含分类和选项项）
  const fetchAllOptions = async () => {
    setLoading(true)
    clearError()
    try {
      // 先获取所有分类
      await fetchCategories()
      
      // 然后获取所有选项项
      const itemsResponse = await optionItemsApi.getList()
      if (itemsResponse.data) {
        items.value = itemsResponse.data
      }
    } catch (err: any) {
      error.value = err.response?.data?.error || err.message || '获取选项数据失败'
      console.error('获取选项数据失败:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }
  
  // 初始化选项数据（获取常用的选项分类）
  const initializeOptions = async () => {
    try {
      // 首先获取所有分类
      await fetchCategories()
      
      // 获取常用的选项分类数据（包含客户信息录入表单需要的所有选项）
      const commonCategories = [
        'customer_source',    // 客户来源
        'customer_level',     // 客户等级
        'customer_tags',      // 客户标签
        'decoration_type',    // 装修类型
        'house_status'        // 房屋状态
      ]
      
      const promises = commonCategories.map(async (categoryCode) => {
        try {
          await fetchItemsByCategoryCode(categoryCode)
        } catch (err) {
          console.warn(`获取分类 ${categoryCode} 的选项项失败:`, err)
        }
      })
      
      await Promise.all(promises)
    } catch (err) {
      console.error('初始化选项数据失败:', err)
      throw err
    }
  }
  
  // 刷新选项数据
  const refreshOptions = async () => {
    await fetchAllOptions()
  }

  // 全局刷新所有选项数据
  const refreshAllOptions = async () => {
    try {
      // 重新获取所有分类和选项数据
      await fetchCategories()
      await fetchAllOptions()
    } catch (error) {
      console.error('全局刷新选项数据失败:', error)
    }
  }
  
  // 创建选项分类
  const createCategory = async (categoryData: CreateOptionCategoryRequest) => {
    setLoading(true)
    clearError()
    try {
      const response = await optionCategoriesApi.create(categoryData)
      if (response.data) {
        // 刷新分类列表
        await fetchCategories()
      }
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.error || err.message || '创建选项分类失败'
      console.error('创建选项分类失败:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }

  // 更新选项分类
  const updateCategory = async (id: string, categoryData: UpdateOptionCategoryRequest) => {
    setLoading(true)
    clearError()
    try {
      const response = await optionCategoriesApi.update(id, categoryData)
      console.log('更新选项分类API响应:', response)
      
      // API返回格式: {data: OptionCategory}
      const updatedCategory = response.data
      if (updatedCategory) {
        // 更新本地数据
        const index = categories.value.findIndex(cat => cat.id === id)
        if (index !== -1) {
          categories.value[index] = updatedCategory
        }
        // 刷新分类列表以确保数据同步
        await fetchCategories()
      }
      return updatedCategory
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || err.response?.data?.message || err.message || '更新选项分类失败'
      error.value = errorMessage
      console.error('更新选项分类失败:', err)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // 删除选项分类
  const deleteCategory = async (id: string) => {
    setLoading(true)
    clearError()
    try {
      await optionCategoriesApi.delete(id)
      // 刷新分类列表
      await fetchCategories()
      // 移除相关的选项项
      items.value = items.value.filter(item => item.category_id !== id)
    } catch (err: any) {
      error.value = err.response?.data?.error || err.message || '删除选项分类失败'
      console.error('删除选项分类失败:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }

  // 创建选项项
  const createItem = async (data: CreateOptionItemRequest) => {
    setLoading(true)
    clearError()
    try {
      const response = await optionItemsApi.create(data)
      console.log('创建选项项API响应:', response)
      // API返回格式: {data: OptionItem}
      const newItem = response.data || response
      if (newItem) {
        items.value.push(newItem)
        // 刷新所有选项数据以确保同步
        await refreshAllOptions()
      }
      return newItem
    } catch (err: any) {
      console.error('创建选项项失败:', err)
      const errorMessage = err.response?.data?.error || err.response?.data?.message || err.message || '创建选项项失败'
      error.value = errorMessage
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // 更新选项项
  const updateItem = async (id: string, data: UpdateOptionItemRequest) => {
    setLoading(true)
    clearError()
    try {
      const response = await optionItemsApi.update(id, data)
      console.log('更新选项项API响应:', response)
      // API返回格式: {data: OptionItem}
      const updatedItem = response.data || response
      if (updatedItem) {
        const index = items.value.findIndex(item => item.id === id)
        if (index !== -1) {
          items.value[index] = updatedItem
        }
        // 刷新所有选项数据以确保同步
        await refreshAllOptions()
      }
      return updatedItem
    } catch (err: any) {
      console.error('更新选项项失败:', err)
      const errorMessage = err.response?.data?.error || err.response?.data?.message || err.message || '更新选项项失败'
      error.value = errorMessage
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // 删除选项项
  const deleteItem = async (id: string) => {
    setLoading(true)
    clearError()
    try {
      await optionItemsApi.delete(id)
      // 从本地数据中移除
      items.value = items.value.filter(item => item.id !== id)
      // 全局刷新所有选项数据
      await refreshAllOptions()
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || '删除选项项失败'
      error.value = errorMessage
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // 批量删除选项项
  const batchDeleteItems = async (ids: string[]) => {
    setLoading(true)
    clearError()
    try {
      await optionItemsApi.batchDelete(ids)
      // 从本地数据中移除
      items.value = items.value.filter(item => !ids.includes(item.id))
      // 全局刷新所有选项数据
      await refreshAllOptions()
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || '批量删除选项项失败'
      error.value = errorMessage
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // 批量更新选项项状态
  const batchUpdateItemsStatus = async (ids: string[], is_active: boolean) => {
    setLoading(true)
    clearError()
    try {
      await optionItemsApi.batchUpdateStatus({ ids, is_active })
      // 更新本地数据
      items.value = items.value.map(item => {
        if (ids.includes(item.id)) {
          return { ...item, is_active }
        }
        return item
      })
      // 刷新所有选项数据以确保同步
      await refreshAllOptions()
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || '批量更新状态失败'
      error.value = errorMessage
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // 重置状态
  const resetState = () => {
    categories.value = []
    items.value = []
    categoriesWithItems.value = []
    loading.value = false
    error.value = null
  }

  return {
    // 状态
    categories,
    items,
    categoriesWithItems,
    loading,
    error,
    
    // 计算属性
    categoryCount,
    itemCount,
    getItemsByCategory,
    getFormattedOptions,
    toSelectOptions,
    
    // 方法
    clearError,
    setLoading,
    fetchCategories,
    fetchItemsByCategory,
    fetchItemsByCategoryCode,
    fetchAllOptions,
    initializeOptions,
    refreshOptions,
    refreshAllOptions,
    resetState,
    
    // CRUD 方法
    createCategory,
    updateCategory,
    deleteCategory,
    createItem,
    updateItem,
    deleteItem,
    batchDeleteItems,
    batchUpdateItemsStatus
  }
})