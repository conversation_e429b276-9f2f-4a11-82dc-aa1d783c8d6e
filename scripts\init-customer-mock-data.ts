import mysql from 'mysql2/promise';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root',
  database: process.env.DB_NAME || 'workchat_admin',
  charset: 'utf8mb4'
};

// 客户模拟数据
const mockCustomers = [
  {
    id: uuidv4(),
    name: '张伟',
    phone: '13800138001',
    email: '<EMAIL>',
    company: '华为技术有限公司',
    position: '产品经理',
    region: '深圳市南山区',
    gender: 'male',
    age: 32,
    source: 'website',
    level: 'high',
    address: '深圳市南山区科技园南区华为总部',
    decoration_type: 'full_decoration',
    house_status: 'new_house',
    budget_range: '20-50w',
    contact_time: 'morning',
    tags: JSON.stringify(['VIP客户', '高端需求']),
    notes: '对智能家居有浓厚兴趣，预算充足',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '李娜',
    phone: '13800138002',
    email: '<EMAIL>',
    company: '腾讯科技有限公司',
    position: '设计师',
    region: '深圳市南山区',
    gender: 'female',
    age: 28,
    source: 'referral',
    level: 'medium',
    address: '深圳市南山区后海大道腾讯大厦',
    decoration_type: 'half_decoration',
    house_status: 'second_hand',
    budget_range: '10-20w',
    contact_time: 'afternoon',
    tags: JSON.stringify(['设计师', '注重美观']),
    notes: '对设计要求较高，喜欢简约风格',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '王强',
    phone: '13800138003',
    email: '<EMAIL>',
    company: '阿里巴巴集团',
    position: '技术总监',
    region: '杭州市西湖区',
    gender: 'male',
    age: 35,
    source: 'social_media',
    level: 'vip',
    address: '杭州市西湖区文三路阿里巴巴西溪园区',
    decoration_type: 'full_decoration',
    house_status: 'villa',
    budget_range: '50w+',
    contact_time: 'evening',
    tags: JSON.stringify(['技术专家', '别墅装修']),
    notes: '别墅装修项目，预算充足，要求高端定制',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '刘芳',
    phone: '13800138004',
    email: '<EMAIL>',
    company: '百度在线网络技术有限公司',
    position: '市场经理',
    region: '北京市海淀区',
    gender: 'female',
    age: 30,
    source: 'advertisement',
    level: 'medium',
    address: '北京市海淀区上地十街百度科技园',
    decoration_type: 'clear_decoration',
    house_status: 'new_house',
    budget_range: '5-10w',
    contact_time: 'morning',
    tags: JSON.stringify(['首次装修', '预算有限']),
    notes: '新房装修，希望性价比高的方案',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '陈明',
    phone: '13800138005',
    email: '<EMAIL>',
    company: '字节跳动有限公司',
    position: '运营专员',
    region: '北京市朝阳区',
    gender: 'male',
    age: 26,
    source: 'exhibition',
    level: 'low',
    address: '北京市朝阳区知春路字节跳动大厦',
    decoration_type: 'soft_decoration',
    house_status: 'second_hand',
    budget_range: '5w',
    contact_time: 'afternoon',
    tags: JSON.stringify(['年轻客户', '软装需求']),
    notes: '二手房软装改造，预算较少',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '赵敏',
    phone: '13800138006',
    email: '<EMAIL>',
    company: '美团点评',
    position: '财务经理',
    region: '北京市朝阳区',
    gender: 'female',
    age: 33,
    source: 'website',
    level: 'high',
    address: '北京市朝阳区望京东路美团总部',
    decoration_type: 'full_decoration',
    house_status: 'old_house',
    budget_range: '20-50w',
    contact_time: 'evening',
    tags: JSON.stringify(['老房翻新', '全包装修']),
    notes: '老房全面翻新，要求环保材料',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '孙磊',
    phone: '13800138007',
    email: '<EMAIL>',
    company: '小米科技有限责任公司',
    position: '硬件工程师',
    region: '北京市海淀区',
    gender: 'male',
    age: 29,
    source: 'referral',
    level: 'medium',
    address: '北京市海淀区清河中街小米科技园',
    decoration_type: 'half_decoration',
    house_status: 'new_house',
    budget_range: '10-20w',
    contact_time: 'morning',
    tags: JSON.stringify(['科技爱好者', '智能家居']),
    notes: '对智能家居产品有特殊需求',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '周丽',
    phone: '13800138008',
    email: '<EMAIL>',
    company: '滴滴出行科技有限公司',
    position: '人事主管',
    region: '北京市海淀区',
    gender: 'female',
    age: 31,
    source: 'social_media',
    level: 'medium',
    address: '北京市海淀区中关村软件园滴滴大厦',
    decoration_type: 'clear_decoration',
    house_status: 'second_hand',
    budget_range: '10-20w',
    contact_time: 'afternoon',
    tags: JSON.stringify(['二手房装修', '简约风格']),
    notes: '喜欢简约现代风格，注重实用性',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '吴刚',
    phone: '13800138009',
    email: '<EMAIL>',
    company: '京东集团',
    position: '物流总监',
    region: '北京市大兴区',
    gender: 'male',
    age: 38,
    source: 'advertisement',
    level: 'vip',
    address: '北京市大兴区京东总部大楼',
    decoration_type: 'full_decoration',
    house_status: 'villa',
    budget_range: '50w+',
    contact_time: 'evening',
    tags: JSON.stringify(['高管客户', '别墅项目']),
    notes: '别墅装修，要求高端大气，预算充足',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '郑雪',
    phone: '13800138010',
    email: '<EMAIL>',
    company: '网易有道信息技术有限公司',
    position: '产品运营',
    region: '北京市海淀区',
    gender: 'female',
    age: 27,
    source: 'exhibition',
    level: 'low',
    address: '北京市海淀区中关村融科资讯中心网易大厦',
    decoration_type: 'soft_decoration',
    house_status: 'new_house',
    budget_range: '5w',
    contact_time: 'morning',
    tags: JSON.stringify(['新房软装', '年轻客户']),
    notes: '新房软装需求，预算有限，追求性价比',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '冯涛',
    phone: '13800138011',
    email: '<EMAIL>',
    company: '蚂蚁金服',
    position: '风控专家',
    region: '杭州市余杭区',
    gender: 'male',
    age: 34,
    source: 'website',
    level: 'high',
    address: '杭州市余杭区文一西路蚂蚁Z空间',
    decoration_type: 'full_decoration',
    house_status: 'new_house',
    budget_range: '20-50w',
    contact_time: 'afternoon',
    tags: JSON.stringify(['金融行业', '品质要求高']),
    notes: '对装修品质要求很高，注重环保和健康',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '何静',
    phone: '13800138012',
    email: '<EMAIL>',
    company: '新浪微博',
    position: '内容编辑',
    region: '北京市朝阳区',
    gender: 'female',
    age: 25,
    source: 'referral',
    level: 'low',
    address: '北京市朝阳区西大望路新浪总部大厦',
    decoration_type: 'half_decoration',
    house_status: 'second_hand',
    budget_range: '5-10w',
    contact_time: 'evening',
    tags: JSON.stringify(['媒体行业', '文艺青年']),
    notes: '喜欢文艺风格，预算有限但有品味',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '许峰',
    phone: '13800138013',
    email: '<EMAIL>',
    company: '搜狐公司',
    position: '技术架构师',
    region: '北京市海淀区',
    gender: 'male',
    age: 36,
    source: 'social_media',
    level: 'high',
    address: '北京市海淀区中关村东路搜狐网络大厦',
    decoration_type: 'full_decoration',
    house_status: 'old_house',
    budget_range: '20-50w',
    contact_time: 'morning',
    tags: JSON.stringify(['技术专家', '老房改造']),
    notes: '老房全面改造，对智能化要求较高',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '苏梅',
    phone: '13800138014',
    email: '<EMAIL>',
    company: '爱奇艺',
    position: '视频编辑',
    region: '北京市海淀区',
    gender: 'female',
    age: 28,
    source: 'advertisement',
    level: 'medium',
    address: '北京市海淀区上地西路爱奇艺大厦',
    decoration_type: 'clear_decoration',
    house_status: 'new_house',
    budget_range: '10-20w',
    contact_time: 'afternoon',
    tags: JSON.stringify(['影视行业', '创意需求']),
    notes: '从事影视行业，对空间设计有独特见解',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '邓华',
    phone: '13800138015',
    email: '<EMAIL>',
    company: '快手科技',
    position: '算法工程师',
    region: '北京市海淀区',
    gender: 'male',
    age: 30,
    source: 'exhibition',
    level: 'medium',
    address: '北京市海淀区上地三街快手科技大厦',
    decoration_type: 'soft_decoration',
    house_status: 'second_hand',
    budget_range: '5-10w',
    contact_time: 'evening',
    tags: JSON.stringify(['算法工程师', '理性消费']),
    notes: '理性消费者，注重性价比和实用性',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '曹琳',
    phone: '13800138016',
    email: '<EMAIL>',
    company: '美图公司',
    position: 'UI设计师',
    region: '厦门市思明区',
    gender: 'female',
    age: 26,
    source: 'website',
    level: 'medium',
    address: '厦门市思明区软件园二期美图大厦',
    decoration_type: 'half_decoration',
    house_status: 'new_house',
    budget_range: '10-20w',
    contact_time: 'morning',
    tags: JSON.stringify(['设计师', '美学要求高']),
    notes: '专业设计师，对色彩搭配和空间美学要求很高',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '田勇',
    phone: '13800138017',
    email: '<EMAIL>',
    company: '360安全科技',
    position: '安全专家',
    region: '北京市朝阳区',
    gender: 'male',
    age: 33,
    source: 'referral',
    level: 'high',
    address: '北京市朝阳区酒仙桥路360大厦',
    decoration_type: 'full_decoration',
    house_status: 'villa',
    budget_range: '50w+',
    contact_time: 'afternoon',
    tags: JSON.stringify(['安全专家', '别墅装修']),
    notes: '别墅装修项目，对安全性和隐私保护要求较高',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '贾丽',
    phone: '13800138018',
    email: '<EMAIL>',
    company: '携程旅行网',
    position: '产品经理',
    region: '上海市长宁区',
    gender: 'female',
    age: 29,
    source: 'social_media',
    level: 'medium',
    address: '上海市长宁区金钟路携程大厦',
    decoration_type: 'clear_decoration',
    house_status: 'old_house',
    budget_range: '10-20w',
    contact_time: 'evening',
    tags: JSON.stringify(['旅游行业', '国际化视野']),
    notes: '经常出差，希望装修风格简洁实用',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '韩斌',
    phone: '13800138019',
    email: '<EMAIL>',
    company: '拼多多',
    position: '运营总监',
    region: '上海市长宁区',
    gender: 'male',
    age: 35,
    source: 'advertisement',
    level: 'vip',
    address: '上海市长宁区宣化路拼多多总部',
    decoration_type: 'full_decoration',
    house_status: 'new_house',
    budget_range: '50w+',
    contact_time: 'morning',
    tags: JSON.stringify(['高管客户', '品质生活']),
    notes: '高管客户，追求高品质生活，预算充足',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '秦雨',
    phone: '13800138020',
    email: '<EMAIL>',
    company: '哔哩哔哩',
    position: '内容运营',
    region: '上海市杨浦区',
    gender: 'female',
    age: 24,
    source: 'exhibition',
    level: 'low',
    address: '上海市杨浦区政立路哔哩哔哩大厦',
    decoration_type: 'soft_decoration',
    house_status: 'second_hand',
    budget_range: '5w',
    contact_time: 'afternoon',
    tags: JSON.stringify(['年轻客户', '二次元文化']),
    notes: '年轻客户，喜欢二次元文化，预算有限',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '姚军',
    phone: '13800138021',
    email: '<EMAIL>',
    company: '顺丰速运',
    position: '区域经理',
    region: '深圳市福田区',
    gender: 'male',
    age: 37,
    source: 'website',
    level: 'high',
    address: '深圳市福田区泰然九路顺丰总部大厦',
    decoration_type: 'full_decoration',
    house_status: 'new_house',
    budget_range: '20-50w',
    contact_time: 'evening',
    tags: JSON.stringify(['物流行业', '实用主义']),
    notes: '物流行业背景，注重实用性和耐用性',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '钱芳',
    phone: '13800138022',
    email: '<EMAIL>',
    company: '大疆创新',
    position: '硬件工程师',
    region: '深圳市南山区',
    gender: 'female',
    age: 28,
    source: 'referral',
    level: 'medium',
    address: '深圳市南山区高新南四道大疆天空之城',
    decoration_type: 'half_decoration',
    house_status: 'second_hand',
    budget_range: '10-20w',
    contact_time: 'morning',
    tags: JSON.stringify(['硬件工程师', '科技感']),
    notes: '硬件工程师，喜欢科技感强的设计风格',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '沈涛',
    phone: '13800138023',
    email: '<EMAIL>',
    company: '比亚迪股份有限公司',
    position: '研发工程师',
    region: '深圳市坪山区',
    gender: 'male',
    age: 31,
    source: 'social_media',
    level: 'medium',
    address: '深圳市坪山区比亚迪路比亚迪总部',
    decoration_type: 'clear_decoration',
    house_status: 'old_house',
    budget_range: '10-20w',
    contact_time: 'afternoon',
    tags: JSON.stringify(['汽车行业', '环保理念']),
    notes: '汽车行业从业者，注重环保和节能',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '卢静',
    phone: '13800138024',
    email: '<EMAIL>',
    company: '平安科技',
    position: '数据分析师',
    region: '深圳市福田区',
    gender: 'female',
    age: 27,
    source: 'advertisement',
    level: 'medium',
    address: '深圳市福田区益田路平安金融中心',
    decoration_type: 'soft_decoration',
    house_status: 'new_house',
    budget_range: '5-10w',
    contact_time: 'evening',
    tags: JSON.stringify(['金融科技', '数据驱动']),
    notes: '数据分析师，理性决策，注重性价比',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '薛峰',
    phone: '13800138025',
    email: '<EMAIL>',
    company: '万科企业股份有限公司',
    position: '项目经理',
    region: '深圳市盐田区',
    gender: 'male',
    age: 39,
    source: 'exhibition',
    level: 'vip',
    address: '深圳市盐田区大梅沙万科中心',
    decoration_type: 'full_decoration',
    house_status: 'villa',
    budget_range: '50w+',
    contact_time: 'morning',
    tags: JSON.stringify(['房地产行业', '别墅项目']),
    notes: '房地产行业专家，别墅装修，要求专业品质',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '常丽',
    phone: '13800138026',
    email: '<EMAIL>',
    company: '招商银行股份有限公司',
    position: '理财经理',
    region: '深圳市福田区',
    gender: 'female',
    age: 32,
    source: 'website',
    level: 'high',
    address: '深圳市福田区深南大道招商银行大厦',
    decoration_type: 'full_decoration',
    house_status: 'new_house',
    budget_range: '20-50w',
    contact_time: 'afternoon',
    tags: JSON.stringify(['银行业', '理财专家']),
    notes: '银行理财经理，注重投资回报和保值增值',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '马强',
    phone: '13800138027',
    email: '<EMAIL>',
    company: '中兴通讯股份有限公司',
    position: '通信工程师',
    region: '深圳市南山区',
    gender: 'male',
    age: 34,
    source: 'referral',
    level: 'medium',
    address: '深圳市南山区高新技术产业园中兴通讯大厦',
    decoration_type: 'half_decoration',
    house_status: 'second_hand',
    budget_range: '10-20w',
    contact_time: 'evening',
    tags: JSON.stringify(['通信行业', '技术专业']),
    notes: '通信工程师，对网络布线和智能化有专业要求',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '范雪',
    phone: '13800138028',
    email: '<EMAIL>',
    company: '海康威视数字技术股份有限公司',
    position: '产品经理',
    region: '杭州市滨江区',
    gender: 'female',
    age: 29,
    source: 'social_media',
    level: 'medium',
    address: '杭州市滨江区阡陌路海康威视科技园',
    decoration_type: 'clear_decoration',
    house_status: 'old_house',
    budget_range: '10-20w',
    contact_time: 'morning',
    tags: JSON.stringify(['安防行业', '安全意识强']),
    notes: '安防行业从业者，对家居安全系统要求较高',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '邱涛',
    phone: '13800138029',
    email: '<EMAIL>',
    company: '恒生电子股份有限公司',
    position: '软件架构师',
    region: '杭州市滨江区',
    gender: 'male',
    age: 36,
    source: 'advertisement',
    level: 'high',
    address: '杭州市滨江区江南大道恒生大厦',
    decoration_type: 'full_decoration',
    house_status: 'new_house',
    budget_range: '20-50w',
    contact_time: 'afternoon',
    tags: JSON.stringify(['金融软件', '系统架构']),
    notes: '软件架构师，对空间布局和功能性要求很高',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '石梅',
    phone: '13800138030',
    email: '<EMAIL>',
    company: '浙江吉利控股集团有限公司',
    position: '设计师',
    region: '杭州市西湖区',
    gender: 'female',
    age: 26,
    source: 'exhibition',
    level: 'medium',
    address: '杭州市西湖区梅灵南路吉利汽车研究院',
    decoration_type: 'soft_decoration',
    house_status: 'second_hand',
    budget_range: '5-10w',
    contact_time: 'evening',
    tags: JSON.stringify(['汽车设计', '美学追求']),
    notes: '汽车设计师，对线条和造型有独特审美',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '龚华',
    phone: '13800138031',
    email: '<EMAIL>',
    company: '网易（杭州）网络有限公司',
    position: '游戏策划',
    region: '杭州市滨江区',
    gender: 'male',
    age: 28,
    source: 'website',
    level: 'medium',
    address: '杭州市滨江区长河路网易大厦',
    decoration_type: 'half_decoration',
    house_status: 'new_house',
    budget_range: '10-20w',
    contact_time: 'morning',
    tags: JSON.stringify(['游戏行业', '创意空间']),
    notes: '游戏策划师，希望有创意工作空间和娱乐区域',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '汤丽',
    phone: '13800138032',
    email: '<EMAIL>',
    company: '蔚来汽车',
    position: '用户运营',
    region: '上海市嘉定区',
    gender: 'female',
    age: 27,
    source: 'referral',
    level: 'medium',
    address: '上海市嘉定区安亭镇蔚来汽车总部',
    decoration_type: 'clear_decoration',
    house_status: 'old_house',
    budget_range: '10-20w',
    contact_time: 'afternoon',
    tags: JSON.stringify(['新能源汽车', '用户体验']),
    notes: '用户运营专家，注重用户体验和生活品质',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '毛强',
    phone: '13800138033',
    email: '<EMAIL>',
    company: '理想汽车',
    position: '产品经理',
    region: '北京市顺义区',
    gender: 'male',
    age: 33,
    source: 'social_media',
    level: 'high',
    address: '北京市顺义区理想汽车制造基地',
    decoration_type: 'full_decoration',
    house_status: 'villa',
    budget_range: '50w+',
    contact_time: 'evening',
    tags: JSON.stringify(['汽车产品', '别墅装修']),
    notes: '汽车产品经理，别墅装修，追求科技感和舒适性',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '戴雪',
    phone: '13800138034',
    email: '<EMAIL>',
    company: '小鹏汽车',
    position: '软件工程师',
    region: '广州市天河区',
    gender: 'female',
    age: 25,
    source: 'advertisement',
    level: 'low',
    address: '广州市天河区科韵路小鹏汽车总部',
    decoration_type: 'soft_decoration',
    house_status: 'new_house',
    budget_range: '5w',
    contact_time: 'morning',
    tags: JSON.stringify(['软件工程师', '年轻客户']),
    notes: '年轻软件工程师，预算有限，追求简约时尚',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '江涛',
    phone: '13800138035',
    email: '<EMAIL>',
    company: '广汽集团',
    position: '研发总监',
    region: '广州市番禺区',
    gender: 'male',
    age: 40,
    source: 'exhibition',
    level: 'vip',
    address: '广州市番禺区汽车城广汽中心',
    decoration_type: 'full_decoration',
    house_status: 'villa',
    budget_range: '50w+',
    contact_time: 'afternoon',
    tags: JSON.stringify(['汽车研发', '高管客户']),
    notes: '汽车研发总监，别墅装修，要求高端定制',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '严丽',
    phone: '13800138036',
    email: '<EMAIL>',
    company: '珠海格力电器股份有限公司',
    position: '市场总监',
    region: '珠海市香洲区',
    gender: 'female',
    age: 35,
    source: 'website',
    level: 'high',
    address: '珠海市香洲区前山金鸡西路格力电器总部',
    decoration_type: 'full_decoration',
    house_status: 'new_house',
    budget_range: '20-50w',
    contact_time: 'evening',
    tags: JSON.stringify(['家电行业', '市场专家']),
    notes: '家电行业市场总监，对家电配置和智能化要求高',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '赖强',
    phone: '13800138037',
    email: '<EMAIL>',
    company: '美的集团股份有限公司',
    position: '技术专家',
    region: '佛山市顺德区',
    gender: 'male',
    age: 32,
    source: 'referral',
    level: 'medium',
    address: '佛山市顺德区北滘镇美的总部大楼',
    decoration_type: 'half_decoration',
    house_status: 'second_hand',
    budget_range: '10-20w',
    contact_time: 'morning',
    tags: JSON.stringify(['家电技术', '实用主义']),
    notes: '家电技术专家，注重实用性和智能化集成',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '金雪',
    phone: '13800138038',
    email: '<EMAIL>',
    company: 'OPPO广东移动通信有限公司',
    position: 'UI设计师',
    region: '东莞市长安镇',
    gender: 'female',
    age: 26,
    source: 'social_media',
    level: 'medium',
    address: '东莞市长安镇乌沙海滨大道OPPO总部',
    decoration_type: 'clear_decoration',
    house_status: 'old_house',
    budget_range: '10-20w',
    contact_time: 'afternoon',
    tags: JSON.stringify(['手机设计', '美学要求']),
    notes: 'UI设计师，对色彩和视觉效果要求很高',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '白涛',
    phone: '13800138039',
    email: '<EMAIL>',
    company: 'vivo通信科技有限公司',
    position: '产品经理',
    region: '东莞市长安镇',
    gender: 'male',
    age: 30,
    source: 'advertisement',
    level: 'medium',
    address: '东莞市长安镇步步高大道vivo总部',
    decoration_type: 'soft_decoration',
    house_status: 'new_house',
    budget_range: '10-20w',
    contact_time: 'evening',
    tags: JSON.stringify(['手机产品', '年轻时尚']),
    notes: '手机产品经理，追求年轻时尚的装修风格',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '孔丽',
    phone: '13800138040',
    email: '<EMAIL>',
    company: '一汽集团',
    position: '质量工程师',
    region: '长春市朝阳区',
    gender: 'female',
    age: 31,
    source: 'exhibition',
    level: 'medium',
    address: '长春市朝阳区东风大街一汽总部',
    decoration_type: 'half_decoration',
    house_status: 'second_hand',
    budget_range: '10-20w',
    contact_time: 'morning',
    tags: JSON.stringify(['汽车质量', '严谨细致']),
    notes: '质量工程师，对装修质量和工艺要求严格',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '水强',
    phone: '13800138041',
    email: '<EMAIL>',
    company: '上汽集团',
    position: '设计总监',
    region: '上海市嘉定区',
    gender: 'male',
    age: 38,
    source: 'website',
    level: 'vip',
    address: '上海市嘉定区申富路上汽集团总部',
    decoration_type: 'full_decoration',
    house_status: 'villa',
    budget_range: '50w+',
    contact_time: 'afternoon',
    tags: JSON.stringify(['汽车设计', '别墅项目']),
    notes: '汽车设计总监，别墅装修，追求艺术性和功能性',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '左雪',
    phone: '13800138042',
    email: '<EMAIL>',
    company: '东风汽车集团有限公司',
    position: '市场经理',
    region: '武汉市经济技术开发区',
    gender: 'female',
    age: 29,
    source: 'referral',
    level: 'medium',
    address: '武汉市经济技术开发区东风大道东风汽车总部',
    decoration_type: 'clear_decoration',
    house_status: 'old_house',
    budget_range: '10-20w',
    contact_time: 'evening',
    tags: JSON.stringify(['汽车市场', '商务需求']),
    notes: '汽车市场经理，需要商务接待功能的装修设计',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '平涛',
    phone: '13800138043',
    email: '<EMAIL>',
    company: '长安汽车股份有限公司',
    position: '研发工程师',
    region: '重庆市江北区',
    gender: 'male',
    age: 27,
    source: 'social_media',
    level: 'low',
    address: '重庆市江北区建新东路长安汽车全球研发中心',
    decoration_type: 'soft_decoration',
    house_status: 'new_house',
    budget_range: '5-10w',
    contact_time: 'morning',
    tags: JSON.stringify(['汽车研发', '年轻工程师']),
    notes: '年轻汽车工程师，预算有限，追求简约实用',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '原丽',
    phone: '13800138044',
    email: '<EMAIL>',
    company: '吉利汽车控股有限公司',
    position: '品牌经理',
    region: '杭州市滨江区',
    gender: 'female',
    age: 28,
    source: 'advertisement',
    level: 'medium',
    address: '杭州市滨江区江陵路吉利汽车研究院',
    decoration_type: 'half_decoration',
    house_status: 'second_hand',
    budget_range: '10-20w',
    contact_time: 'afternoon',
    tags: JSON.stringify(['汽车品牌', '时尚品味']),
    notes: '汽车品牌经理，对品牌形象和时尚感要求较高',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '史强',
    phone: '13800138045',
    email: '<EMAIL>',
    company: '比亚迪汽车工业有限公司',
    position: '电池工程师',
    region: '深圳市龙岗区',
    gender: 'male',
    age: 33,
    source: 'exhibition',
    level: 'high',
    address: '深圳市龙岗区葵涌街道比亚迪工业园',
    decoration_type: 'full_decoration',
    house_status: 'new_house',
    budget_range: '20-50w',
    contact_time: 'evening',
    tags: JSON.stringify(['新能源', '技术专家']),
    notes: '电池技术专家，对环保和节能技术要求很高',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '邓雪',
    phone: '13800138046',
    email: '<EMAIL>',
    company: '蔚来汽车科技有限公司',
    position: '软件工程师',
    region: '合肥市经济技术开发区',
    gender: 'female',
    age: 26,
    source: 'website',
    level: 'medium',
    address: '合肥市经济技术开发区蔚来汽车制造基地',
    decoration_type: 'clear_decoration',
    house_status: 'old_house',
    budget_range: '5-10w',
    contact_time: 'morning',
    tags: JSON.stringify(['汽车软件', '智能化']),
    notes: '汽车软件工程师，对智能家居系统有专业需求',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '冷涛',
    phone: '13800138047',
    email: '<EMAIL>',
    company: '理想汽车制造有限公司',
    position: '制造工程师',
    region: '常州市武进区',
    gender: 'male',
    age: 30,
    source: 'referral',
    level: 'medium',
    address: '常州市武进区理想汽车制造基地',
    decoration_type: 'soft_decoration',
    house_status: 'second_hand',
    budget_range: '10-20w',
    contact_time: 'afternoon',
    tags: JSON.stringify(['汽车制造', '工程思维']),
    notes: '制造工程师，注重功能性和实用性设计',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '谷丽',
    phone: '13800138048',
    email: '<EMAIL>',
    company: '小鹏汽车科技有限公司',
    position: '自动驾驶工程师',
    region: '广州市黄埔区',
    gender: 'female',
    age: 28,
    source: 'social_media',
    level: 'high',
    address: '广州市黄埔区科学城小鹏汽车智能网联科技园',
    decoration_type: 'half_decoration',
    house_status: 'new_house',
    budget_range: '20-50w',
    contact_time: 'evening',
    tags: JSON.stringify(['自动驾驶', '前沿技术']),
    notes: '自动驾驶工程师，对前沿科技和智能化要求很高',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '游强',
    phone: '13800138049',
    email: '<EMAIL>',
    company: '威马汽车科技集团有限公司',
    position: '产品总监',
    region: '温州市瓯海区',
    gender: 'male',
    age: 36,
    source: 'advertisement',
    level: 'vip',
    address: '温州市瓯海区威马汽车智能产业园',
    decoration_type: 'full_decoration',
    house_status: 'villa',
    budget_range: '50w+',
    contact_time: 'morning',
    tags: JSON.stringify(['汽车产品', '高管客户']),
    notes: '汽车产品总监，别墅装修，追求高端品质和科技感',
    assigned_to: null,
    status: 'active'
  },
  {
    id: uuidv4(),
    name: '邹雪',
    phone: '13800138050',
    email: '<EMAIL>',
    company: '零跑汽车有限公司',
    position: 'AI算法工程师',
    region: '杭州市滨江区',
    gender: 'female',
    age: 25,
    source: 'exhibition',
    level: 'medium',
    address: '杭州市滨江区长河街道零跑汽车总部',
    decoration_type: 'clear_decoration',
    house_status: 'new_house',
    budget_range: '10-20w',
    contact_time: 'afternoon',
    tags: JSON.stringify(['AI算法', '年轻专家']),
    notes: 'AI算法工程师，年轻专业人士，追求简约科技风',
    assigned_to: null,
    status: 'active'
  }
];

// 初始化客户数据函数
export async function initCustomerData() {
  let connection;
  
  try {
    console.log('开始连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功');
    
    // 检查customers表是否存在
    const [tables] = await connection.execute(
      "SHOW TABLES LIKE 'customers'"
    ) as [any[], any];
    
    if (tables.length === 0) {
      console.error('customers表不存在，请先运行建表脚本');
      return;
    }
    
    console.log('开始清空现有客户数据...');
    await connection.execute('DELETE FROM customers');
    console.log('现有客户数据已清空');
    
    console.log('开始插入模拟客户数据...');
    
    for (const customer of mockCustomers) {
      const sql = `
        INSERT INTO customers (
          id, name, phone, email, company, position, region, gender, age,
          source, level, address, decoration_type, house_status, budget_range,
          contact_time, tags, notes, assigned_to, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      const values = [
        customer.id,
        customer.name,
        customer.phone,
        customer.email,
        customer.company,
        customer.position,
        customer.region,
        customer.gender,
        customer.age,
        customer.source,
        customer.level,
        customer.address,
        customer.decoration_type,
        customer.house_status,
        customer.budget_range,
        customer.contact_time,
        customer.tags,
        customer.notes,
        customer.assigned_to,
        customer.status
      ];
      
      await connection.execute(sql, values);
    }
    
    console.log(`成功插入 ${mockCustomers.length} 条客户数据`);
    
    // 验证数据插入
    const [result] = await connection.execute('SELECT COUNT(*) as count FROM customers') as [any[], any];
    console.log(`数据库中现有客户数量: ${(result[0] as any).count}`);
    
    // 显示部分数据样例
    const [samples] = await connection.execute(
      'SELECT id, name, phone, company, source, level, decoration_type, house_status, budget_range FROM customers LIMIT 5'
    );
    console.log('\n插入的数据样例:');
    console.table(samples);
    
  } catch (error) {
    console.error('初始化客户数据时发生错误:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 直接运行初始化函数
initCustomerData();