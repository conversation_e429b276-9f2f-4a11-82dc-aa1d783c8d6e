-- MySQL数据库表结构创建脚本
-- 企业微信客户管理系统数据库表结构
-- 创建时间: 2024-01-17

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `workchat_admin` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `workchat_admin`;

-- 1. 部门表
CREATE TABLE IF NOT EXISTS `departments` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `name` varchar(100) NOT NULL,
  `code` varchar(50) NOT NULL UNIQUE,
  `description` text,
  `parent_id` varchar(36),
  `manager_id` varchar(36),
  `sort_order` int NOT NULL DEFAULT 0,
  `is_active` boolean NOT NULL DEFAULT true,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_departments_name` (`name`),
  INDEX `idx_departments_code` (`code`),
  INDEX `idx_departments_parent_id` (`parent_id`),
  INDEX `idx_departments_manager_id` (`manager_id`),
  INDEX `idx_departments_sort_order` (`sort_order`),
  INDEX `idx_departments_is_active` (`is_active`),
  CONSTRAINT `fk_departments_parent` FOREIGN KEY (`parent_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';

-- 2. 用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `email` varchar(255) NOT NULL UNIQUE,
  `username` varchar(100),
  `name` varchar(100) NOT NULL DEFAULT '',
  `avatar_url` text,
  `phone` varchar(20),
  `avatar` varchar(500),
  `department_id` varchar(36),
  `department` varchar(100),
  `position` varchar(100),
  `role` varchar(50),
  `is_active` boolean NOT NULL DEFAULT true,
  `status` VARCHAR(50) DEFAULT 'active',
  `last_login_at` timestamp NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_users_email` (`email`),
  INDEX `idx_users_username` (`username`),
  INDEX `idx_users_department_id` (`department_id`),
  INDEX `idx_users_role` (`role`),
  INDEX `idx_users_is_active` (`is_active`),
  INDEX `idx_users_status` (`status`),
  INDEX `idx_users_created_at` (`created_at`),
  CONSTRAINT `fk_users_department` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 3. 角色表
CREATE TABLE IF NOT EXISTS `roles` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `name` varchar(50) NOT NULL UNIQUE,
  `display_name` varchar(100),
  `description` text,
  `permissions` json,
  `is_active` boolean NOT NULL DEFAULT true,
  `is_system` boolean NOT NULL DEFAULT false,
  `status` VARCHAR(50) DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_roles_name` (`name`),
  INDEX `idx_roles_display_name` (`display_name`),
  INDEX `idx_roles_is_active` (`is_active`),
  INDEX `idx_roles_is_system` (`is_system`),
  INDEX `idx_roles_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 4. 权限表
CREATE TABLE IF NOT EXISTS `permissions` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `name` varchar(100) NOT NULL UNIQUE,
  `display_name` varchar(100),
  `description` text,
  `module` varchar(100),
  `resource` varchar(100) NOT NULL,
  `action` varchar(50) NOT NULL,
  `is_active` boolean NOT NULL DEFAULT true,
  `is_system` boolean NOT NULL DEFAULT false,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_permissions_name` (`name`),
  INDEX `idx_permissions_display_name` (`display_name`),
  INDEX `idx_permissions_module` (`module`),
  INDEX `idx_permissions_resource` (`resource`),
  INDEX `idx_permissions_action` (`action`),
  INDEX `idx_permissions_is_active` (`is_active`),
  INDEX `idx_permissions_is_system` (`is_system`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- 5. 角色权限关联表
CREATE TABLE IF NOT EXISTS `role_permissions` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `role_id` varchar(36) NOT NULL,
  `permission_id` varchar(36) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`),
  INDEX `idx_role_permissions_role_id` (`role_id`),
  INDEX `idx_role_permissions_permission_id` (`permission_id`),
  CONSTRAINT `fk_role_permissions_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_role_permissions_permission` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- 6. 用户角色关联表
CREATE TABLE IF NOT EXISTS `user_roles` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `user_id` varchar(36) NOT NULL,
  `role_id` varchar(36) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY `uk_user_role` (`user_id`, `role_id`),
  INDEX `idx_user_roles_user_id` (`user_id`),
  INDEX `idx_user_roles_role_id` (`role_id`),
  CONSTRAINT `fk_user_roles_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_roles_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- 7. 选项分类表
CREATE TABLE IF NOT EXISTS `option_categories` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `name` varchar(100) NOT NULL DEFAULT '',
  `code` varchar(50) NOT NULL UNIQUE,
  `description` text,
  `sort_order` int NOT NULL DEFAULT 0,
  `is_active` boolean NOT NULL DEFAULT true,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_option_categories_code` (`code`),
  INDEX `idx_option_categories_sort_order` (`sort_order`),
  INDEX `idx_option_categories_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='选项分类表';

-- 8. 选项项表
CREATE TABLE IF NOT EXISTS `option_items` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `category_id` varchar(36) NOT NULL,
  `name` varchar(100) NOT NULL DEFAULT '',
  `code` varchar(50),
  `label` varchar(100),
  `value` varchar(100) NOT NULL,
  `color` varchar(50),
  `icon` varchar(255),
  `extra_data` json,
  `description` text,
  `sort_order` int NOT NULL DEFAULT 0,
  `is_active` boolean NOT NULL DEFAULT true,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_option_items_category_id` (`category_id`),
  INDEX `idx_option_items_code` (`code`),
  INDEX `idx_option_items_label` (`label`),
  INDEX `idx_option_items_value` (`value`),
  INDEX `idx_option_items_color` (`color`),
  INDEX `idx_option_items_icon` (`icon`),
  INDEX `idx_option_items_sort_order` (`sort_order`),
  INDEX `idx_option_items_is_active` (`is_active`),
  UNIQUE KEY `uk_category_value` (`category_id`, `value`),
  CONSTRAINT `fk_option_items_category` FOREIGN KEY (`category_id`) REFERENCES `option_categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='选项项表';

-- 9. 客户表
CREATE TABLE IF NOT EXISTS `customers` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20),
  `email` varchar(255),
  `company` varchar(200),
  `position` varchar(100),
  `region` varchar(100),
  `gender` varchar(10),
  `age` int,
  `source` varchar(50),
  `level` varchar(20),
  `address` text,
  `decoration_type` varchar(50),
  `house_status` varchar(50),
  `budget_range` varchar(50),
  `contact_time` varchar(50),
  `tags` json,
  `notes` text,
  `assigned_to` varchar(36),
  `status` varchar(20) NOT NULL DEFAULT 'active',
  `last_contact_at` timestamp NULL,
  `next_follow_up_at` timestamp NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_customers_name` (`name`),
  INDEX `idx_customers_phone` (`phone`),
  INDEX `idx_customers_email` (`email`),
  INDEX `idx_customers_company` (`company`),
  INDEX `idx_customers_position` (`position`),
  INDEX `idx_customers_last_contact_at` (`last_contact_at`),
  INDEX `idx_customers_next_follow_up_at` (`next_follow_up_at`),
  INDEX `idx_customers_region` (`region`),
  INDEX `idx_customers_source` (`source`),
  INDEX `idx_customers_level` (`level`),
  INDEX `idx_customers_assigned_to` (`assigned_to`),
  INDEX `idx_customers_status` (`status`),
  INDEX `idx_customers_created_at` (`created_at`),
  CONSTRAINT `fk_customers_assigned_to` FOREIGN KEY (`assigned_to`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户表';

-- 10. 客户跟进记录表
CREATE TABLE IF NOT EXISTS `customer_follow_records` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `customer_id` varchar(36) NOT NULL,
  `created_by` varchar(36),
  `type` varchar(50) NOT NULL,
  `content` text NOT NULL,
  `next_follow_time` timestamp NULL,
  `status` varchar(20) NOT NULL DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_follow_records_customer_id` (`customer_id`),
  INDEX `idx_follow_records_created_by` (`created_by`),
  INDEX `idx_follow_records_type` (`type`),
  INDEX `idx_follow_records_status` (`status`),
  INDEX `idx_follow_records_next_follow_time` (`next_follow_time`),
  INDEX `idx_follow_records_created_at` (`created_at`),
  CONSTRAINT `fk_follow_records_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_follow_records_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户跟进记录表';

-- 10. 营销活动表
CREATE TABLE IF NOT EXISTS `marketing_campaigns` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `name` varchar(200) NOT NULL,
  `description` text,
  `type` varchar(50) NOT NULL,
  `cover_image` text,
  `status` varchar(20) NOT NULL DEFAULT 'draft',
  `start_date` date,
  `end_date` date,
  `start_time` timestamp NULL,
  `end_time` timestamp NULL,
  `budget` decimal(10,2),
  `spent` decimal(10,2) DEFAULT 0.00,
  `participant_count` int NOT NULL DEFAULT 0,
  `conversion_count` int DEFAULT 0,
  `share_count` int NOT NULL DEFAULT 0,
  `target_audience` text,
  `rules` json,
  `prizes` json,
  `created_by` varchar(36),
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_campaigns_name` (`name`),
  INDEX `idx_campaigns_type` (`type`),
  INDEX `idx_campaigns_status` (`status`),
  INDEX `idx_campaigns_start_date` (`start_date`),
  INDEX `idx_campaigns_end_date` (`end_date`),
  INDEX `idx_campaigns_start_time` (`start_time`),
  INDEX `idx_campaigns_end_time` (`end_time`),
  INDEX `idx_campaigns_created_by` (`created_by`),
  INDEX `idx_campaigns_created_at` (`created_at`),
  CONSTRAINT `fk_campaigns_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='营销活动表';

-- 11. 活动参与者表
CREATE TABLE IF NOT EXISTS `campaign_participants` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `campaign_id` varchar(36) NOT NULL,
  `customer_id` varchar(36) NOT NULL,
  `participation_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` varchar(20) NOT NULL DEFAULT 'participated',
  `notes` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY `uk_campaign_customer` (`campaign_id`, `customer_id`),
  INDEX `idx_participants_campaign_id` (`campaign_id`),
  INDEX `idx_participants_customer_id` (`customer_id`),
  INDEX `idx_participants_participation_date` (`participation_date`),
  INDEX `idx_participants_status` (`status`),
  CONSTRAINT `fk_participants_campaign` FOREIGN KEY (`campaign_id`) REFERENCES `marketing_campaigns` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_participants_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='活动参与者表';

-- 12. 活动分享表
CREATE TABLE IF NOT EXISTS `campaign_shares` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `campaign_id` varchar(36) NOT NULL,
  `customer_id` varchar(36) NOT NULL,
  `share_platform` varchar(50) NOT NULL,
  `share_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `share_content` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_shares_campaign_id` (`campaign_id`),
  INDEX `idx_shares_customer_id` (`customer_id`),
  INDEX `idx_shares_platform` (`share_platform`),
  INDEX `idx_shares_share_time` (`share_time`),
  CONSTRAINT `fk_shares_campaign` FOREIGN KEY (`campaign_id`) REFERENCES `marketing_campaigns` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_shares_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='活动分享表';

-- 13. 会议表
CREATE TABLE IF NOT EXISTS `meetings` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `title` varchar(255) NOT NULL,
  `customer_id` varchar(36),
  `description` text,
  `agenda` text,
  `materials` json,
  `recording_url` varchar(500),
  `notes` text,
  `summary` text,
  `action_items` json,
  `type` varchar(50),
  `meeting_time` timestamp,
  `duration` int,
  `attendees` json,
  `start_time` timestamp NOT NULL,
  `end_time` timestamp NOT NULL,
  `location` varchar(200),
  `meeting_url` varchar(500),
  `meeting_type` varchar(50) DEFAULT 'general',
  `status` varchar(20) NOT NULL DEFAULT 'scheduled',
  `created_by` varchar(36) NOT NULL,
  `organizer_id` varchar(36),
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_meetings_title` (`title`),
  INDEX `idx_meetings_type` (`type`),
  INDEX `idx_meetings_start_time` (`start_time`),
  INDEX `idx_meetings_end_time` (`end_time`),
  INDEX `idx_meetings_meeting_type` (`meeting_type`),
  INDEX `idx_meetings_status` (`status`),
  INDEX `idx_meetings_created_by` (`created_by`),
  INDEX `idx_meetings_organizer_id` (`organizer_id`),
  CONSTRAINT `fk_meetings_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会议表';

-- 14. 会议参与者表
CREATE TABLE IF NOT EXISTS `meeting_participants` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `meeting_id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `role` varchar(50) NOT NULL DEFAULT 'participant',
  `status` varchar(20) NOT NULL DEFAULT 'invited',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY `uk_meeting_user` (`meeting_id`, `user_id`),
  INDEX `idx_meeting_participants_meeting_id` (`meeting_id`),
  INDEX `idx_meeting_participants_user_id` (`user_id`),
  INDEX `idx_meeting_participants_role` (`role`),
  INDEX `idx_meeting_participants_status` (`status`),
  CONSTRAINT `fk_meeting_participants_meeting` FOREIGN KEY (`meeting_id`) REFERENCES `meetings` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_meeting_participants_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会议参与者表';

-- 15. 公海规则表
CREATE TABLE IF NOT EXISTS `pool_rules` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `name` varchar(100) NOT NULL,
  `description` text,
  `conditions` json NOT NULL,
  `actions` json NOT NULL DEFAULT ('[]'),
  `auto_release_days` int DEFAULT 30,
  `reminder_days` int DEFAULT 7,
  `max_claims_per_day` int DEFAULT NULL,
  `applies_to_roles` json,
  `applies_to_departments` json,
  `is_active` boolean NOT NULL DEFAULT true,
  `priority` int NOT NULL DEFAULT 0,
  `created_by` varchar(36),
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_pool_rules_name` (`name`),
  INDEX `idx_pool_rules_auto_release_days` (`auto_release_days`),
  INDEX `idx_pool_rules_reminder_days` (`reminder_days`),
  INDEX `idx_pool_rules_max_claims_per_day` (`max_claims_per_day`),
  INDEX `idx_pool_rules_is_active` (`is_active`),
  INDEX `idx_pool_rules_priority` (`priority`),
  INDEX `idx_pool_rules_created_by` (`created_by`),
  CONSTRAINT `fk_pool_rules_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公海规则表';

-- 16. 客户行为表
CREATE TABLE IF NOT EXISTS `customer_behaviors` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `customer_id` varchar(36) NOT NULL,
  `behavior_type` varchar(50) NOT NULL,
  `behavior_data` json,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `source` varchar(50),
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_behaviors_customer_id` (`customer_id`),
  INDEX `idx_behaviors_type` (`behavior_type`),
  INDEX `idx_behaviors_timestamp` (`timestamp`),
  INDEX `idx_behaviors_source` (`source`),
  CONSTRAINT `fk_behaviors_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户行为表';

-- 17. 微信客户跟踪表
CREATE TABLE IF NOT EXISTS `wechat_customer_tracking` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `customer_id` varchar(36) NOT NULL,
  `wechat_id` varchar(100),
  `nickname` varchar(100),
  `avatar_url` text,
  `last_interaction` timestamp,
  `interaction_count` int NOT NULL DEFAULT 0,
  `tags` json,
  `notes` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_wechat_tracking_customer_id` (`customer_id`),
  INDEX `idx_wechat_tracking_wechat_id` (`wechat_id`),
  INDEX `idx_wechat_tracking_last_interaction` (`last_interaction`),
  CONSTRAINT `fk_wechat_tracking_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信客户跟踪表';

-- 18. 销售漏斗统计表
CREATE TABLE IF NOT EXISTS `sales_funnel_stats` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `date` date NOT NULL,
  `stage` varchar(50) NOT NULL,
  `count` int NOT NULL DEFAULT 0,
  `conversion_rate` decimal(5,2),
  `team_id` varchar(36),
  `user_id` varchar(36),
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `uk_funnel_stats` (`date`, `stage`, `team_id`, `user_id`),
  INDEX `idx_funnel_stats_date` (`date`),
  INDEX `idx_funnel_stats_stage` (`stage`),
  INDEX `idx_funnel_stats_team_id` (`team_id`),
  INDEX `idx_funnel_stats_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='销售漏斗统计表';

-- 19. 客户价值分析表
CREATE TABLE IF NOT EXISTS `customer_value_analysis` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `customer_id` varchar(36) NOT NULL,
  `total_value` decimal(10,2) NOT NULL DEFAULT 0,
  `potential_value` decimal(10,2) NOT NULL DEFAULT 0,
  `interaction_score` int NOT NULL DEFAULT 0,
  `conversion_probability` decimal(5,2) NOT NULL DEFAULT 0,
  `last_calculated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_value_analysis_customer_id` (`customer_id`),
  INDEX `idx_value_analysis_total_value` (`total_value`),
  INDEX `idx_value_analysis_potential_value` (`potential_value`),
  INDEX `idx_value_analysis_last_calculated` (`last_calculated`),
  CONSTRAINT `fk_value_analysis_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户价值分析表';

-- 20. 跟进任务表
CREATE TABLE IF NOT EXISTS `follow_ups` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `customer_id` varchar(36) NOT NULL,
  `assigned_to` varchar(36),
  `title` varchar(200) DEFAULT '',
  `description` text,
  `type` varchar(50),
  `content` text,
  `due_date` timestamp,
  `priority` varchar(20) NOT NULL DEFAULT 'medium',
  `status` varchar(20) NOT NULL DEFAULT 'pending',
  `completed_at` timestamp NULL,
  `result` text,
  `next_action` varchar(200),
  `next_follow_up_at` timestamp NULL,
  `attachments` json,
  `tags` json,
  `notes` text,
  `created_by` varchar(36),
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_follow_ups_customer_id` (`customer_id`),
  INDEX `idx_follow_ups_assigned_to` (`assigned_to`),
  INDEX `idx_follow_ups_type` (`type`),
  INDEX `idx_follow_ups_due_date` (`due_date`),
  INDEX `idx_follow_ups_priority` (`priority`),
  INDEX `idx_follow_ups_status` (`status`),
  INDEX `idx_follow_ups_created_by` (`created_by`),
  INDEX `idx_follow_ups_created_at` (`created_at`),
  CONSTRAINT `fk_follow_ups_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_follow_ups_assigned_to` FOREIGN KEY (`assigned_to`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_follow_ups_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='跟进任务表';

-- 21. 公海客户表
CREATE TABLE IF NOT EXISTS `public_pool` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `customer_id` varchar(36) NOT NULL,
  `reason` varchar(200),
  `entered_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_assigned_to` varchar(36),
  `moved_by` varchar(36),
  `moved_at` timestamp NULL,
  `assignment_count` int NOT NULL DEFAULT 0,
  `claimed_by` varchar(36),
  `claimed_at` timestamp NULL,
  `notes` text,
  `status` varchar(20) NOT NULL DEFAULT 'available',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_public_pool_customer_id` (`customer_id`),
  INDEX `idx_public_pool_entered_at` (`entered_at`),
  INDEX `idx_public_pool_last_assigned_to` (`last_assigned_to`),
  INDEX `idx_public_pool_moved_by` (`moved_by`),
  INDEX `idx_public_pool_moved_at` (`moved_at`),
  INDEX `idx_public_pool_claimed_by` (`claimed_by`),
  INDEX `idx_public_pool_claimed_at` (`claimed_at`),
  INDEX `idx_public_pool_status` (`status`),
  CONSTRAINT `fk_public_pool_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_public_pool_last_assigned_to` FOREIGN KEY (`last_assigned_to`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_public_pool_moved_by` FOREIGN KEY (`moved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_public_pool_claimed_by` FOREIGN KEY (`claimed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公海客户表';

-- 22. 迁移日志表
CREATE TABLE IF NOT EXISTS `migration_logs` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `migration_id` varchar(100) NOT NULL,
  `table_name` varchar(100) NOT NULL,
  `operation` varchar(50) NOT NULL,
  `status` varchar(20) NOT NULL,
  `records_count` int NOT NULL DEFAULT 0,
  `error_message` text,
  `start_time` timestamp NOT NULL,
  `end_time` timestamp NULL,
  `duration_ms` int,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_migration_logs_migration_id` (`migration_id`),
  INDEX `idx_migration_logs_table_name` (`table_name`),
  INDEX `idx_migration_logs_operation` (`operation`),
  INDEX `idx_migration_logs_status` (`status`),
  INDEX `idx_migration_logs_start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='迁移日志表';

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 创建完成提示
SELECT 'MySQL数据库表结构创建完成！' as message;