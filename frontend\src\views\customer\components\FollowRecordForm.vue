<template>
  <div class="follow-record-form">
    <!-- 表单头部信息 -->
    <div class="form-header">
      <div class="header-info">
        <n-icon size="18" color="#1677ff">
          <phone-portrait-outline />
        </n-icon>
        <span class="header-title">{{ isEdit ? '编辑跟进记录' : '新增跟进记录' }}</span>
      </div>
      <div class="header-tips">
        <n-alert type="info" :show-icon="false" size="small">
          请详细记录与客户的沟通情况，有助于后续跟进和团队协作
        </n-alert>
      </div>
    </div>

    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="100px"
      require-mark-placement="right-hanging"
      size="medium"
    >
      <!-- 基础信息区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#1677ff">
            <time-outline />
          </n-icon>
          <span class="section-title">基础信息</span>
        </div>
        
        <n-grid :cols="2" :x-gap="20" :y-gap="16" responsive="screen">
          <n-form-item-gi label="跟进时间" path="follow_time">
            <n-date-picker
              v-model:value="followTimeValue"
              type="datetime"
              placeholder="选择跟进时间"
              style="width: 100%"
              :is-date-disabled="(ts: number) => ts > Date.now()"
              format="yyyy-MM-dd HH:mm"
              value-format="timestamp"
            />
          </n-form-item-gi>
          
          <n-form-item-gi label="跟进方式" path="followMethod">
            <n-select
              v-model:value="formData.followMethod"
              placeholder="选择跟进方式"
              :options="followMethodOptions"
              :render-label="renderMethodLabel"
            />
          </n-form-item-gi>
          
          <n-form-item-gi label="客户反馈" path="customerFeedback">
            <n-select
              v-model:value="formData.customerFeedback"
              placeholder="选择客户反馈"
              :options="feedbackOptions"
              :render-label="renderFeedbackLabel"
            />
          </n-form-item-gi>
          
          <n-form-item-gi label="跟进状态" path="status">
            <n-select
              v-model:value="formData.status"
              placeholder="选择跟进状态"
              :options="statusOptions"
              :render-label="renderStatusLabel"
            />
          </n-form-item-gi>
        </n-grid>
      </div>

      <!-- 跟进内容区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#52c41a">
            <document-text-outline />
          </n-icon>
          <span class="section-title">跟进内容</span>
        </div>
        
        <n-form-item label="详细内容" path="followContent">
          <n-input
            v-model:value="formData.followContent"
            type="textarea"
            placeholder="请详细描述本次跟进的具体内容：&#10;• 客户需求和关注点&#10;• 沟通要点和客户反应&#10;• 遇到的问题和解决方案&#10;• 客户的疑虑和担心&#10;• 下一步计划和行动"
            :rows="5"
            show-count
            maxlength="800"
            :input-props="{ spellcheck: false }"
          />
        </n-form-item>
      </div>

      <!-- 后续安排区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#faad14">
            <calendar-outline />
          </n-icon>
          <span class="section-title">后续安排</span>
        </div>
        
        <n-form-item label="下次跟进" path="next_follow_time">
          <n-space vertical style="width: 100%">
            <n-date-picker
              v-model:value="nextFollowTimeValue"
              type="datetime"
              placeholder="选择下次跟进时间（可选）"
              style="width: 100%"
              :is-date-disabled="(ts: number) => ts < Date.now()"
              format="yyyy-MM-dd HH:mm"
              value-format="timestamp"
              clearable
            />
            <n-text depth="3" style="font-size: 12px;">
              建议根据客户反馈情况合理安排下次跟进时间
            </n-text>
          </n-space>
        </n-form-item>
        
        <n-form-item label="备注信息">
          <n-input
            v-model:value="formData.remark"
            type="textarea"
            placeholder="其他需要记录的信息：&#10;• 客户特殊要求&#10;• 注意事项&#10;• 团队协作提醒"
            :rows="3"
            show-count
            maxlength="300"
            :input-props="{ spellcheck: false }"
          />
        </n-form-item>
      </div>

      <!-- 快速操作区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#722ed1">
            <flash-outline />
          </n-icon>
          <span class="section-title">快速操作</span>
        </div>
        
        <div class="quick-actions">
          <n-space>
            <n-button size="small" @click="fillQuickTemplate('interested')">
              客户感兴趣
            </n-button>
            <n-button size="small" @click="fillQuickTemplate('considering')">
              客户考虑中
            </n-button>
            <n-button size="small" @click="fillQuickTemplate('price_sensitive')">
              价格敏感
            </n-button>
            <n-button size="small" @click="fillQuickTemplate('no_contact')">
              无法联系
            </n-button>
          </n-space>
        </div>
      </div>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, h, type VNodeChild } from 'vue'
import {
  NForm,
  NFormItem,
  NFormItemGi,
  NGrid,
  NInput,
  NSelect,
  NDatePicker,
  NAlert,
  NSpace,
  NText,
  NButton,
  NIcon,
  NTag,
  type FormInst,
  type FormRules,
  type SelectRenderLabel
} from 'naive-ui'
import {
  PhonePortraitOutline,
  TimeOutline,
  DocumentTextOutline,
  CalendarOutline,
  FlashOutline,
  CallOutline,
  ChatbubbleOutline,
  MailOutline,
  HomeOutline,
  StorefrontOutline,
  HappyOutline,
  SadOutline,
  RemoveCircleOutline,
  CheckmarkCircleOutline,
  PauseCircleOutline,
  CloseCircleOutline
} from '@vicons/ionicons5'
import type { FollowRecord } from '@/types'

// Props
interface Props {
  modelValue: FollowRecord | null
  isEdit?: boolean
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isEdit: false,
  loading: false
})

// Emits
interface Emits {
  (e: 'update:modelValue', value: FollowRecord): void
  (e: 'submit'): void
}

const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref<FormInst>()

// 表单数据
const formData = reactive<Partial<FollowRecord> & {
  customerId: string | number
  visitCount: number
  followMethod: string
  followContent: string
  customerFeedback: string
  remark: string
}>({
  customerId: 0,
  visitCount: 1,
  follow_time: new Date().toISOString(),
  followMethod: 'phone',
  followContent: '',
  customerFeedback: '',
  next_follow_time: '',
  nextFollowTime: '',
  status: 'pending',
  remark: ''
})

// 跟进时间的计算属性
const followTimeValue = computed({
  get: () => {
    if (!formData.follow_time) return null
    const date = new Date(formData.follow_time)
    return isNaN(date.getTime()) ? null : date.getTime()
  },
  set: (value: number | null) => {
    formData.follow_time = value ? new Date(value).toISOString() : ''
  }
})

// 下次跟进时间的计算属性
const nextFollowTimeValue = computed({
  get: () => {
    if (!formData.next_follow_time) return null
    const date = new Date(formData.next_follow_time)
    return isNaN(date.getTime()) ? null : date.getTime()
  },
  set: (value: number | null) => {
    formData.next_follow_time = value ? new Date(value).toISOString() : ''
  }
})

// 选项数据
const followMethodOptions = [
  { label: '电话沟通', value: 'phone', icon: CallOutline, color: '#1677ff' },
  { label: '微信沟通', value: 'wechat', icon: ChatbubbleOutline, color: '#52c41a' },
  { label: '短信联系', value: 'sms', icon: MailOutline, color: '#faad14' },
  { label: '上门拜访', value: 'visit', icon: HomeOutline, color: '#722ed1' },
  { label: '邀请到店', value: 'store', icon: StorefrontOutline, color: '#eb2f96' },
  { label: '邮件联系', value: 'email', icon: MailOutline, color: '#13c2c2' },
  { label: '其他方式', value: 'other', icon: RemoveCircleOutline, color: '#8c8c8c' }
]

const feedbackOptions = [
  { label: '非常感兴趣', value: 'very_interested', icon: HappyOutline, color: '#52c41a', type: 'success' },
  { label: '比较感兴趣', value: 'interested', icon: HappyOutline, color: '#1677ff', type: 'info' },
  { label: '一般兴趣', value: 'neutral', icon: RemoveCircleOutline, color: '#faad14', type: 'warning' },
  { label: '暂无兴趣', value: 'not_interested', icon: SadOutline, color: '#ff4d4f', type: 'error' },
  { label: '需要考虑', value: 'considering', icon: PauseCircleOutline, color: '#722ed1', type: 'default' },
  { label: '价格敏感', value: 'price_sensitive', icon: RemoveCircleOutline, color: '#fa8c16', type: 'warning' },
  { label: '时间不合适', value: 'timing_issue', icon: PauseCircleOutline, color: '#8c8c8c', type: 'default' },
  { label: '已有其他选择', value: 'has_alternative', icon: CloseCircleOutline, color: '#ff4d4f', type: 'error' },
  { label: '无法联系', value: 'no_contact', icon: CloseCircleOutline, color: '#8c8c8c', type: 'default' },
  { label: '拒绝沟通', value: 'refused', icon: CloseCircleOutline, color: '#ff4d4f', type: 'error' }
]

const statusOptions = [
  { label: '跟进中', value: 'pending', icon: PauseCircleOutline, color: '#1677ff', type: 'option' as const },
  { label: '待跟进', value: 'waiting', icon: TimeOutline, color: '#faad14', type: 'option' as const },
  { label: '已完成', value: 'completed', icon: CheckmarkCircleOutline, color: '#52c41a', type: 'option' as const },
  { label: '已转化', value: 'converted', icon: CheckmarkCircleOutline, color: '#722ed1', type: 'option' as const },
  { label: '已放弃', value: 'abandoned', icon: CloseCircleOutline, color: '#ff4d4f', type: 'option' as const }
]

// 渲染标签函数
const renderMethodLabel: SelectRenderLabel = (option): VNodeChild => {
  const opt = followMethodOptions.find(item => item.value === option.value)
  if (!opt) return option.label as VNodeChild
  
  return h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
    h(NIcon, { size: 16, color: opt.color }, { default: () => h(opt.icon) }),
    h('span', opt.label)
  ])
}

const renderFeedbackLabel: SelectRenderLabel = (option): VNodeChild => {
  const opt = feedbackOptions.find(item => item.value === option.value)
  if (!opt) return option.label as VNodeChild
  
  return h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
    h(NIcon, { size: 16, color: opt.color }, { default: () => h(opt.icon) }),
    h('span', opt.label),
    h(NTag, { type: opt.type as any, size: 'small' }, { default: () => opt.label })
  ])
}

const renderStatusLabel: SelectRenderLabel = (option): VNodeChild => {
  const opt = statusOptions.find(item => item.value === option.value)
  if (!opt) return option.label as VNodeChild
  
  return h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
    h(NIcon, { size: 16, color: opt.color }, { default: () => h(opt.icon) }),
    h('span', opt.label),
    h(NTag, { type: opt.type as any, size: 'small' }, { default: () => opt.label })
  ])
}

// 快速模板填充
const fillQuickTemplate = (type: string) => {
  const templates = {
    interested: {
      customerFeedback: 'very_interested',
      followContent: '客户对我们的产品/服务表现出浓厚兴趣，询问了详细的价格和服务内容。客户比较关注性价比和服务质量，希望能够提供更详细的方案。',
      status: 'pending',
      nextFollowTime: Date.now() + 2 * 24 * 60 * 60 * 1000 // 2天后
    },
    considering: {
      customerFeedback: 'considering',
      followContent: '客户表示需要时间考虑，主要顾虑是预算和时间安排。已向客户详细介绍了我们的优势和服务流程，客户表示会认真考虑。',
      status: 'waiting',
      nextFollowTime: Date.now() + 7 * 24 * 60 * 60 * 1000 // 7天后
    },
    price_sensitive: {
      customerFeedback: 'price_sensitive',
      followContent: '客户对价格比较敏感，希望能够获得更优惠的价格。已向客户介绍了不同的套餐选择，建议客户考虑性价比更高的方案。',
      status: 'pending',
      nextFollowTime: Date.now() + 3 * 24 * 60 * 60 * 1000 // 3天后
    },
    no_contact: {
      customerFeedback: 'no_contact',
      followContent: '多次尝试联系客户，电话无人接听，微信消息未回复。可能客户比较忙碌或者联系方式有变化。',
      status: 'waiting',
      nextFollowTime: Date.now() + 5 * 24 * 60 * 60 * 1000 // 5天后
    }
  }
  
  const template = templates[type as keyof typeof templates]
  if (template) {
    Object.assign(formData, template)
  }
}

// 表单验证规则
const rules: FormRules = {
  followTime: {
    required: true,
    type: 'number',
    message: '请选择跟进时间',
    trigger: ['blur', 'change']
  },
  followMethod: {
    required: true,
    message: '请选择跟进方式',
    trigger: ['blur', 'change']
  },
  followContent: {
    required: true,
    message: '请输入跟进内容',
    trigger: ['blur', 'input'],
    min: 10,
    max: 800
  },
  status: {
    required: true,
    message: '请选择跟进状态',
    trigger: ['blur', 'change']
  }
}

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      Object.assign(formData, {
        ...newValue,
        follow_time: newValue.follow_time ? (typeof newValue.follow_time === 'string' ? new Date(newValue.follow_time).getTime() : newValue.follow_time) : Date.now(),
        next_follow_time: newValue.next_follow_time ? (typeof newValue.next_follow_time === 'string' ? new Date(newValue.next_follow_time).getTime() : newValue.next_follow_time) : null
      })
    }
  },
  { immediate: true, deep: true }
)

// 监听表单数据变化，同步到父组件
watch(
  formData,
  (newValue) => {
    const followRecord: FollowRecord = {
      id: newValue.id || 0,
      customer_id: newValue.customer_id || (typeof newValue.customerId === 'number' ? newValue.customerId : 0),
      customer_name: newValue.customer_name || '',
      type: newValue.type || 'phone',
      follow_time: newValue.follow_time || new Date().toISOString(),
      content: newValue.content || newValue.followContent || '',
      next_follow_time: newValue.next_follow_time || '',
      status: newValue.status || 'pending',
      remark: newValue.remark || '',
      stage: newValue.stage || 'follow',
      created_by: newValue.created_by || 0,
      created_by_name: newValue.created_by_name || '',
      created_at: newValue.created_at || new Date().toISOString(),
      updated_at: newValue.updated_at || new Date().toISOString()
    }
    emit('update:modelValue', followRecord)
  },
  { deep: true }
)

// 表单验证方法
const validate = async (): Promise<boolean> => {
  try {
    await formRef.value?.validate()
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetForm = () => {
  formRef.value?.restoreValidation()
  Object.assign(formData, {
    customerId: 0,
    visitCount: 1,
    follow_time: new Date().toISOString(),
    followMethod: 'phone',
    followContent: '',
    customerFeedback: '',
    next_follow_time: '',
    nextFollowTime: '',
    status: 'pending',
    remark: ''
  })
}

// 暴露方法给父组件
defineExpose({
  validate,
  resetForm
})
</script>

<style scoped>
.follow-record-form {
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
}

.form-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.header-tips {
  margin-top: 8px;
}

.form-section {
  margin-bottom: 32px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8e8e8;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.quick-actions {
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

/* 表单样式优化 */
:deep(.n-form-item-label) {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

:deep(.n-input__textarea) {
  min-height: 100px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

:deep(.n-date-picker) {
  width: 100%;
}

:deep(.n-select) {
  width: 100%;
}

/* 表单项间距调整 */
:deep(.n-form-item) {
  margin-bottom: 20px;
}

:deep(.n-form-item:last-child) {
  margin-bottom: 0;
}

/* 必填项标记样式 */
:deep(.n-form-item--required .n-form-item-label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
  font-weight: bold;
}

/* 输入框聚焦效果 */
:deep(.n-input:focus-within) {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

:deep(.n-select:focus-within) {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

:deep(.n-date-picker:focus-within) {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

/* 选择器选项样式 */
:deep(.n-base-selection-label) {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 滚动条样式 */
.follow-record-form::-webkit-scrollbar {
  width: 6px;
}

.follow-record-form::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.follow-record-form::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.follow-record-form::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .follow-record-form {
    padding: 0;
    max-height: 60vh;
  }
  
  .form-header {
    margin-bottom: 16px;
    padding-bottom: 12px;
  }
  
  .header-title {
    font-size: 14px;
  }
  
  .form-section {
    margin-bottom: 20px;
    padding: 16px;
  }
  
  .section-title {
    font-size: 13px;
  }
  
  :deep(.n-form-item) {
    margin-bottom: 16px;
  }
  
  :deep(.n-form-item-label) {
    font-size: 13px;
  }
  
  :deep(.n-grid) {
    grid-template-columns: 1fr !important;
  }
}

@media (max-width: 480px) {
  .form-section {
    padding: 12px;
    margin-bottom: 16px;
  }
  
  .quick-actions {
    padding: 8px;
  }
  
  :deep(.n-space) {
    flex-wrap: wrap;
  }
  
  :deep(.n-button) {
    font-size: 12px;
    padding: 4px 8px;
  }
}
</style>