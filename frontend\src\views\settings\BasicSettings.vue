<template>
  <div class="basic-settings">
    <n-card>
      <n-tabs v-model:value="activeTab" type="line" animated>
        <n-tab-pane name="basic" tab="基础设置">
          <BasicSettingsForm />
        </n-tab-pane>
        <n-tab-pane name="tracking" tab="跟踪配置">
          <TrackingConfig />
        </n-tab-pane>
        <n-tab-pane name="options" tab="选项管理">
          <div class="options-management-content">
            <n-tabs v-model:value="optionsActiveTab" type="line" animated>
              <n-tab-pane name="categories" tab="选项分类管理">
                <CategoryManager />
              </n-tab-pane>
              <n-tab-pane name="items" tab="选项数据管理">
                <ItemManager />
              </n-tab-pane>
            </n-tabs>
          </div>
        </n-tab-pane>
      </n-tabs>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { NCard, NTabs, NTabPane } from 'naive-ui'
import BasicSettingsForm from './components/BasicSettingsForm.vue'
import TrackingConfig from './TrackingConfig.vue'
import CategoryManager from '@/components/settings/CategoryManager.vue'
import ItemManager from '@/components/settings/ItemManager.vue'

const route = useRoute()
const router = useRouter()

const activeTab = ref('basic')
const optionsActiveTab = ref('categories')

// 初始化标签页
const initializeTab = () => {
  const tabParam = route.query.tab as string
  const optionsTabParam = route.query.optionsTab as string
  
  if (tabParam === 'options') {
    activeTab.value = 'options'
    if (optionsTabParam && ['categories', 'items'].includes(optionsTabParam)) {
      optionsActiveTab.value = optionsTabParam
    }
  } else if (['basic', 'tracking'].includes(tabParam)) {
    activeTab.value = tabParam
  }
}

// 监听路由变化
watch(
  () => route.query,
  () => {
    initializeTab()
  }
)

// 监听标签页变化，更新URL
watch(activeTab, (newTab) => {
  const query = { ...route.query }
  if (newTab === 'basic') {
    delete query.tab
    delete query.optionsTab
  } else {
    query.tab = newTab
  }
  router.replace({ query })
})

watch(optionsActiveTab, (newTab) => {
  if (activeTab.value === 'options') {
    const query = { ...route.query }
    query.optionsTab = newTab
    router.replace({ query })
  }
})

onMounted(() => {
  initializeTab()
})
</script>

<style scoped>
.basic-settings {
  padding: 0;
}

.options-management-content {
  padding: 16px 0;
}
</style>