<template>
  <div class="customer-follow-detail">
    <n-card class="detail-header" :bordered="false">
      <template #header>
        <div class="header-content">
          <n-button text @click="handleBack">
            <template #icon>
              <n-icon><arrow-back-outline /></n-icon>
            </template>
            返回客户列表
          </n-button>
          <div class="customer-info">
            <h2>{{ customerData?.name || '客户跟进详情' }}</h2>
            <p v-if="customerData?.mobile" class="customer-mobile">{{ customerData.mobile }}</p>
          </div>
        </div>
      </template>
    </n-card>

    <div class="detail-content">
      <n-grid :cols="24" :x-gap="16" :y-gap="16">
        <!-- 客户基本信息编辑 -->
        <n-grid-item :span="12">
          <n-card title="客户信息" :bordered="false">
            <n-form
              ref="customerFormRef"
              :model="customerForm"
              :rules="customerRules"
              label-placement="left"
              label-width="80px"
            >
              <n-form-item label="客户姓名" path="name">
                <n-input v-model:value="customerForm.name" placeholder="请输入客户姓名" />
              </n-form-item>
              
              <n-form-item label="联系电话" path="mobile">
                <n-input v-model:value="customerForm.mobile" placeholder="请输入联系电话" />
              </n-form-item>
              
              <n-form-item label="小区名称" path="company">
                <n-input v-model:value="customerForm.company" placeholder="请输入小区名称" />
              </n-form-item>
              
              <n-form-item label="客户来源" path="source">
                <n-select
                  v-model:value="customerForm.source"
                  :options="sourceOptions"
                  placeholder="请选择客户来源"
                />
              </n-form-item>
              
              <n-form-item label="客户状态" path="status">
                <n-select
                  v-model:value="customerForm.status"
                  :options="statusOptions"
                  placeholder="请选择客户状态"
                />
              </n-form-item>
              
              <n-form-item label="备注" path="remark">
                <n-input
                  v-model:value="customerForm.remark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入备注信息"
                />
              </n-form-item>
              
              <n-form-item>
                <n-space>
                  <n-button type="primary" @click="handleSaveCustomer">
                    保存客户信息
                  </n-button>
                  <n-button @click="handleResetCustomer">
                    重置
                  </n-button>
                </n-space>
              </n-form-item>
            </n-form>
          </n-card>
        </n-grid-item>

        <!-- 跟进记录 -->
        <n-grid-item :span="12">
          <n-card title="跟进记录" :bordered="false">
            <template #header-extra>
              <n-button type="primary" size="small" @click="handleAddFollow">
                <template #icon>
                  <n-icon><add-outline /></n-icon>
                </template>
                新增跟进
              </n-button>
            </template>
            
            <div class="follow-records">
              <n-empty v-if="followRecords.length === 0" description="暂无跟进记录">
                <template #extra>
                  <n-button size="small" @click="handleAddFollow">创建首次跟进</n-button>
                </template>
              </n-empty>
              
              <div v-else class="records-list">
                <n-card 
                  v-for="(record, index) in followRecords" 
                  :key="record.id || index"
                  class="record-item"
                  size="small"
                >
                  <div class="record-header">
                    <span class="record-time">{{ formatDate(record.follow_time) }}</span>
                    <div class="record-actions">
                      <n-button text size="small" type="primary" @click="handleEditFollow(record)">
                        <template #icon><n-icon><create-outline /></n-icon></template>
                        编辑
                      </n-button>
                      <n-button text size="small" type="error" @click="handleDeleteFollow(record.id)">
                        <template #icon><n-icon><trash-outline /></n-icon></template>
                        删除
                      </n-button>
                    </div>
                  </div>
                  <div class="record-content">
                    <n-descriptions size="small" :column="1">
                      <n-descriptions-item label="跟进方式">{{ record.type }}</n-descriptions-item>
                      <n-descriptions-item label="跟进内容">{{ record.content }}</n-descriptions-item>
                      <n-descriptions-item label="备注">{{ record.remark || '-' }}</n-descriptions-item>
                      <n-descriptions-item label="下次跟进">{{ record.next_follow_time ? formatDate(record.next_follow_time) : '-' }}</n-descriptions-item>
                    </n-descriptions>
                  </div>
                </n-card>
              </div>
            </div>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 跟进记录表单弹窗 -->
    <n-modal v-model:show="showFollowModal" preset="card" title="跟进记录" style="width: 600px;">
      <follow-record-form
        v-model="editingRecord"
        :customer-id="customerId"
        :initial-data="editingRecord"
        @success="handleFollowSuccess"
        @cancel="showFollowModal = false"
      />
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  NCard, NGrid, NGridItem, NForm, NFormItem, NInput, NSelect, NButton, 
  NSpace, NIcon, NEmpty, NDescriptions, NDescriptionsItem, NModal,
  useMessage
} from 'naive-ui'
import { 
  ArrowBackOutline, AddOutline, CreateOutline, TrashOutline 
} from '@vicons/ionicons5'
import FollowRecordForm from './components/FollowRecordForm.vue'
import type { Customer, FollowRecord } from '@/types'
import { formatDate } from '@/utils'
import { useCustomerOptionsStore } from '@/stores/customerOptions'
import { CustomerOptionCategory } from '@/types/customerOptions'

const route = useRoute()
const router = useRouter()
const message = useMessage()
const customerOptionsStore = useCustomerOptionsStore()

// 客户ID
const customerId = ref<number>(Number(Array.isArray(route.params.customerId) ? route.params.customerId[0] : route.params.customerId))

// 客户数据
const customerData = ref<Customer | null>(null)

// 客户表单
const customerForm = reactive({
  name: '',
  mobile: '',
  company: '',
  source: '',
  status: '',
  remark: ''
})

// 表单验证规则
const customerRules = {
  name: [
    { required: true, message: '请输入客户姓名', trigger: 'blur' }
  ],
  mobile: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 选项数据
const sourceOptions = computed(() => 
  customerOptionsStore.toSelectOptions(CustomerOptionCategory.SOURCE)
)

const statusOptions = computed(() => 
  customerOptionsStore.toSelectOptions(CustomerOptionCategory.STATUS)
)

// 跟进记录
const followRecords = ref<FollowRecord[]>([])
const showFollowModal = ref(false)
const editingRecord = ref<FollowRecord | null>(null)

// 表单引用
const customerFormRef = ref()

// 初始化数据
const initializeData = () => {
  // 从路由查询参数中获取客户数据
  if (route.query.customerData) {
    try {
      customerData.value = JSON.parse(route.query.customerData as string)
      // 填充表单
      if (customerData.value) {
        Object.assign(customerForm, {
          name: customerData.value.name || '',
          mobile: customerData.value.mobile || '',
          company: customerData.value.company || '',
          source: customerData.value.source || '',
          status: customerData.value.status || '',
          remark: customerData.value.remark || ''
        })
      }
    } catch (error) {
      console.error('解析客户数据失败:', error)
      message.error('客户数据解析失败')
    }
  }
  
  // 加载跟进记录
  loadFollowRecords()
}

// 加载跟进记录
const loadFollowRecords = async () => {
  try {
    // 这里应该调用API获取跟进记录
    // const response = await getFollowRecords(customerId.value)
    // followRecords.value = response.data
    
    // 模拟数据
    followRecords.value = [
      {
          id: 1,
          customer_id: Number(customerId.value),
          customer_name: customerData.value?.name || '客户',
          follow_time: new Date(Date.now() - 86400000).toISOString(),
          type: '电话跟进',
          content: '初次联系客户，了解装修需求',
          next_follow_time: new Date(Date.now() + 86400000).toISOString(),
          status: 'completed',
          created_by: 1,
          created_by_name: '当前用户',
          remark: '客户对现代简约风格感兴趣',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          stage: 'follow' as const
        }
    ]
  } catch (error) {
    console.error('加载跟进记录失败:', error)
    message.error('加载跟进记录失败')
  }
}

// 格式化日期时间
const formatDateTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 返回客户列表
const handleBack = () => {
  router.push({ name: 'CustomerList' })
}

// 保存客户信息
const handleSaveCustomer = async () => {
  try {
    await customerFormRef.value?.validate()
    
    // 这里应该调用API保存客户信息
    // await updateCustomer(customerId.value, customerForm)
    
    message.success('客户信息保存成功')
  } catch (error) {
    console.error('保存客户信息失败:', error)
    message.error('保存客户信息失败')
  }
}

// 重置客户表单
const handleResetCustomer = () => {
  if (customerData.value) {
    Object.assign(customerForm, {
      name: customerData.value.name || '',
      mobile: customerData.value.mobile || '',
      company: customerData.value.company || '',
      source: customerData.value.source || '',
      status: customerData.value.status || '',
      remark: customerData.value.remark || ''
    })
  }
}

// 新增跟进
const handleAddFollow = () => {
  editingRecord.value = null
  showFollowModal.value = true
}

// 编辑跟进
const handleEditFollow = (record: FollowRecord) => {
  editingRecord.value = record
  showFollowModal.value = true
}

// 删除跟进
const handleDeleteFollow = async (recordId: number) => {
  try {
    // 这里应该调用API删除跟进记录
    // await deleteFollowRecord(recordId)
    
    followRecords.value = followRecords.value.filter(record => record.id !== recordId)
    message.success('删除成功')
  } catch (error) {
    console.error('删除跟进记录失败:', error)
    message.error('删除失败')
  }
}

// 跟进记录操作成功
const handleFollowSuccess = () => {
  showFollowModal.value = false
  loadFollowRecords()
}

onMounted(async () => {
  await customerOptionsStore.loadAllCustomerOptions()
  initializeData()
})
</script>

<style scoped>
.customer-follow-detail {
  padding: 16px;
  background: #f5f5f5;
  min-height: 100vh;
}

.detail-header {
  margin-bottom: 16px;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.customer-info h2 {
  margin: 0;
  color: #333;
  font-size: 20px;
}

.customer-phone {
  margin: 4px 0 0 0;
  color: #666;
  font-size: 14px;
}

.detail-content {
  max-width: 1200px;
}

.follow-records {
  max-height: 600px;
  overflow-y: auto;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.record-item {
  border-left: 3px solid #1677ff;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.record-time {
  font-weight: 500;
  color: #333;
}

.record-actions {
  display: flex;
  gap: 8px;
}

.record-content {
  padding-left: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .customer-follow-detail {
    padding: 8px;
  }
  
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .detail-content :deep(.n-grid-item) {
    grid-column: span 24;
  }
}
</style>