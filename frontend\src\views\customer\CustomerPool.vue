<template>
  <div class="customer-pool">
    <!-- 页面头部 -->
    <PageHeader title="客户公海" description="管理和分配公海客户，提高客户转化率">
      <template #actions>
        <n-space size="small">
          <n-button type="primary" @click="handleBatchClaim">
            <template #icon>
              <n-icon><hand-right-outline /></n-icon>
            </template>
            批量领取
          </n-button>
          <n-button @click="handleRefresh">
            <template #icon>
              <n-icon><refresh-outline /></n-icon>
            </template>
            刷新
          </n-button>
          <n-button @click="showRulesModal = true">
            <template #icon>
              <n-icon><document-text-outline /></n-icon>
            </template>
            公海规则
          </n-button>
        </n-space>
      </template>
    </PageHeader>

    <!-- 统计卡片 -->
    <n-grid :cols="4" :x-gap="16" class="stats-grid">
      <n-card class="stat-card" :bordered="false">
        <n-statistic label="公海总数" :value="poolStats.total">
          <template #prefix>
            <n-icon size="20" color="#1677ff">
              <water-outline />
            </n-icon>
          </template>
        </n-statistic>
        <div class="stat-trend">
          <n-tag size="small" type="info">
            较昨日 +{{ poolStats.todayIncrease }}
          </n-tag>
        </div>
      </n-card>
      
      <n-card class="stat-card" :bordered="false">
        <n-statistic label="今日新增" :value="poolStats.todayNew">
          <template #prefix>
            <n-icon size="20" color="#52c41a">
              <add-circle-outline />
            </n-icon>
          </template>
        </n-statistic>
        <div class="stat-trend">
          <n-tag size="small" type="success">
            入池率 {{ poolStats.poolRate }}%
          </n-tag>
        </div>
      </n-card>
      
      <n-card class="stat-card" :bordered="false">
        <n-statistic label="本周领取" :value="poolStats.weekClaimed">
          <template #prefix>
            <n-icon size="20" color="#faad14">
              <trending-up-outline />
            </n-icon>
          </template>
        </n-statistic>
        <div class="stat-trend">
          <n-tag size="small" type="warning">
            领取率 {{ poolStats.claimRate }}%
          </n-tag>
        </div>
      </n-card>
      
      <n-card class="stat-card" :bordered="false">
        <n-statistic label="可领取数" :value="poolStats.available">
          <template #prefix>
            <n-icon size="20" color="#722ed1">
              <checkmark-circle-outline />
            </n-icon>
          </template>
        </n-statistic>
        <div class="stat-trend">
          <n-tag size="small" type="primary">
            我的配额 {{ poolStats.myQuota }}
          </n-tag>
        </div>
      </n-card>
    </n-grid>

    <!-- 筛选器 -->
    <div class="filters">
      <n-space>
        <n-input
          v-model:value="searchForm.name"
          placeholder="搜索客户姓名、手机号"
          clearable
          style="width: 300px"
        >
          <template #prefix>
            <SearchIcon />
          </template>
        </n-input>
        
        <n-select
          v-model:value="searchForm.source"
          placeholder="客户来源"
          clearable
          style="width: 150px"
          :options="sourceOptions"
        />
        
        <n-select
          v-model:value="searchForm.poolReason"
          placeholder="入池原因"
          clearable
          style="width: 150px"
          :options="poolReasonOptions"
        />
        
        <n-select
          v-model:value="searchForm.category"
          placeholder="客户分类"
          clearable
          style="width: 150px"
          :options="categoryOptions"
        />
        
        <n-date-picker
          v-model:value="searchForm.poolDateRange"
          type="daterange"
          placeholder="入池时间"
          clearable
        />
        
        <n-button type="default" @click="handleReset">重置</n-button>
        <n-button type="primary" @click="handleSearch">搜索</n-button>
      </n-space>
    </div>

    <!-- 客户表格 -->
    <n-card class="table-card" :bordered="false">
      <template #header>
        <div class="table-header">
          <div class="table-title">
            <n-icon size="18" color="#1677ff">
              <list-outline />
            </n-icon>
            <span>公海客户列表</span>
            <n-tag v-if="checkedRowKeys.length > 0" type="info" size="small">
              已选择 {{ checkedRowKeys.length }} 项
            </n-tag>
          </div>
          <div class="table-actions">
            <n-space size="small">
              <n-button
                v-if="checkedRowKeys.length > 0"
                type="primary"
                size="small"
                @click="handleBatchClaim"
              >
                批量领取
              </n-button>
              <n-button size="small" @click="handleRefresh">
                <template #icon>
                  <n-icon><refresh-outline /></n-icon>
                </template>
                刷新
              </n-button>
              <n-dropdown :options="exportOptions" @select="handleExport">
                <n-button size="small">
                  <template #icon>
                    <n-icon><cloud-download-outline /></n-icon>
                  </template>
                  导出
                </n-button>
              </n-dropdown>
            </n-space>
          </div>
        </div>
      </template>
      
      <n-data-table
        ref="tableRef"
        :columns="columns"
        :data="poolCustomers"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row: PoolCustomer) => row.id"
        :checked-row-keys="checkedRowKeys"
        @update:checked-row-keys="handleCheck"
        remote
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
        :scroll-x="1400"
        striped
      />
    </n-card>

    <!-- 公海规则弹窗 -->
    <n-modal v-model:show="showRulesModal" preset="card" title="公海规则" style="width: 600px">
      <div class="rules-content">
        <n-alert type="info" :show-icon="false" style="margin-bottom: 16px">
          <template #header>
            <div class="alert-header">
              <n-icon size="16" color="#1677ff">
                <information-circle-outline />
              </n-icon>
              <span>公海管理规则</span>
            </div>
          </template>
        </n-alert>
        
        <div class="rules-list">
          <div class="rule-item">
            <div class="rule-title">1. 入池条件</div>
            <div class="rule-content">
              • 客户超过7天未跟进自动进入公海<br>
              • 销售主动释放的客户<br>
              • 无效客户经审核后进入公海
            </div>
          </div>
          
          <div class="rule-item">
            <div class="rule-title">2. 领取规则</div>
            <div class="rule-content">
              • 每人每日最多领取5个客户<br>
              • 优先级：A类客户 > B类客户 > C类客户<br>
              • 同一客户24小时内不可重复领取
            </div>
          </div>
          
          <div class="rule-item">
            <div class="rule-title">3. 保护期</div>
            <div class="rule-content">
              • 新领取客户有3天保护期<br>
              • 保护期内必须至少跟进1次<br>
              • 保护期结束未跟进将重新进入公海
            </div>
          </div>
          
          <div class="rule-item">
            <div class="rule-title">4. 数据统计</div>
            <div class="rule-content">
              • 领取数量计入个人业绩考核<br>
              • 转化率影响后续领取权限<br>
              • 恶意占用客户将被限制领取
            </div>
          </div>
        </div>
      </div>
    </n-modal>
  </div>
</template>

# 修复 h 函数导入

<script setup lang="ts">
import { ref, reactive, computed, onMounted, h } from 'vue'
import { useMessage, useDialog } from 'naive-ui'
import PageHeader from '@/components/common/PageHeader.vue'
import { useCustomerOptionsStore } from '@/stores/customerOptions'
import { CustomerOptionCategory } from '@/constants/customerOptions'
import {
  HandRightOutline,
  RefreshOutline,
  DocumentTextOutline,
  WaterOutline,
  AddCircleOutline,
  TrendingUpOutline,
  CheckmarkCircleOutline,
  SearchOutline as SearchIcon,
  ChevronDownOutline,
  ChevronUpOutline,
  ListOutline,
  CloudDownloadOutline,
  InformationCircleOutline
} from '@vicons/ionicons5'

// 类型定义
interface PoolCustomer {
  id: string
  name: string
  phone: string
  source: string
  category: string
  poolDate: string
  poolReason: string
  poolDays: number
  originalOwner: string
  lastContactDate: string
}

interface PoolStats {
  total: number
  todayNew: number
  weekClaimed: number
  available: number
  todayIncrease: number
  poolRate: number
  claimRate: number
  myQuota: number
}

const message = useMessage()
const dialog = useDialog()
const customerOptionsStore = useCustomerOptionsStore()

// 响应式数据
const loading = ref(false)
const showAdvanced = ref(false)
const showRulesModal = ref(false)
const checkedRowKeys = ref<string[]>([])

const poolStats = reactive<PoolStats>({
  total: 156,
  todayNew: 12,
  weekClaimed: 45,
  available: 89,
  todayIncrease: 8,
  poolRate: 15.2,
  claimRate: 28.8,
  myQuota: 5
})

const searchForm = reactive({
  name: '',
  phone: '',
  source: null,
  poolDateRange: null as [number, number] | null,
  poolReason: null,
  category: null,
  poolDaysMin: null,
  poolDaysMax: null,
  originalOwner: ''
})

const poolCustomers = ref<PoolCustomer[]>([
  {
    id: '1',
    name: '张三',
    phone: '13800138001',
    source: '线上推广',
    category: 'A类客户',
    poolDate: '2024-01-15',
    poolReason: '超时未跟进',
    poolDays: 5,
    originalOwner: '李销售',
    lastContactDate: '2024-01-10'
  },
  {
    id: '2',
    name: '李四',
    phone: '13800138002',
    source: '朋友介绍',
    category: 'B类客户',
    poolDate: '2024-01-14',
    poolReason: '主动释放',
    poolDays: 6,
    originalOwner: '王销售',
    lastContactDate: '2024-01-08'
  }
])

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true,
  prefix: ({ itemCount }: { itemCount: number }) => `共 ${itemCount} 条`
})

// 选项配置
const sourceOptions = computed(() => 
  customerOptionsStore.toSelectOptions(CustomerOptionCategory.SOURCE)
)

const poolReasonOptions = [
  { label: '超时未跟进', value: 'timeout' },
  { label: '主动释放', value: 'release' },
  { label: '无效客户', value: 'invalid' },
  { label: '重复客户', value: 'duplicate' }
]

const categoryOptions = [
  { label: 'A类客户', value: 'A' },
  { label: 'B类客户', value: 'B' },
  { label: 'C类客户', value: 'C' }
]

const exportOptions = [
  { label: '导出当前页', key: 'current' },
  { label: '导出全部', key: 'all' },
  { label: '导出选中项', key: 'selected' }
]

// 表格列配置
const columns = [
  {
    type: 'selection',
    disabled: (row: PoolCustomer) => row.category === 'C'
  },
  {
    title: '客户姓名',
    key: 'name',
    width: 120,
    render: (row: PoolCustomer) => {
      return h('div', { class: 'customer-name-cell' }, [
        h('span', { class: 'name' }, row.name),
        h('n-tag', { 
          size: 'small', 
          type: row.category === 'A' ? 'error' : row.category === 'B' ? 'warning' : 'default',
          style: 'margin-left: 8px'
        }, row.category)
      ])
    }
  },
  {
    title: '手机号码',
    key: 'phone',
    width: 130
  },
  {
    title: '客户来源',
    key: 'source',
    width: 120
  },
  {
    title: '入池信息',
    key: 'poolInfo',
    width: 200,
    render: (row: PoolCustomer) => {
      return h('div', { class: 'pool-info-cell' }, [
        h('div', { class: 'pool-date' }, `入池时间：${row.poolDate}`),
        h('div', { class: 'pool-reason' }, `入池原因：${row.poolReason}`),
        h('div', { class: 'pool-days' }, [
          h('span', '在池：'),
          h('n-tag', { 
            size: 'small',
            type: row.poolDays > 7 ? 'error' : row.poolDays > 3 ? 'warning' : 'success'
          }, `${row.poolDays}天`)
        ])
      ])
    }
  },
  {
    title: '原负责人',
    key: 'originalOwner',
    width: 100
  },
  {
    title: '最后联系',
    key: 'lastContactDate',
    width: 120,
    render: (row: PoolCustomer) => {
      const days = Math.floor((Date.now() - new Date(row.lastContactDate).getTime()) / (1000 * 60 * 60 * 24))
      return h('div', { class: 'last-contact-cell' }, [
        h('div', row.lastContactDate),
        h('div', { class: 'contact-days' }, `${days}天前`)
      ])
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right',
    render: (row: PoolCustomer) => {
      return h('n-space', { size: 'small' }, [
        h('n-button', {
          size: 'small',
          type: 'primary',
          onClick: () => handleClaim(row)
        }, '领取'),
        h('n-button', {
          size: 'small',
          type: 'info',
          onClick: () => handleViewDetail(row)
        }, '查看'),
        h('n-dropdown', {
          options: [
            { label: '查看历史', key: 'history' },
            { label: '标记无效', key: 'invalid' }
          ],
          onSelect: (key: string) => handleMoreAction(key, row)
        }, {
          default: () => h('n-button', { size: 'small' }, '更多')
        })
      ])
    }
  }
]

// 方法
const toggleAdvancedSearch = () => {
  showAdvanced.value = !showAdvanced.value
}

const handleSearch = () => {
  pagination.page = 1
  fetchPoolCustomers()
}

const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    phone: '',
    source: null,
    poolDateRange: null,
    poolReason: null,
    category: null,
    poolDaysMin: null,
    poolDaysMax: null,
    originalOwner: ''
  })
  handleSearch()
}

const handleRefresh = () => {
  fetchPoolCustomers()
  fetchPoolStats()
}

const handlePoolRules = () => {
  showRulesModal.value = true
}

const handleCheck = (keys: string[]) => {
  checkedRowKeys.value = keys
}

const handlePageChange = (page: number) => {
  pagination.page = page
  fetchPoolCustomers()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  fetchPoolCustomers()
}

const handleBatchClaim = () => {
  if (checkedRowKeys.value.length === 0) {
    message.warning('请选择要领取的客户')
    return
  }
  
  dialog.info({
    title: '确认领取',
    content: `确定要领取选中的 ${checkedRowKeys.value.length} 个客户吗？`,
    positiveText: '确定领取',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // TODO: 调用API批量领取客户
        message.success(`成功领取 ${checkedRowKeys.value.length} 个客户`)
        checkedRowKeys.value = []
        await fetchPoolCustomers()
        await fetchPoolStats()
      } catch (error) {
        message.error('领取失败')
      }
    }
  })
}

const handleClaim = (customer: PoolCustomer) => {
  dialog.info({
    title: '确认领取',
    content: `确定要领取客户"${customer.name}"吗？`,
    positiveText: '确定领取',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // TODO: 调用API领取客户
        message.success(`成功领取客户"${customer.name}"`)
        await fetchPoolCustomers()
        await fetchPoolStats()
      } catch (error) {
        message.error('领取失败')
      }
    }
  })
}

const handleViewDetail = (customer: PoolCustomer) => {
  // TODO: 跳转到客户详情页面
  message.info('查看客户详情功能开发中')
}

const handleMoreAction = (key: string, customer: PoolCustomer) => {
  switch (key) {
    case 'history':
      message.info('查看历史功能开发中')
      break
    case 'invalid':
      dialog.warning({
        title: '确认标记',
        content: `确定要将客户"${customer.name}"标记为无效吗？`,
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => {
          message.success('标记成功')
          fetchPoolCustomers()
        }
      })
      break
  }
}

const handleExport = (key: string) => {
  switch (key) {
    case 'current':
      message.info('导出当前页功能开发中')
      break
    case 'all':
      message.info('导出全部功能开发中')
      break
    case 'selected':
      if (checkedRowKeys.value.length === 0) {
        message.warning('请选择要导出的数据')
        return
      }
      message.info('导出选中项功能开发中')
      break
  }
}

// 数据获取
const fetchPoolCustomers = async () => {
  try {
    loading.value = true
    // TODO: 调用API获取公海客户数据
    pagination.itemCount = poolCustomers.value.length
  } catch (error) {
    message.error('获取公海客户失败')
    console.error('获取公海客户失败:', error)
  } finally {
    loading.value = false
  }
}

const fetchPoolStats = async () => {
  try {
    // TODO: 调用API获取公海统计数据
    console.log('获取公海统计数据')
  } catch (error) {
    console.error('获取公海统计失败:', error)
  }
}

// 生命周期
onMounted(async () => {
  await customerOptionsStore.loadAllCustomerOptions()
  await fetchPoolCustomers()
  await fetchPoolStats()
})
</script>

<style scoped>

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  padding: 20px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.stat-card h3 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: bold;
}

.stat-card p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

.stat-trend {
  margin-top: 8px;
  display: flex;
  justify-content: center;
}

.filters {
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.table-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.table-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
  gap: 6px;
}

.table-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

/* 表格单元格样式 */
.customer-name-cell {
  display: flex;
  align-items: center;
}

.customer-name-cell .name {
  font-weight: 500;
  color: #1a1a1a;
}

.pool-info-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
}

.pool-date,
.pool-reason {
  color: #666;
}

.pool-days {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666;
}

.last-contact-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.contact-days {
  font-size: 11px;
  color: #999;
}

/* 公海规则弹窗样式 */
.rules-content {
  max-height: 500px;
  overflow-y: auto;
}

.alert-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.rules-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.rule-item {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1677ff;
}

.rule-title {
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
  font-size: 14px;
}

.rule-content {
  color: #666;
  font-size: 13px;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .customer-pool {
    padding: 16px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .filters {
    padding: 12px;
  }
  
  .filters :deep(.n-space) {
    flex-wrap: wrap;
  }
}
</style>