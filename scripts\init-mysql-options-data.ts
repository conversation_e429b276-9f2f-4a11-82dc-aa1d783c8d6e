import { MySQLManager, MySQLConfig } from '../src/database/MySQLManager.js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { v4 as uuidv4 } from 'uuid';

// 获取当前文件目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 加载环境变量
dotenv.config({ path: join(__dirname, '..', '.env') });

// 数据库配置
const dbConfig: MySQLConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'workchat_admin'
};

// 创建MySQL管理器实例
const mysqlManager = new MySQLManager(dbConfig);

// 选项分类数据
const optionCategories = [
  {
    id: uuidv4(),
    code: 'customer_source',
    name: '客户来源',
    description: '客户的来源渠道',
    is_active: true,
    sort_order: 1
  },
  {
    id: uuidv4(),
    code: 'customer_level',
    name: '客户级别',
    description: '客户的重要程度级别',
    is_active: true,
    sort_order: 2
  },
  {
    id: uuidv4(),
    code: 'decoration_type',
    name: '装修类型',
    description: '装修的类型分类',
    is_active: true,
    sort_order: 3
  },
  {
    id: uuidv4(),
    code: 'house_status',
    name: '房屋状态',
    description: '房屋的当前状态',
    is_active: true,
    sort_order: 4
  },
  {
    id: uuidv4(),
    code: 'budget_range',
    name: '预算范围',
    description: '装修预算的范围',
    is_active: true,
    sort_order: 5
  },
  {
    id: uuidv4(),
    code: 'designer',
    name: '设计师',
    description: '负责项目的设计师',
    is_active: true,
    sort_order: 6
  },
  {
    id: uuidv4(),
    code: 'sales',
    name: '销售人员',
    description: '负责项目的销售人员',
    is_active: true,
    sort_order: 7
  },
  {
    id: uuidv4(),
    code: 'customer_status',
    name: '客户状态',
    description: '客户当前的跟进状态',
    is_active: true,
    sort_order: 8
  },
  {
    id: uuidv4(),
    code: 'payment_method',
    name: '付款方式',
    description: '客户的付款方式',
    is_active: true,
    sort_order: 9
  },
  {
    id: uuidv4(),
    code: 'package_type',
    name: '套餐类型',
    description: '装修套餐的类型',
    is_active: true,
    sort_order: 10
  },
  {
    id: uuidv4(),
    code: 'customer_tag',
    name: '客户标签',
    description: '客户的分类标签',
    is_active: true,
    sort_order: 11
  }
];

// 选项项数据接口
interface OptionItem {
  category_code: string;
  code: string;
  label: string;
  value: string;
  sort_order: number;
  color?: string;
  description?: string;
  icon?: string;
}

// 选项项数据
const optionItems: OptionItem[] = [
  // 客户来源
  {
    category_code: 'customer_source',
    code: 'website',
    label: '官网',
    value: 'website',
    sort_order: 1
  },
  {
    category_code: 'customer_source',
    code: 'referral',
    label: '朋友推荐',
    value: 'referral',
    sort_order: 2
  },
  {
    category_code: 'customer_source',
    code: 'social_media',
    label: '社交媒体',
    value: 'social_media',
    sort_order: 3
  },
  {
    category_code: 'customer_source',
    code: 'advertisement',
    label: '广告',
    value: 'advertisement',
    sort_order: 4
  },
  {
    category_code: 'customer_source',
    code: 'exhibition',
    label: '展会',
    value: 'exhibition',
    sort_order: 5
  },
  
  // 客户级别
  {
    category_code: 'customer_level',
    code: 'vip',
    label: 'VIP客户',
    value: 'vip',
    color: '#ff4d4f',
    sort_order: 1
  },
  {
    category_code: 'customer_level',
    code: 'high',
    label: '高价值客户',
    value: 'high',
    color: '#fa8c16',
    sort_order: 2
  },
  {
    category_code: 'customer_level',
    code: 'medium',
    label: '普通客户',
    value: 'medium',
    color: '#1890ff',
    sort_order: 3
  },
  {
    category_code: 'customer_level',
    code: 'low',
    label: '潜在客户',
    value: 'low',
    color: '#52c41a',
    sort_order: 4
  },
  
  // 装修类型
  {
    category_code: 'decoration_type',
    code: 'full_decoration',
    label: '全包装修',
    value: 'full_decoration',
    sort_order: 1
  },
  {
    category_code: 'decoration_type',
    code: 'half_decoration',
    label: '半包装修',
    value: 'half_decoration',
    sort_order: 2
  },
  {
    category_code: 'decoration_type',
    code: 'clear_decoration',
    label: '清包装修',
    value: 'clear_decoration',
    sort_order: 3
  },
  {
    category_code: 'decoration_type',
    code: 'soft_decoration',
    label: '软装设计',
    value: 'soft_decoration',
    sort_order: 4
  },
  
  // 房屋状态
  {
    category_code: 'house_status',
    code: 'new_house',
    label: '新房',
    value: 'new_house',
    sort_order: 1
  },
  {
    category_code: 'house_status',
    code: 'second_hand',
    label: '二手房',
    value: 'second_hand',
    sort_order: 2
  },
  {
    category_code: 'house_status',
    code: 'old_house',
    label: '老房翻新',
    value: 'old_house',
    sort_order: 3
  },
  {
    category_code: 'house_status',
    code: 'villa',
    label: '别墅',
    value: 'villa',
    sort_order: 4
  },
  
  // 预算范围
  {
    category_code: 'budget_range',
    code: 'budget_5w',
    label: '5万以下',
    value: '5w',
    sort_order: 1
  },
  {
    category_code: 'budget_range',
    code: 'budget_5_10w',
    label: '5-10万',
    value: '5-10w',
    sort_order: 2
  },
  {
    category_code: 'budget_range',
    code: 'budget_10_20w',
    label: '10-20万',
    value: '10-20w',
    sort_order: 3
  },
  {
    category_code: 'budget_range',
    code: 'budget_20_50w',
    label: '20-50万',
    value: '20-50w',
    sort_order: 4
  },
  {
    category_code: 'budget_range',
    code: 'budget_50w',
    label: '50万以上',
    value: '50w+',
    sort_order: 5
  },
  
  // 设计师选项
  {
    category_code: 'designer',
    code: 'zhang_san',
    label: '张三',
    value: 'zhang_san',
    sort_order: 1
  },
  {
    category_code: 'designer',
    code: 'li_si',
    label: '李四',
    value: 'li_si',
    sort_order: 2
  },
  {
    category_code: 'designer',
    code: 'wang_wu',
    label: '王五',
    value: 'wang_wu',
    sort_order: 3
  },
  
  // 销售人员选项
  {
    category_code: 'sales',
    code: 'sales_a',
    label: '销售A',
    value: 'sales_a',
    sort_order: 1
  },
  {
    category_code: 'sales',
    code: 'sales_b',
    label: '销售B',
    value: 'sales_b',
    sort_order: 2
  },
  {
    category_code: 'sales',
    code: 'sales_c',
    label: '销售C',
    value: 'sales_c',
    sort_order: 3
  },
  
  // 客户状态选项
  {
    category_code: 'customer_status',
    code: 'new',
    label: '新客户',
    value: 'new',
    color: '#52c41a',
    sort_order: 1
  },
  {
    category_code: 'customer_status',
    code: 'contacted',
    label: '已联系',
    value: 'contacted',
    color: '#1890ff',
    sort_order: 2
  },
  {
    category_code: 'customer_status',
    code: 'quoted',
    label: '已报价',
    value: 'quoted',
    color: '#fa8c16',
    sort_order: 3
  },
  {
    category_code: 'customer_status',
    code: 'signed',
    label: '已签约',
    value: 'signed',
    color: '#ff4d4f',
    sort_order: 4
  },
  {
    category_code: 'customer_status',
    code: 'lost',
    label: '已流失',
    value: 'lost',
    color: '#d9d9d9',
    sort_order: 5
  },
  
  // 付款方式选项
  {
    category_code: 'payment_method',
    code: 'cash',
    label: '现金',
    value: 'cash',
    sort_order: 1
  },
  {
    category_code: 'payment_method',
    code: 'bank_transfer',
    label: '银行转账',
    value: 'bank_transfer',
    sort_order: 2
  },
  {
    category_code: 'payment_method',
    code: 'credit_card',
    label: '信用卡',
    value: 'credit_card',
    sort_order: 3
  },
  {
    category_code: 'payment_method',
    code: 'installment',
    label: '分期付款',
    value: 'installment',
    sort_order: 4
  },
  
  // 套餐类型选项
  {
    category_code: 'package_type',
    code: 'basic',
    label: '基础套餐',
    value: 'basic',
    sort_order: 1
  },
  {
    category_code: 'package_type',
    code: 'standard',
    label: '标准套餐',
    value: 'standard',
    sort_order: 2
  },
  {
    category_code: 'package_type',
    code: 'premium',
    label: '高级套餐',
    value: 'premium',
    sort_order: 3
  },
  {
    category_code: 'package_type',
    code: 'luxury',
    label: '豪华套餐',
    value: 'luxury',
    sort_order: 4
  },
  
  // 客户标签选项
  {
    category_code: 'customer_tag',
    code: 'urgent',
    label: '紧急',
    value: 'urgent',
    color: '#ff4d4f',
    sort_order: 1
  },
  {
    category_code: 'customer_tag',
    code: 'important',
    label: '重要',
    value: 'important',
    color: '#fa8c16',
    sort_order: 2
  },
  {
    category_code: 'customer_tag',
    code: 'follow_up',
    label: '需跟进',
    value: 'follow_up',
    color: '#1890ff',
    sort_order: 3
  },
  {
    category_code: 'customer_tag',
    code: 'potential',
    label: '潜在客户',
    value: 'potential',
    color: '#52c41a',
    sort_order: 4
  }

];

// 初始化选项数据
async function initOptionsData() {
  try {
    console.log('开始初始化选项数据...');
    
    // 初始化数据库连接池
    console.log('正在初始化数据库连接池...');
    await mysqlManager.initialize();
    console.log('数据库连接池初始化成功');
    
    // 测试数据库连接
    console.log('正在测试数据库连接...');
    await mysqlManager.testConnection();
    console.log('数据库连接成功');

    // 检查表是否存在
    console.log('检查数据库表是否存在...');
    const categoriesTableExists = await mysqlManager.tableExists('option_categories');
    const itemsTableExists = await mysqlManager.tableExists('option_items');
    
    if (!categoriesTableExists) {
      throw new Error('option_categories 表不存在，请先运行数据库迁移脚本');
    }
    if (!itemsTableExists) {
      throw new Error('option_items 表不存在，请先运行数据库迁移脚本');
    }
    console.log('数据库表检查通过');

    // 清空现有数据
    console.log('清空现有选项数据...');
    await mysqlManager.query('DELETE FROM option_items');
    await mysqlManager.query('DELETE FROM option_categories');
    console.log('现有数据已清空');
    
    // 2. 插入选项分类
    console.log('插入选项分类...');
    for (const category of optionCategories) {
      const sql = `
        INSERT INTO option_categories (id, code, name, description, is_active, sort_order, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        description = VALUES(description),
        is_active = VALUES(is_active),
        sort_order = VALUES(sort_order),
        updated_at = NOW()
      `;
      
      const result = await mysqlManager.query(sql, [
        category.id,
        category.code,
        category.name,
        category.description,
        category.is_active,
        category.sort_order
      ]);
      
      if (result.success) {
        console.log(`✓ 插入分类: ${category.name}`);
      } else {
        console.error(`✗ 插入分类失败: ${category.name}`, result.error);
      }
    }
    
    // 3. 获取分类ID映射
    const categoriesResult = await mysqlManager.query('SELECT id, code FROM option_categories');
    if (!categoriesResult.success) {
      throw new Error('获取分类ID失败');
    }
    
    const categoryMap = new Map();
    (categoriesResult.data as any[]).forEach((cat: any) => {
      categoryMap.set(cat.code, cat.id);
    });
    
    // 4. 插入选项项
    console.log('插入选项项...');
    for (const item of optionItems) {
      const categoryId = categoryMap.get(item.category_code);
      if (!categoryId) {
        console.warn(`跳过选项项 ${item.label}: 找不到分类 ${item.category_code}`);
        continue;
      }
      
      const sql = `
        INSERT INTO option_items (id, category_id, code, label, value, color, icon, description, is_active, sort_order, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ON DUPLICATE KEY UPDATE
        label = VALUES(label),
        value = VALUES(value),
        color = VALUES(color),
        icon = VALUES(icon),
        description = VALUES(description),
        is_active = VALUES(is_active),
        sort_order = VALUES(sort_order),
        updated_at = NOW()
      `;
      
      const result = await mysqlManager.query(sql, [
        uuidv4(),
        categoryId,
        item.code,
        item.label,
        item.value,
        item.color || null,
        item.icon || null,
        item.description || null,
        true,
        item.sort_order
      ]);
      
      if (result.success) {
        console.log(`✓ 插入选项: ${item.label}`);
      } else {
        console.error(`✗ 插入选项失败: ${item.label}`, result.error);
      }
    }
    
    // 5. 验证数据
    console.log('\n验证插入的数据:');
    for (const category of optionCategories) {
      const categoryId = categoryMap.get(category.code);
      const itemsResult = await mysqlManager.query(
        'SELECT * FROM option_items WHERE category_id = ? AND is_active = true ORDER BY sort_order',
        [categoryId]
      );
      
      if (itemsResult.success && itemsResult.data) {
        console.log(`${category.name}: ${itemsResult.data.length} 个选项`);
        (itemsResult.data as any[]).forEach((item: any) => {
          console.log(`  - ${item.label} (${item.value})`);
        });
      }
    }
    
    console.log('\nMySQL选项数据初始化完成！');
    
  } catch (error) {
    console.error('初始化选项数据时发生错误:', error);
    throw error;
  } finally {
    // 关闭数据库连接
    await mysqlManager.close();
  }
}

// 执行初始化
initOptionsData()
  .then(() => {
    console.log('脚本执行完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('初始化选项数据失败:', error);
    process.exit(1);
  });

export { initOptionsData };