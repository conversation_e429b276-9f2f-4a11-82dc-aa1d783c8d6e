<template>
  <div class="category-manager">
    <!-- 操作栏 -->
    <div class="action-bar">
      <n-space justify="space-between">
        <n-space>
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索分类名称或代码"
            clearable
            style="width: 300px"
            @input="handleSearch"
          >
            <template #prefix>
              <n-icon :component="SearchOutline" />
            </template>
          </n-input>
          <n-select
            v-model:value="statusFilter"
            placeholder="状态筛选"
            clearable
            style="width: 120px"
            :options="statusOptions"
            @update:value="handleSearch"
          />
        </n-space>
        <n-space>
          <n-button type="primary" @click="handleCreate">
            <template #icon>
              <n-icon :component="AddOutline" />
            </template>
            新增分类
          </n-button>
          <n-button @click="handleRefresh">
            <template #icon>
              <n-icon :component="RefreshOutline" />
            </template>
            刷新
          </n-button>
        </n-space>
      </n-space>
    </div>

    <!-- 数据表格 -->
    <n-data-table
      :columns="columns"
      :data="tableData"
      :loading="loading"
      :pagination="paginationReactive"
      :row-key="(row: OptionCategory) => row.id"
      @update:page="handlePageChange"
      @update:page-size="handlePageSizeChange"
    />

    <!-- 创建/编辑弹窗 -->
    <n-modal v-model:show="showModal" preset="dialog" title="分类信息">
      <template #header>
        <span>{{ isEdit ? '编辑分类' : '新增分类' }}</span>
      </template>
      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="分类代码" path="code">
          <n-input
            v-model:value="formData.code"
            placeholder="请输入分类代码（英文）"
            :disabled="isEdit"
          />
        </n-form-item>
        <n-form-item label="分类名称" path="name">
          <n-input v-model:value="formData.name" placeholder="请输入分类名称" />
        </n-form-item>
        <n-form-item label="描述" path="description">
          <n-input
            v-model:value="formData.description"
            type="textarea"
            placeholder="请输入分类描述"
            :autosize="{ minRows: 2, maxRows: 4 }"
          />
        </n-form-item>
        <n-form-item label="排序" path="sort_order">
          <n-input-number
            v-model:value="formData.sort_order"
            placeholder="排序值"
            :min="0"
            style="width: 100%"
          />
        </n-form-item>
        <n-form-item label="状态" path="is_active">
          <n-switch v-model:value="formData.is_active">
            <template #checked>启用</template>
            <template #unchecked>禁用</template>
          </n-switch>
        </n-form-item>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showModal = false">取消</n-button>
          <n-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h } from 'vue'
import {
  NSpace,
  NInput,
  NSelect,
  NButton,
  NDataTable,
  NModal,
  NForm,
  NFormItem,
  NInputNumber,
  NSwitch,
  NIcon,
  NTag,
  NPopconfirm,
  useMessage,
  type DataTableColumns,
  type FormInst,
  type FormRules
} from 'naive-ui'
import {
  SearchOutline,
  AddOutline,
  RefreshOutline,
  CreateOutline,
  TrashOutline
} from '@vicons/ionicons5'
import { useOptionsStore } from '@/stores/optionsStore'
import type {
  OptionCategory,
  CreateOptionCategoryRequest,
  UpdateOptionCategoryRequest
} from '@/types/options'

// 消息提示
const message = useMessage()

// Options Store
const optionsStore = useOptionsStore()

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const statusFilter = ref<string | null>(null)
const tableData = ref<OptionCategory[]>([])
const showModal = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const formRef = ref<FormInst | null>(null)

// 分页配置
const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true,
  itemCount: 0
})

// 状态筛选选项
const statusOptions = [
  { label: '启用', value: 'true', type: 'option' as const },
  { label: '禁用', value: 'false', type: 'option' as const }
]

// 表单数据
const formData = ref<CreateOptionCategoryRequest>({
  code: '',
  name: '',
  description: '',
  sort_order: 0,
  is_active: true
})

// 表单验证规则
const formRules: FormRules = {
  code: [
    { required: true, message: '请输入分类代码', trigger: 'blur' },
    { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '代码只能包含字母、数字和下划线，且以字母或下划线开头', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' }
  ],
  sort_order: [
    { required: true, message: '请输入排序值', trigger: 'blur', type: 'number' }
  ]
}

// 表格列配置
const columns: DataTableColumns<OptionCategory> = [
  {
    type: 'selection'
  },
  {
    title: '分类代码',
    key: 'code',
    width: 120
  },
  {
    title: '分类名称',
    key: 'name',
    width: 150
  },
  {
    title: '描述',
    key: 'description',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '排序',
    key: 'sort_order',
    width: 80,
    sorter: true
  },
  {
    title: '状态',
    key: 'is_active',
    width: 80,
    render(row) {
      return h(
        NTag,
        {
          type: row.is_active ? 'success' : 'error'
        },
        {
          default: () => (row.is_active ? '启用' : '禁用')
        }
      )
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 180,
    render(row) {
      return new Date(row.created_at).toLocaleString()
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render(row) {
      return h(NSpace, null, {
        default: () => [
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              ghost: true,
              onClick: () => handleEdit(row)
            },
            {
              default: () => '编辑',
              icon: () => h(NIcon, { component: CreateOutline })
            }
          ),
          h(
            NPopconfirm,
            {
              onPositiveClick: () => handleDelete(row.id)
            },
            {
              default: () => '确定删除此分类吗？',
              trigger: () =>
                h(
                  NButton,
                  {
                    size: 'small',
                    type: 'error',
                    ghost: true
                  },
                  {
                    default: () => '删除',
                    icon: () => h(NIcon, { component: TrashOutline })
                  }
                )
            }
          )
        ]
      })
    }
  }
]

// 获取数据
const fetchData = async () => {
  try {
    loading.value = true
    const params = {
      page: paginationReactive.page,
      page_size: paginationReactive.pageSize,
      search: searchKeyword.value || undefined,
      is_active: statusFilter.value !== null ? statusFilter.value === 'true' : undefined
    }
    
    const response = await optionsStore.fetchCategories()
    tableData.value = optionsStore.categories
    paginationReactive.itemCount = optionsStore.categories.length
  } catch (error) {
    message.error('获取分类数据失败')
    console.error('获取分类数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  paginationReactive.page = 1
  fetchData()
}

// 刷新数据
const handleRefresh = () => {
  fetchData()
}

// 分页处理
const handlePageChange = (page: number) => {
  paginationReactive.page = page
  fetchData()
}

const handlePageSizeChange = (pageSize: number) => {
  paginationReactive.pageSize = pageSize
  paginationReactive.page = 1
  fetchData()
}

// 新增分类
const handleCreate = () => {
  isEdit.value = false
  formData.value = {
    code: '',
    name: '',
    description: '',
    sort_order: 0,
    is_active: true
  }
  showModal.value = true
}

// 编辑分类
const handleEdit = (row: OptionCategory) => {
  isEdit.value = true
  formData.value = {
    code: row.code,
    name: row.name,
    description: row.description || '',
    sort_order: row.sort_order,
    is_active: row.is_active
  }
  showModal.value = true
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    if (isEdit.value) {
      // 编辑逻辑需要找到当前编辑的分类ID
      const currentCategory = optionsStore.categories.find(cat => cat.code === formData.value.code)
      if (currentCategory) {
        // 使用字符串ID，不要转换为数字
        await optionsStore.updateCategory(currentCategory.id, formData.value as UpdateOptionCategoryRequest)
        message.success('分类更新成功')
      }
    } else {
      await optionsStore.createCategory(formData.value)
      message.success('分类创建成功')
    }
    
    showModal.value = false
    await fetchData()
  } catch (error) {
    message.error(isEdit.value ? '分类更新失败' : '分类创建失败')
    console.error('提交失败:', error)
  } finally {
    submitting.value = false
  }
}

// 删除分类
const handleDelete = async (id: string) => {
  try {
    // 使用字符串ID，不要转换为数字
    await optionsStore.deleteCategory(id)
    message.success('分类删除成功')
    await fetchData()
  } catch (error) {
    message.error('分类删除失败')
    console.error('删除失败:', error)
  }
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.category-manager {
  padding: 16px;
}

.action-bar {
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>