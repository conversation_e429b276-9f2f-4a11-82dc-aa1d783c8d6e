<template>
  <div class="login-container">
    <div class="login-background">
      <!-- 背景装饰 -->
      <div class="bg-decoration">
        <div class="circle circle-1"></div>
        <div class="circle circle-2"></div>
        <div class="circle circle-3"></div>
      </div>
    </div>
    
    <div class="login-content">
      <!-- 左侧信息区域 -->
      <div class="login-info">
        <div class="logo-section">
          <div class="logo">
            <n-icon size="48" color="#1890ff">
              <BusinessOutline />
            </n-icon>
            <h1 class="logo-text">YYSH</h1>
          </div>
          <p class="logo-subtitle">客户管理系统</p>
        </div>
        
        <div class="feature-list">
          <div class="feature-item">
            <n-icon size="20" color="#52c41a">
              <CheckmarkCircleOutline />
            </n-icon>
            <span>智能客户管理</span>
          </div>
          <div class="feature-item">
            <n-icon size="20" color="#52c41a">
              <CheckmarkCircleOutline />
            </n-icon>
            <span>高效跟进记录</span>
          </div>
          <div class="feature-item">
            <n-icon size="20" color="#52c41a">
              <CheckmarkCircleOutline />
            </n-icon>
            <span>数据分析洞察</span>
          </div>
          <div class="feature-item">
            <n-icon size="20" color="#52c41a">
              <CheckmarkCircleOutline />
            </n-icon>
            <span>团队协作管理</span>
          </div>
        </div>
      </div>
      
      <!-- 右侧登录表单 -->
      <div class="login-form-container">
        <div class="login-form">
          <div class="form-header">
            <h2>欢迎回来</h2>
            <p>请登录您的账户</p>
          </div>
          
          <n-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            size="large"
            @submit.prevent="handleSubmit"
          >
            <n-form-item path="username" :show-label="false">
              <n-input
                v-model:value="formData.username"
                placeholder="用户名/邮箱/手机号"
                :input-props="{ autocomplete: 'username' }"
              >
                <template #prefix>
                  <n-icon size="18">
                    <PersonOutline />
                  </n-icon>
                </template>
              </n-input>
            </n-form-item>
            
            <n-form-item path="password" :show-label="false">
              <n-input
                v-model:value="formData.password"
                type="password"
                placeholder="密码"
                show-password-on="mousedown"
                :input-props="{ autocomplete: 'current-password' }"
                @keyup.enter="handleSubmit"
              >
                <template #prefix>
                  <n-icon size="18">
                    <LockClosedOutline />
                  </n-icon>
                </template>
              </n-input>
            </n-form-item>
            
            <!-- 验证码 -->
            <n-form-item
              v-if="showCaptcha"
              path="captcha"
              :show-label="false"
            >
              <div class="captcha-container">
                <n-input
                  v-model:value="formData.captcha"
                  placeholder="验证码"
                  class="captcha-input"
                >
                  <template #prefix>
                    <n-icon size="18">
                      <ShieldCheckmarkOutline />
                    </n-icon>
                  </template>
                </n-input>
                <div class="captcha-image" @click="refreshCaptcha">
                  <img :src="captchaUrl" alt="验证码" />
                </div>
              </div>
            </n-form-item>
            
            <!-- 记住我和忘记密码 -->
            <div class="form-options">
              <n-checkbox v-model:checked="formData.remember">
                记住我
              </n-checkbox>

            </div>
            
            <!-- 登录按钮 -->
            <n-button
              type="primary"
              size="large"
              block
              :loading="loading"
              @click="handleSubmit"
              class="login-button"
            >
              登录
            </n-button>
          </n-form>
          
          <!-- 其他登录方式 -->
          <div class="other-login">
            <n-divider>其他登录方式</n-divider>
            <div class="social-login">
              <n-button circle quaternary size="large" @click="handleWeChatLogin" :loading="wechatLoading">
                <template #icon>
                  <n-icon size="20">
                    <LogoWechat />
                  </n-icon>
                </template>
              </n-button>
              <n-button circle quaternary size="large">
                <template #icon>
                  <n-icon size="20">
                    <PhonePortraitOutline />
                  </n-icon>
                </template>
              </n-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    


    <!-- 企业微信二维码弹窗 -->
    <n-modal v-model:show="showWeChatQR" preset="dialog" title="企业微信登录" style="width: 400px;">
      <div class="wechat-qr-container">
        <div class="qr-header">
          <n-icon size="24" color="#1890ff">
            <LogoWechat />
          </n-icon>
          <span class="qr-title">请使用企业微信扫码登录</span>
        </div>
        
        <div class="qr-code-wrapper">
          <div v-if="wechatQRUrl" class="qr-code">
            <img :src="wechatQRUrl" alt="企业微信登录二维码" />
          </div>
          <div v-else class="qr-loading">
            <n-spin size="large" />
            <p>正在生成二维码...</p>
          </div>
        </div>
        
        <div class="qr-tips">
          <p>1. 打开企业微信客户端</p>
          <p>2. 点击右上角扫一扫</p>
          <p>3. 扫描上方二维码完成登录</p>
        </div>
        
        <div class="qr-actions">
          <n-button @click="refreshWeChatQR" type="primary" ghost>
            刷新二维码
          </n-button>
        </div>
      </div>
      
      <template #action>
        <n-button @click="closeWeChatQR">取消</n-button>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  NForm,
  NFormItem,
  NInput,
  NButton,
  NCheckbox,
  NDivider,
  NIcon,
  NModal,
  NSpin,
  useMessage,
  type FormInst,
  type FormRules
} from 'naive-ui'
import {
  BusinessOutline,
  CheckmarkCircleOutline,
  PersonOutline,
  LockClosedOutline,
  ShieldCheckmarkOutline,
  LogoWechat,
  PhonePortraitOutline
} from '@vicons/ionicons5'
import { useAuthStore } from '@/stores'
import { validatePhone } from '@/utils'

const router = useRouter()
const route = useRoute()
const message = useMessage()
const authStore = useAuthStore()

// 表单引用
const formRef = ref<FormInst | null>(null)


// 响应式数据
const loading = ref(false)

const wechatLoading = ref(false)
const showCaptcha = ref(false)

const showWeChatQR = ref(false)
const captchaUrl = ref('')
const wechatQRUrl = ref('')

// 登录表单数据
const formData = reactive({
  username: '',
  password: '',
  captcha: '',
  remember: false
})



// 表单验证规则
const formRules: FormRules = {
  username: [
    {
      required: true,
      message: '请输入用户名、邮箱或手机号',
      trigger: ['input', 'blur']
    },
    {
      validator: (rule, value) => {
        if (!value) return true
        // 简单验证：可以是用户名、邮箱或手机号
        if (value.length < 3) {
          return new Error('用户名至少3个字符')
        }
        return true
      },
      trigger: ['input', 'blur']
    }
  ],
  password: [
    {
      required: true,
      message: '请输入密码',
      trigger: ['input', 'blur']
    },
    {
      min: 6,
      message: '密码至少6个字符',
      trigger: ['input', 'blur']
    }
  ],
  captcha: [
    {
      required: true,
      message: '请输入验证码',
      trigger: ['input', 'blur'],
      validator: () => showCaptcha.value
    }
  ]
}



// 处理登录提交
const handleSubmit = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    loading.value = true
    
    // 调用登录API
    const result = await authStore.login({
      username: formData.username,
      password: formData.password,
      captcha: formData.captcha,
      remember: formData.remember
    })
    
    if (result.success) {
      message.success('登录成功')
      // 登录成功，跳转到目标页面
      const redirect = route.query.redirect as string || '/dashboard'
      router.push(redirect)
    } else {
      // 登录失败，显示错误信息
      message.error(result.message || '登录失败')
      // 登录失败后显示验证码
      showCaptcha.value = true
      refreshCaptcha()
    }
  } catch (error: any) {
    console.error('登录失败:', error)
    message.error('登录失败，请稍后重试')
    // 表单验证失败或其他错误
    if (error.message && error.message.includes('验证码')) {
      showCaptcha.value = true
      refreshCaptcha()
    }
  } finally {
    loading.value = false
  }
}

// 刷新验证码
const refreshCaptcha = () => {
  // 生成新的验证码URL
  captchaUrl.value = `/api/auth/captcha?t=${Date.now()}`
}



// 处理企业微信登录
const handleWeChatLogin = async () => {
  try {
    wechatLoading.value = true
    
    // 获取企业微信登录二维码
    const response = await fetch('/api/auth/wechat-qr')
    const result = await response.json()
    
    if (result.success) {
      wechatQRUrl.value = result.data.qrUrl
      showWeChatQR.value = true
      
      // 开始轮询检查登录状态
      const pollInterval = setInterval(async () => {
        try {
          const statusResponse = await fetch(`/api/auth/wechat-status?token=${result.data.token}`)
          const statusResult = await statusResponse.json()
          
          if (statusResult.success) {
            if (statusResult.data.status === 'success') {
              clearInterval(pollInterval)
              showWeChatQR.value = false
              
              // 使用返回的token进行登录
              const loginResult = await authStore.loginWithToken(statusResult.data.userToken)
              if (loginResult.success) {
                message.success('企业微信登录成功')
                const redirect = route.query.redirect as string || '/dashboard'
                router.push(redirect)
              } else {
                message.error('登录失败，请重试')
              }
            } else if (statusResult.data.status === 'expired') {
              clearInterval(pollInterval)
              showWeChatQR.value = false
              message.error('二维码已过期，请重新获取')
            }
          }
        } catch (error) {
          console.error('检查登录状态失败:', error)
        }
      }, 2000)
      
      // 5分钟后停止轮询
      setTimeout(() => {
        clearInterval(pollInterval)
        if (showWeChatQR.value) {
          showWeChatQR.value = false
          message.error('登录超时，请重试')
        }
      }, 300000)
      
    } else {
      message.error(result.message || '获取企业微信二维码失败')
    }
  } catch (error) {
    console.error('企业微信登录失败:', error)
    message.error('企业微信登录失败，请稍后重试')
  } finally {
    wechatLoading.value = false
  }
}

// 关闭企业微信二维码弹窗
const closeWeChatQR = () => {
  showWeChatQR.value = false
  wechatQRUrl.value = ''
}

// 刷新企业微信二维码
const refreshWeChatQR = () => {
  closeWeChatQR()
  setTimeout(() => {
    handleWeChatLogin()
  }, 100)
}

// 组件挂载时的初始化
onMounted(() => {
  // 如果已经登录，直接跳转
  if (authStore.isAuthenticated) {
    const redirect = route.query.redirect as string || '/dashboard'
    router.push(redirect)
  }
  
  // 开发环境下预填充表单
  if (import.meta.env.DEV) {
    formData.username = 'admin'
    formData.password = 'password'
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  position: relative;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 0;
}

.bg-decoration {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.login-content {
  display: flex;
  width: 100%;
  position: relative;
  z-index: 1;
}

.login-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60px;
  color: white;
}

.logo-section {
  text-align: center;
  margin-bottom: 60px;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 16px;
}

.logo-text {
  font-size: 48px;
  font-weight: 700;
  margin: 0;
  color: white;
}

.logo-subtitle {
  font-size: 18px;
  opacity: 0.9;
  margin: 0;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  opacity: 0.9;
}

.login-form-container {
  flex: 0 0 480px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.login-form {
  width: 100%;
  max-width: 360px;
  padding: 40px;
}

.form-header {
  text-align: center;
  margin-bottom: 32px;
}

.form-header h2 {
  font-size: 28px;
  font-weight: 600;
  color: var(--n-text-color);
  margin: 0 0 8px 0;
}

.form-header p {
  font-size: 14px;
  color: var(--n-text-color-disabled);
  margin: 0;
}

.captcha-container {
  display: flex;
  gap: 12px;
  width: 100%;
}

.captcha-input {
  flex: 1;
}

.captcha-image {
  width: 100px;
  height: 40px;
  border: 1px solid var(--n-border-color);
  border-radius: 3px;
  cursor: pointer;
  overflow: hidden;
}

.captcha-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 16px 0 24px 0;
}

.login-button {
  height: 44px;
  font-size: 16px;
  font-weight: 500;
}

.other-login {
  margin-top: 32px;
}

.social-login {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 16px;
}

/* 企业微信二维码弹窗样式 */
.wechat-qr-container {
  text-align: center;
  padding: 20px 0;
}

.qr-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 20px;
}

.qr-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--n-text-color);
}

.qr-code-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  margin-bottom: 20px;
}

.qr-code {
  padding: 10px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.qr-code img {
  width: 180px;
  height: 180px;
  display: block;
}

.qr-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.qr-loading p {
  margin: 0;
  color: var(--n-text-color-disabled);
  font-size: 14px;
}

.qr-tips {
  text-align: left;
  background: var(--n-color-target);
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.qr-tips p {
  margin: 4px 0;
  font-size: 14px;
  color: var(--n-text-color);
}

.qr-actions {
  margin-bottom: 10px;
}

@media (max-width: 768px) {
  .login-content {
    flex-direction: column;
  }
  
  .login-info {
    flex: none;
    padding: 40px 20px;
  }
  
  .login-form-container {
    flex: none;
    background: white;
  }
  
  .login-form {
    padding: 20px;
  }
  
  .wechat-qr-container {
    padding: 10px 0;
  }
  
  .qr-code img {
    width: 150px;
    height: 150px;
  }
}
</style>