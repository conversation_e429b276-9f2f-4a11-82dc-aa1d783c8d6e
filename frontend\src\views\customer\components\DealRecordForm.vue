<template>
  <div class="deal-record-form">
    <!-- 表单头部信息 -->
    <div class="form-header">
      <div class="header-info">
        <n-icon size="18" color="#1677ff">
          <contract-outline />
        </n-icon>
        <span class="header-title">{{ isEdit ? '编辑成交记录' : '新增成交记录' }}</span>
      </div>
      <div class="header-tips">
        <n-alert type="success" :show-icon="false" size="small">
          恭喜！记录客户成交信息，包括合同详情、付款方式和项目安排
        </n-alert>
      </div>
    </div>

    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="100px"
      require-mark-placement="right-hanging"
      size="medium"
    >
      <!-- 合同基础信息区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#1677ff">
            <document-text-outline />
          </n-icon>
          <span class="section-title">合同基础信息</span>
        </div>
        
        <n-grid :cols="2" :x-gap="20" :y-gap="16" responsive="screen">
          <n-form-item-gi label="签单时间" path="contract_time">
            <n-date-picker
              v-model:value="formData.contractTime"
              type="datetime"
              placeholder="选择签单时间"
              style="width: 100%"
              :is-date-disabled="(ts: number) => ts > Date.now()"
              format="yyyy-MM-dd HH:mm"
              value-format="timestamp"
            />
          </n-form-item-gi>
          
          <n-form-item-gi label="签单金额" path="contractAmount">
            <n-input-number
              v-model:value="formData.contractAmount"
              placeholder="请输入签单金额"
              style="width: 100%"
              :min="0"
              :step="1000"
              :precision="2"
              :show-button="false"
              :format="formatCurrency"
              :parse="parseCurrency"
            >
              <template #prefix>
                <n-icon size="16" color="#52c41a">
                  <cash-outline />
                </n-icon>
              </template>
              <template #suffix>元</template>
            </n-input-number>
          </n-form-item-gi>
          
          <n-form-item-gi label="签单套餐" path="packageType">
          <n-select
            v-model:value="formData.packageType"
              placeholder="选择签单套餐"
              :options="packageOptions"
              :render-label="renderPackageLabel"
            />
          </n-form-item-gi>
          
          <n-form-item-gi label="付款方式" path="paymentMethod">
            <n-select
              v-model:value="formData.paymentMethod"
              placeholder="选择付款方式"
              :options="paymentOptions"
              :render-label="renderPaymentLabel"
            />
          </n-form-item-gi>
        </n-grid>
      </div>

      <!-- 人员安排区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#52c41a">
            <people-outline />
          </n-icon>
          <span class="section-title">人员安排</span>
        </div>
        
        <n-grid :cols="2" :x-gap="20" :y-gap="16" responsive="screen">
          <n-form-item-gi label="设计师" path="designer">
            <n-select
              v-model:value="formData.designer"
              placeholder="选择设计师"
              :options="designerOptions"
              :render-label="renderDesignerLabel"
            />
          </n-form-item-gi>
          
          <n-form-item-gi label="销售顾问" path="sales">
          <n-select
            v-model:value="formData.sales"
              placeholder="选择销售顾问"
              :options="salesOptions"
              :render-label="renderSalesLabel"
            />
          </n-form-item-gi>
        </n-grid>
      </div>

      <!-- 项目时间安排区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#faad14">
            <calendar-outline />
          </n-icon>
          <span class="section-title">项目时间安排</span>
        </div>
        
        <n-grid :cols="2" :x-gap="20" :y-gap="16" responsive="screen">
          <n-form-item-gi label="项目工期" path="projectDuration">
            <n-input-number
              v-model:value="formData.projectDuration"
              placeholder="请输入项目工期（天）"
              :min="1"
              :max="365"
              style="width: 100%;"
            >
              <template #suffix>天</template>
            </n-input-number>
          </n-form-item-gi>
        </n-grid>
        
        <div class="time-duration" v-if="formData.projectDuration">
          <n-alert type="info" :show-icon="false" size="small">
            <template #header>
              <n-icon size="14" color="#1677ff">
                <time-outline />
              </n-icon>
              <span style="margin-left: 4px;">项目工期</span>
            </template>
            预计工期：{{ formData.projectDuration }} 天
          </n-alert>
        </div>
      </div>

      <!-- 项目详情区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#722ed1">
            <construct-outline />
          </n-icon>
          <span class="section-title">项目详情</span>
        </div>
        
        <n-form-item label="合同备注" path="remark">
          <n-input
            v-model:value="formData.remark"
            type="textarea"
            placeholder="记录合同相关的重要信息：&#10;• 特殊条款和约定&#10;• 客户特殊要求&#10;• 付款节点安排&#10;• 其他重要事项"
            :rows="4"
            show-count
            maxlength="500"
            :input-props="{ spellcheck: false }"
          />
        </n-form-item>
        
        <n-form-item label="项目详情">
          <n-input
            v-model:value="formData.remark"
            type="textarea"
            placeholder="记录项目的具体内容：&#10;• 施工范围和项目&#10;• 材料品牌和规格&#10;• 设计风格和要求&#10;• 施工标准和工艺"
            :rows="5"
            show-count
            maxlength="800"
            :input-props="{ spellcheck: false }"
          />
        </n-form-item>
      </div>

      <!-- 交易凭证区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#13c2c2">
            <camera-outline />
          </n-icon>
          <span class="section-title">交易凭证</span>
        </div>
        
        <n-form-item label="交易截图">
          <div class="screenshot-section">
            <n-upload
              ref="uploadRef"
              v-model:file-list="fileList"
              multiple
              :max="8"
              list-type="image-card"
              accept="image/*"
              :custom-request="handleUpload"
              @remove="handleRemove"
            >
              <n-button size="medium" type="primary" ghost>
                <template #icon>
                  <n-icon><camera-outline /></n-icon>
                </template>
                上传截图
              </n-button>
            </n-upload>
            <div class="upload-tip">
              <n-text depth="3" style="font-size: 12px">
                上传付款截图、合同照片等相关图片，最多8张，单张不超过5MB
              </n-text>
            </div>
          </div>
        </n-form-item>
      </div>

      <!-- 客户评价区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#eb2f96">
            <heart-outline />
          </n-icon>
          <span class="section-title">客户评价</span>
        </div>
        
        <n-form-item label="客户满意度">
          <div class="satisfaction-section">
            <n-rate
              v-model:value="formData.satisfaction"
              :count="5"
              allow-half
              clearable
              size="large"
            />
            <div class="satisfaction-text">
              <n-text type="info" style="font-size: 14px; font-weight: 500;">
            客户满意度
              </n-text>
            </div>
          </div>
        </n-form-item>
        
        <n-form-item label="其他备注">
          <n-input
            v-model:value="formData.remark"
            type="textarea"
            placeholder="其他需要记录的信息：&#10;• 客户反馈和建议&#10;• 后续服务安排&#10;• 注意事项和提醒"
            :rows="3"
            show-count
            maxlength="200"
            :input-props="{ spellcheck: false }"
          />
        </n-form-item>
      </div>

      <!-- 快速操作区域 -->
      <div class="form-section">
        <div class="section-header">
          <n-icon size="16" color="#fa8c16">
            <flash-outline />
          </n-icon>
          <span class="section-title">快速操作</span>
        </div>
        
        <div class="quick-actions">
          <n-space>
            <n-button size="small" @click="fillQuickTemplate('basic')">
              基础装修
            </n-button>
            <n-button size="small" @click="fillQuickTemplate('premium')">
              精装修
            </n-button>
            <n-button size="small" @click="fillQuickTemplate('luxury')">
              豪华装修
            </n-button>
            <n-button size="small" @click="fillQuickTemplate('custom')">
              全屋定制
            </n-button>
          </n-space>
        </div>
      </div>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, h, onMounted, type VNodeChild } from 'vue'
import {
  NModal,
  NForm,
  NFormItem,
  NFormItemGi,
  NGrid,
  NInput,
  NInputNumber,
  NSelect,
  NDatePicker,
  NUpload,
  NButton,
  NIcon,
  NText,
  NRate,
  NAlert,
  NSpace,
  NTag,
  useMessage,
  type FormInst,
  type FormRules,
  type UploadFileInfo,
  type UploadCustomRequestOptions,
  type SelectRenderLabel
} from 'naive-ui'
import {
  ContractOutline,
  DocumentTextOutline,
  CashOutline,
  PeopleOutline,
  CalendarOutline,
  TimeOutline,
  ConstructOutline,
  CameraOutline,
  HeartOutline,
  FlashOutline,
  PersonOutline,
  BusinessOutline,
  CheckmarkCircleOutline,
  CardOutline,
  WalletOutline,
  StarOutline
} from '@vicons/ionicons5'
import { useCustomerOptionsStore } from '@/stores/customerOptions'
import { CustomerOptionCategory } from '@/constants/customerOptions'
import type { DealRecord } from '@/types'

// Props
interface Props {
  modelValue: DealRecord | null
  isEdit?: boolean
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isEdit: false,
  loading: false
})

// Emits
interface Emits {
  (e: 'update:modelValue', value: DealRecord): void
  (e: 'submit'): void
}

const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref<FormInst>()
const uploadRef = ref()
const message = useMessage()
const customerOptionsStore = useCustomerOptionsStore()

// 表单数据
const formData = reactive<DealRecord & { satisfaction: number }>({
  id: 0,
  customer_id: 0,
  customer_name: '',
  packageType: '',
  contractAmount: 0,
  paidAmount: 0,
  remainingAmount: 0,
  paymentMethod: '',
  contractTime: Date.now(),
  projectDuration: 0,
  designer: '',
  designerId: 0,
  sales: '',
  salesId: 0,
  contractNo: '',
  status: 'pending',
  remark: '',
  satisfaction: 0,
  attachments: [],
  created_by: 0,
  created_by_name: '',
  created_at: '',
  updated_at: ''
})

// 文件列表
const fileList = ref<UploadFileInfo[]>([])

// 选项数据 - 从store获取
const packageOptions = computed(() => {
  const options = customerOptionsStore.getCategoryItems(CustomerOptionCategory.PACKAGE_TYPE)
  return options.map((option: any) => ({
    label: option.name,
    value: option.code,
    icon: BusinessOutline,
    color: '#1677ff',
    price: option.description || '面议'
  }))
})

const paymentOptions = computed(() => {
  const options = customerOptionsStore.getCategoryItems(CustomerOptionCategory.PAYMENT_METHOD)
  return options.map((option: any) => ({
    label: option.name,
    value: option.code,
    icon: CashOutline,
    color: '#1677ff',
    desc: option.description || ''
  }))
})

const designerOptions = computed(() => {
  const options = customerOptionsStore.getCategoryItems(CustomerOptionCategory.DESIGNER)
  return options.map((option: any) => ({
    label: option.name,
    value: option.code,
    icon: PersonOutline,
    color: '#1677ff',
    level: option.description || '设计师'
  }))
})

const salesOptions = computed(() => {
  const options = customerOptionsStore.getCategoryItems(CustomerOptionCategory.SALES)
  return options.map((option: any) => ({
    label: option.name,
    value: option.code,
    icon: PersonOutline,
    color: '#1677ff',
    level: option.description || '销售顾问'
  }))
})

// 渲染标签函数
const renderPackageLabel: SelectRenderLabel = (option): VNodeChild => {
  const opt = packageOptions.value.find((item: any) => item.value === option.value)
  if (!opt) return option.label as VNodeChild
  
  return h('div', { style: 'display: flex; align-items: center; justify-content: space-between; width: 100%;' }, [
    h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
      h(NIcon, { size: 16, color: opt.color }, { default: () => h(opt.icon) }),
      h('span', opt.label)
    ]),
    h(NTag, { size: 'small', type: 'info' }, { default: () => opt.price })
  ])
}

const renderPaymentLabel: SelectRenderLabel = (option): VNodeChild => {
  const opt = paymentOptions.value.find((item: any) => item.value === option.value)
  if (!opt) return option.label as VNodeChild
  
  return h('div', { style: 'display: flex; align-items: center; justify-content: space-between; width: 100%;' }, [
    h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
      h(NIcon, { size: 16, color: opt.color }, { default: () => h(opt.icon) }),
      h('span', opt.label)
    ]),
    h(NText, { depth: 3, style: 'font-size: 12px;' }, { default: () => opt.desc })
  ])
}

const renderDesignerLabel: SelectRenderLabel = (option): VNodeChild => {
  const opt = designerOptions.value.find((item: any) => item.value === option.value)
  if (!opt) return option.label as VNodeChild
  
  return h('div', { style: 'display: flex; align-items: center; justify-content: space-between; width: 100%;' }, [
    h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
      h(NIcon, { size: 16, color: opt.color }, { default: () => h(opt.icon) }),
      h('span', opt.label)
    ]),
    h(NTag, { size: 'small', type: 'success' }, { default: () => opt.level })
  ])
}

const renderSalesLabel: SelectRenderLabel = (option): VNodeChild => {
  const opt = salesOptions.value.find((item: any) => item.value === option.value)
  if (!opt) return option.label as VNodeChild
  
  return h('div', { style: 'display: flex; align-items: center; justify-content: space-between; width: 100%;' }, [
    h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
      h(NIcon, { size: 16, color: opt.color }, { default: () => h(opt.icon) }),
      h('span', opt.label)
    ]),
    h(NTag, { size: 'small', type: 'warning' }, { default: () => opt.level })
  ])
}

// 格式化金额
const formatCurrency = (value: number | null): string => {
  if (value === null || value === undefined) return ''
  return value.toLocaleString('zh-CN')
}

const parseCurrency = (input: string): number | null => {
  const value = input.replace(/,/g, '')
  return isNaN(Number(value)) ? null : Number(value)
}

// 计算剩余金额
const calculateRemainingAmount = () => {
  formData.remainingAmount = formData.contractAmount - formData.paidAmount
}

// 快速模板填充
const fillQuickTemplate = (type: string) => {
  const templates = {
    basic: {
      packageType: 'basic_package',
      paymentMethod: 'installment_3',
      remark: '基础装修套餐，包含水电改造、墙面处理、地面铺装等基础项目。材料选用环保标准产品，施工周期约60-90天。'
    },
    premium: {
      packageType: 'premium_package',
      paymentMethod: 'installment_2',
      remark: '精装修套餐，在基础装修基础上增加定制柜体、品牌材料等。包含主材和部分软装，施工周期约90-120天。'
    },
    luxury: {
      packageType: 'luxury_package',
      paymentMethod: 'full_payment',
      remark: '豪华装修套餐，采用进口材料和高端品牌，包含全屋定制和软装设计。施工周期约120-150天，提供5年质保。'
    },
    custom: {
      packageType: 'custom_package',
      paymentMethod: 'progress_payment',
      remark: '全屋定制套餐，根据客户需求量身定制设计方案。包含个性化定制家具和专属设计服务。'
    }
  }
  
  const template = templates[type as keyof typeof templates]
  if (template) {
    Object.assign(formData, template)
    // 设置合同时间为当前时间
    formData.contractTime = Date.now()
    // 根据套餐类型设置项目工期
    const durationMap = { basic: 90, premium: 120, luxury: 150, custom: 120 }
    formData.projectDuration = durationMap[type as keyof typeof durationMap] || 90
  }
}

// 表单验证规则
const rules: FormRules = {
  contractTime: {
    required: true,
    type: 'number',
    message: '请选择签单时间',
    trigger: ['blur', 'change']
  },
  packageType: {
    required: true,
    message: '请选择签单套餐',
    trigger: ['blur', 'change']
  },
  paymentMethod: {
    required: true,
    message: '请选择付款方式',
    trigger: ['blur', 'change']
  },
  designer: {
    required: true,
    message: '请选择设计师',
    trigger: ['blur', 'change']
  },
  sales: {
    required: true,
    message: '请选择销售顾问',
    trigger: ['blur', 'change']
  }
}

// 满意度文本和类型
const getSatisfactionText = (rating?: number): string => {
  if (!rating) return '未评价'
  if (rating <= 1) return '非常不满意'
  if (rating <= 2) return '不满意'
  if (rating <= 3) return '一般'
  if (rating <= 4) return '满意'
  return '非常满意'
}

const getSatisfactionType = (rating?: number): 'default' | 'success' | 'warning' | 'error' => {
  if (!rating) return 'default'
  if (rating <= 2) return 'error'
  if (rating <= 3) return 'warning'
  return 'success'
}

// 文件上传处理
const handleUpload = ({ file, onFinish, onError }: UploadCustomRequestOptions) => {
  // 模拟文件上传
  setTimeout(() => {
    if (file.file) {
      const url = URL.createObjectURL(file.file)
      
      if (!formData.attachments) {
    formData.attachments = []
  }
  formData.attachments.push(url)
      
      onFinish()
      message.success(`${file.name} 上传成功`)
    } else {
      onError()
      message.error(`${file.name} 上传失败`)
    }
  }, 1000)
}

// 文件删除处理
const handleRemove = (options: { file: UploadFileInfo; fileList: UploadFileInfo[] }) => {
  const { file } = options
  if (formData.attachments && file.url) {
    const index = formData.attachments.findIndex(url => url === file.url)
    if (index > -1) {
      formData.attachments.splice(index, 1)
    }
  }
  message.info(`${file.name} 已删除`)
}

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      Object.assign(formData, {
        ...newValue,
        contractTime: newValue.contractTime ? (typeof newValue.contractTime === 'string' ? new Date(newValue.contractTime).getTime() : newValue.contractTime) : Date.now(),
        attachments: newValue.attachments || []
      })
      
      // 更新文件列表
      fileList.value = (newValue.attachments || []).map((url, index) => ({
        id: `file-${index}`,
        name: `附件${index + 1}`,
        status: 'finished' as const,
        url
      }))
    }
  },
  { immediate: true, deep: true }
)

// 监听表单数据变化，同步到父组件
watch(
  formData,
  (newValue) => {
    emit('update:modelValue', { ...newValue })
  },
  { deep: true }
)

// 表单验证方法
const validate = async (): Promise<boolean> => {
  try {
    await formRef.value?.validate()
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetForm = () => {
  formRef.value?.restoreValidation()
  Object.assign(formData, {
    id: 0,
    customer_id: 0,
    customer_name: '',
    packageType: '',
    contractAmount: 0,
    paidAmount: 0,
    remainingAmount: 0,
    paymentMethod: '',
    contractTime: Date.now(),
    projectDuration: 0,
    designer: '',
    designerId: 0,
    sales: '',
    salesId: 0,
    contractNo: '',
    status: 'pending',
    remark: '',
    attachments: [],
    created_by: 0,
    created_by_name: '',
    created_at: '',
    updated_at: ''
  })
  fileList.value = []
}

// 组件挂载时加载选项数据
onMounted(async () => {
  await customerOptionsStore.loadAllCustomerOptions()
})

// 暴露方法给父组件
defineExpose({
  validate,
  resetForm
})
</script>

<style scoped>
.deal-record-form {
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
}

.form-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.header-tips {
  margin-top: 8px;
}

.form-section {
  margin-bottom: 32px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8e8e8;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.time-duration {
  margin-top: 16px;
}

.screenshot-section {
  width: 100%;
}

.upload-tip {
  margin-top: 8px;
}

.satisfaction-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.satisfaction-text {
  min-width: 80px;
}

.quick-actions {
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

/* 表单样式优化 */
:deep(.n-form-item-label) {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

:deep(.n-input__textarea) {
  min-height: 100px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

:deep(.n-date-picker) {
  width: 100%;
}

:deep(.n-select) {
  width: 100%;
}

:deep(.n-input-number) {
  width: 100%;
}

:deep(.n-upload) {
  width: 100%;
}

:deep(.n-rate) {
  font-size: 24px;
}

/* 表单项间距调整 */
:deep(.n-form-item) {
  margin-bottom: 20px;
}

:deep(.n-form-item:last-child) {
  margin-bottom: 0;
}

/* 必填项标记样式 */
:deep(.n-form-item--required .n-form-item-label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
  font-weight: bold;
}

/* 输入框聚焦效果 */
:deep(.n-input:focus-within) {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

:deep(.n-select:focus-within) {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

:deep(.n-date-picker:focus-within) {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

:deep(.n-input-number:focus-within) {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

/* 选择器选项样式 */
:deep(.n-base-selection-label) {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 上传组件样式调整 */
:deep(.n-upload-file-list) {
  margin-top: 8px;
}

:deep(.n-upload-trigger) {
  width: auto;
}

/* 金额输入框样式 */
:deep(.n-input-number .n-input__prefix) {
  color: #52c41a;
  font-weight: 600;
}

:deep(.n-input-number .n-input__suffix) {
  color: #8c8c8c;
  font-weight: 500;
}

/* 评分组件样式 */
:deep(.n-rate .n-rate__item) {
  margin-right: 8px;
}

/* 滚动条样式 */
.deal-record-form::-webkit-scrollbar {
  width: 6px;
}

.deal-record-form::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.deal-record-form::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.deal-record-form::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .deal-record-form {
    padding: 0;
    max-height: 60vh;
  }
  
  .form-header {
    margin-bottom: 16px;
    padding-bottom: 12px;
  }
  
  .header-title {
    font-size: 14px;
  }
  
  .form-section {
    margin-bottom: 20px;
    padding: 16px;
  }
  
  .section-title {
    font-size: 13px;
  }
  
  :deep(.n-form-item) {
    margin-bottom: 16px;
  }
  
  :deep(.n-form-item-label) {
    font-size: 13px;
  }
  
  :deep(.n-grid) {
    grid-template-columns: 1fr !important;
  }
  
  :deep(.n-rate) {
    font-size: 20px;
  }
  
  .satisfaction-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .form-section {
    padding: 12px;
    margin-bottom: 16px;
  }
  
  .quick-actions {
    padding: 8px;
  }
  
  :deep(.n-space) {
    flex-wrap: wrap;
  }
  
  :deep(.n-button) {
    font-size: 12px;
    padding: 4px 8px;
  }
  
  .upload-tip {
    margin-top: 4px;
  }
  
  :deep(.n-rate) {
    font-size: 18px;
  }
}
</style>