import { http } from './http'
import type { ApiResponse } from '@/types'

// API 响应解包工具函数
const unwrapResponse = <T>(response: any): T => {
  // 后端返回格式: {success: boolean, data: T}
  if (response.data && response.data.success !== undefined) {
    return response.data.data || response.data
  }
  return response.data
}

interface Role {
  id: number
  name: string
  display_name: string
  description?: string
  status: boolean
  is_system?: boolean
  created_at: string
  updated_at: string
}

interface RoleInsert {
  name: string
  display_name: string
  description?: string
  status?: boolean
  is_system?: boolean
}

interface RoleUpdate {
  name?: string
  display_name?: string
  description?: string
  status?: boolean
}

interface Permission {
  id: number
  name: string
  display_name: string
  module: string
  description?: string
  created_at: string
  updated_at: string
}

interface UserRole {
  id: number
  user_id: string
  role_id: number
  expires_at?: string
  created_at: string
  updated_at: string
}

export interface RoleWithPermissions extends Role {
  role_permissions?: {
    permissions: Permission
  }[]
  permissions: Permission[]
}

export interface CreateRoleData {
  name: string
  display_name: string
  description?: string
  status?: boolean
  permission_ids: number[]
}

export interface UpdateRoleData {
  name?: string
  display_name?: string
  description?: string
  status?: boolean
  permission_ids?: number[]
}

export interface RoleQueryParams {
  page?: number
  page_size?: number
  search?: string
  status?: boolean
}

export interface AssignRoleData {
  user_id: string
  role_ids: number[]
  expires_at?: string
}

class RoleService {
  // 获取角色列表
  async getRoles(params?: RoleQueryParams) {
    try {
      const queryParams = new URLSearchParams()
      
      if (params?.page) queryParams.append('page', params.page.toString())
      if (params?.page_size) queryParams.append('page_size', params.page_size.toString())
      if (params?.search) queryParams.append('search', params.search)
      if (params?.status !== undefined) queryParams.append('status', params.status.toString())
      
      const response = await http.get(`/roles?${queryParams.toString()}`)
      return unwrapResponse(response)
    } catch (error) {
      console.error('获取角色列表失败:', error)
      throw error
    }
  }

  // 获取角色详情
  async getRoleDetail(id: number): Promise<ApiResponse<RoleWithPermissions>> {
    try {
      const response = await http.get(`/roles/${id}`)
      return unwrapResponse(response)
    } catch (error) {
      console.error('获取角色详情失败:', error)
      return { success: false, message: '获取角色详情失败', data: undefined as any }
    }
  }

  // 创建角色
  async createRole(roleData: CreateRoleData): Promise<ApiResponse<Role>> {
    try {
      const response = await http.post('/roles', roleData)
      return unwrapResponse(response)
    } catch (error) {
      console.error('创建角色失败:', error)
      return { success: false, message: '创建角色失败', data: undefined as any }
    }
  }

  // 更新角色
  async updateRole(id: number, roleData: UpdateRoleData) {
    try {
      const response = await http.put(`/roles/${id}`, roleData)
      return unwrapResponse(response)
    } catch (error) {
      console.error('更新角色失败:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : '更新角色失败',
        data: undefined as any
      }
    }
  }

  // 删除角色
  async deleteRole(id: number) {
    try {
      const response = await http.delete(`/roles/${id}`)
      return unwrapResponse(response)
    } catch (error) {
      console.error('删除角色失败:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : '删除角色失败',
        data: undefined as any
      }
    }
  }

  // 获取所有权限
  async getPermissions(): Promise<ApiResponse<Permission[]>> {
    try {
      const response = await http.get('/permissions')
      return unwrapResponse(response)
    } catch (error) {
      console.error('获取权限列表失败:', error)
      return { success: false, message: '获取权限列表失败', data: [] }
    }
  }

  // 分配角色给用户
  async assignRoles(assignData: AssignRoleData) {
    try {
      const response = await http.post('/user-roles/assign', assignData)
      return unwrapResponse(response)
    } catch (error) {
      console.error('分配角色失败:', error)
      return {
          success: false,
          message: error instanceof Error ? error.message : '分配角色失败',
          data: undefined as any
        }
    }
  }

  // 获取用户角色
  async getUserRoles(userId: string) {
    try {
      const response = await http.get(`/user-roles/${userId}`)
      return unwrapResponse(response)
    } catch (error) {
      console.error('获取用户角色失败:', error)
      return {
          success: false,
          message: error instanceof Error ? error.message : '获取用户角色失败',
          data: undefined as any
        }
    }
  }
}

export const roleService = new RoleService()
export default roleService

export type {
  Role,
  RoleInsert,
  RoleUpdate,
  Permission,
  UserRole
}