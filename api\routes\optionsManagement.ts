import express, { type Request, type Response } from 'express';
import { getMySQLManager, type MySQLManager } from '../../src/database/MySQLManager.js';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

const router = express.Router();

// 获取 MySQL 管理器实例的辅助函数
function getMySQLManagerInstance(): MySQLManager {
  try {
    return getMySQLManager();
  } catch (error) {
    console.error('MySQL管理器未初始化:', error);
    throw new Error('数据库连接未初始化');
  }
}

// 定义接口类型
interface CreateCategoryRequest {
  code: string;
  name: string;
  description?: string;
  sort_order?: number;
}

interface UpdateCategoryRequest {
  code?: string;
  name?: string;
  description?: string;
  is_active?: boolean;
  sort_order?: number;
}

interface CreateItemRequest {
  category_id: string;
  code: string;
  value: string;
  label: string;
  description?: string;
  color?: string;
  icon?: string;
  sort_order?: number;
}

interface UpdateItemRequest {
  code?: string;
  value?: string;
  label?: string;
  description?: string;
  color?: string;
  icon?: string;
  is_active?: boolean;
  sort_order?: number;
}

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// ==================== 选项分类管理 ====================

/**
 * 获取选项分类列表（管理端专用）
 * GET /api/options-management/categories
 */
router.get('/categories', async (req: Request, res: Response): Promise<void> => {
  try {
    const mysqlManager = getMySQLManagerInstance();
    
    const { 
      search, 
      is_active, 
      page = 1, 
      page_size = 10,
      sort_by = 'sort_order',
      sort_order = 'asc'
    } = req.query;

    // 构建查询条件
    const whereConditions: string[] = [];
    const queryParams: any[] = [];

    // 搜索筛选
    if (search) {
      whereConditions.push('(code LIKE ? OR name LIKE ? OR description LIKE ?)');
      const searchPattern = `%${search}%`;
      queryParams.push(searchPattern, searchPattern, searchPattern);
    }

    // 状态筛选
    if (is_active !== undefined) {
      whereConditions.push('is_active = ?');
      queryParams.push(is_active === 'true' ? 1 : 0);
    }

    // 构建WHERE子句
    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 排序
    const validSortFields = ['id', 'code', 'name', 'sort_order', 'created_at', 'updated_at'];
    const validSortOrders = ['asc', 'desc'];
    
    const sortField = validSortFields.includes(sort_by as string) ? sort_by as string : 'sort_order';
    const sortDirection = validSortOrders.includes(sort_order as string) ? sort_order as string : 'asc';

    // 分页
    const pageNum = Math.max(1, parseInt(page as string) || 1);
    const pageSize = Math.max(1, Math.min(100, parseInt(page_size as string) || 10));
    const offset = (pageNum - 1) * pageSize;

    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM option_categories ${whereClause}`;
    const countResult = await mysqlManager.query(countSql, queryParams);
    const total = countResult.success && Array.isArray(countResult.data) && countResult.data.length > 0 
      ? (countResult.data[0] as any).total : 0;

    // 获取分页数据
    const dataSql = `
      SELECT * FROM option_categories 
      ${whereClause}
      ORDER BY ${sortField} ${sortDirection.toUpperCase()}
      LIMIT ${offset}, ${pageSize}
    `;
    
    const dataResult = await mysqlManager.query(dataSql, queryParams);

    if (!dataResult.success) {
      res.status(500).json({
        success: false,
        error: `获取选项分类列表失败: ${dataResult.error}`
      } as ApiResponse);
      return;
    }

    const totalPages = Math.ceil(total / pageSize);

    res.json({
      success: true,
      data: {
        data: dataResult.data || [],
        total,
        page: pageNum,
        page_size: pageSize,
        total_pages: totalPages
      }
    } as ApiResponse);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse);
  }
});

/**
 * 创建选项分类
 * POST /api/options-management/categories
 */
router.post('/categories', async (req: Request, res: Response): Promise<void> => {
  try {
    const mysqlManager = getMySQLManagerInstance();
    
    const { code, name, description, sort_order = 0 }: CreateCategoryRequest = req.body;

    if (!code || !name) {
      res.status(400).json({
        success: false,
        error: '分类代码和名称不能为空'
      } as ApiResponse);
      return;
    }

    // 检查分类代码是否已存在
    const checkSql = 'SELECT id FROM option_categories WHERE code = ?';
    const checkResult = await mysqlManager.query(checkSql, [code]);
    
    if (checkResult.success && Array.isArray(checkResult.data) && checkResult.data.length > 0) {
      res.status(400).json({
        success: false,
        error: '分类代码已存在'
      } as ApiResponse);
      return;
    }

    // 生成UUID作为主键
    const { v4: uuidv4 } = require('uuid');
    const categoryId = uuidv4();

    // 创建新分类
    const insertSql = `
      INSERT INTO option_categories (id, code, name, description, sort_order, is_active, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
    `;
    const insertResult = await mysqlManager.query(insertSql, [
      categoryId,
      code,
      name,
      description || null,
      sort_order,
      1
    ]);

    if (!insertResult.success) {
      res.status(500).json({
        success: false,
        error: `创建分类失败: ${insertResult.error}`
      } as ApiResponse);
      return;
    }

    // 获取创建的记录
    const selectSql = 'SELECT * FROM option_categories WHERE id = ?';
    const selectResult = await mysqlManager.query(selectSql, [categoryId]);
    
    const createdCategory = selectResult.success && Array.isArray(selectResult.data) && selectResult.data.length > 0
      ? selectResult.data[0] : null;

    res.status(201).json({
      success: true,
      data: createdCategory,
      message: '分类创建成功'
    } as ApiResponse);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse);
  }
});

/**
 * 更新选项分类
 * PUT /api/options-management/categories/:id
 */
router.put('/categories/:id', async (req: Request, res: Response): Promise<void> => {
  try {
    const mysqlManager = getMySQLManagerInstance();
    
    const { id } = req.params;
    const { code, name, description, is_active, sort_order }: UpdateCategoryRequest = req.body;

    const updateData: any = {};
    if (code !== undefined) updateData.code = code;
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (is_active !== undefined) updateData.is_active = is_active;
    if (sort_order !== undefined) updateData.sort_order = sort_order;

    if (Object.keys(updateData).length === 0) {
      res.status(400).json({
        success: false,
        error: '没有提供要更新的数据'
      } as ApiResponse);
      return;
    }

    // 如果更新代码，检查是否与其他分类重复
    if (code) {
      const checkCodeSql = 'SELECT id FROM option_categories WHERE code = ? AND id != ?';
      const codeResult = await mysqlManager.query(checkCodeSql, [code, id]);
      
      if (codeResult.success && Array.isArray(codeResult.data) && codeResult.data.length > 0) {
        res.status(400).json({
          success: false,
          error: '分类代码已存在'
        } as ApiResponse);
        return;
      }
    }

    // 构建更新SQL
    const updateFields = [];
    const updateParams = [];
    
    if (code !== undefined) {
      updateFields.push('code = ?');
      updateParams.push(code);
    }
    if (name !== undefined) {
      updateFields.push('name = ?');
      updateParams.push(name);
    }
    if (description !== undefined) {
      updateFields.push('description = ?');
      updateParams.push(description);
    }
    if (is_active !== undefined) {
      updateFields.push('is_active = ?');
      updateParams.push(is_active ? 1 : 0);
    }
    if (sort_order !== undefined) {
      updateFields.push('sort_order = ?');
      updateParams.push(sort_order);
    }
    
    updateFields.push('updated_at = NOW()');
    updateParams.push(id);

    const updateSql = `UPDATE option_categories SET ${updateFields.join(', ')} WHERE id = ?`;
    const updateResult = await mysqlManager.query(updateSql, updateParams);

    if (!updateResult.success) {
      res.status(500).json({
        success: false,
        error: `更新分类失败: ${updateResult.error}`
      } as ApiResponse);
      return;
    }

    if (updateResult.affectedRows === 0) {
      res.status(404).json({
        success: false,
        error: '分类不存在'
      } as ApiResponse);
      return;
    }

    // 获取更新后的记录
    const selectSql = 'SELECT * FROM option_categories WHERE id = ?';
    const selectResult = await mysqlManager.query(selectSql, [id]);
    
    const updatedCategory = selectResult.success && Array.isArray(selectResult.data) && selectResult.data.length > 0
      ? selectResult.data[0] : null;

    res.json({
      success: true,
      data: updatedCategory,
      message: '分类更新成功'
    } as ApiResponse);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse);
  }
});

/**
 * 删除选项分类
 * DELETE /api/options-management/categories/:id
 */
router.delete('/categories/:id', async (req: Request, res: Response): Promise<void> => {
  try {
    const mysqlManager = getMySQLManagerInstance();
    
    const { id } = req.params;

    // 检查分类是否存在
    const checkExistSql = 'SELECT id FROM option_categories WHERE id = ?';
    const existResult = await mysqlManager.query(checkExistSql, [id]);
    
    if (!existResult.success || !Array.isArray(existResult.data) || existResult.data.length === 0) {
      res.status(404).json({
        success: false,
        error: '选项分类不存在'
      } as ApiResponse);
      return;
    }

    // 检查分类下是否还有选项项
    const checkItemsSql = 'SELECT id FROM option_items WHERE category_id = ? AND is_active = 1';
    const itemsResult = await mysqlManager.query(checkItemsSql, [id]);

    if (itemsResult.success && Array.isArray(itemsResult.data) && itemsResult.data.length > 0) {
      res.status(400).json({
        success: false,
        error: '该分类下还有激活的选项项，无法删除'
      } as ApiResponse);
      return;
    }

    // 删除分类
    const deleteSql = 'DELETE FROM option_categories WHERE id = ?';
    const deleteResult = await mysqlManager.query(deleteSql, [id]);

    if (!deleteResult.success) {
      res.status(500).json({
        success: false,
        error: `删除分类失败: ${deleteResult.error}`
      } as ApiResponse);
      return;
    }

    if (deleteResult.affectedRows === 0) {
      res.status(404).json({
        success: false,
        error: '选项分类不存在'
      } as ApiResponse);
      return;
    }

    res.json({
      success: true,
      message: '选项分类删除成功'
    } as ApiResponse);
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: `删除分类异常: ${error.message}`
    } as ApiResponse);
  }
});

// ==================== 选项项管理 ====================

/**
 * 获取选项项列表（管理端专用）
 * GET /api/options-management/items
 */
router.get('/items', async (req: Request, res: Response): Promise<void> => {
  try {
    const mysqlManager = getMySQLManagerInstance();
    
    const { 
      category_id, 
      category_code, 
      search, 
      is_active, 
      page = 1, 
      page_size = 20,
      sort_by = 'sort_order',
      sort_order = 'asc'
    } = req.query;

    // 构建查询条件
    const whereConditions: string[] = [];
    const queryParams: any[] = [];

    // 根据分类ID筛选
    if (category_id) {
      whereConditions.push('oi.category_id = ?');
      queryParams.push(category_id);
    }

    // 根据分类代码筛选
    if (category_code) {
      whereConditions.push('oc.code = ?');
      queryParams.push(category_code);
    }

    // 搜索筛选
    if (search) {
      whereConditions.push('(oi.code LIKE ? OR oi.value LIKE ? OR oi.label LIKE ? OR oi.description LIKE ?)');
      const searchPattern = `%${search}%`;
      queryParams.push(searchPattern, searchPattern, searchPattern, searchPattern);
    }

    // 状态筛选
    if (is_active !== undefined) {
      whereConditions.push('oi.is_active = ?');
      queryParams.push(is_active === 'true' ? 1 : 0);
    }

    // 构建WHERE子句
    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 排序
    const validSortFields = ['id', 'code', 'value', 'label', 'sort_order', 'created_at', 'updated_at'];
    const validSortOrders = ['asc', 'desc'];
    
    const sortField = validSortFields.includes(sort_by as string) ? sort_by as string : 'sort_order';
    const sortDirection = validSortOrders.includes(sort_order as string) ? sort_order as string : 'asc';

    // 分页
    const pageNum = Math.max(1, parseInt(page as string) || 1);
    const pageSize = Math.min(100, Math.max(1, parseInt(page_size as string) || 20));
    const offset = (pageNum - 1) * pageSize;

    // 获取总数
    const countSql = `
      SELECT COUNT(*) as total 
      FROM option_items oi 
      LEFT JOIN option_categories oc ON oi.category_id = oc.id 
      ${whereClause}
    `;
    const countResult = await mysqlManager.query(countSql, queryParams);
    const total = countResult.success && Array.isArray(countResult.data) && countResult.data.length > 0 
      ? (countResult.data[0] as any).total : 0;

    // 获取分页数据
    const dataSql = `
      SELECT 
        oi.*,
        oc.id as category_id,
        oc.code as category_code,
        oc.name as category_name
      FROM option_items oi 
      LEFT JOIN option_categories oc ON oi.category_id = oc.id 
      ${whereClause}
      ORDER BY oi.${sortField} ${sortDirection.toUpperCase()}
      LIMIT ${offset}, ${pageSize}
    `;
    const dataResult = await mysqlManager.query(dataSql, queryParams);

    if (!dataResult.success) {
      console.error('获取选项项列表失败:', dataResult.error);
      res.status(500).json({
        success: false,
        message: '获取选项项列表失败',
        error: dataResult.error
      });
      return;
    }

    const totalPages = Math.ceil(total / pageSize);

    res.json({
      success: true,
      data: {
        items: (dataResult.data as any[]) || [],
        total,
        page: pageNum,
        page_size: pageSize,
        total_pages: totalPages
      }
    });
  } catch (error: any) {
    console.error('获取选项项列表异常:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

/**
 * 创建选项项
 * POST /api/options-management/items
 */
router.post('/items', async (req: Request, res: Response): Promise<void> => {
  try {
    const mysqlManager = getMySQLManagerInstance();
    
    const { category_id, code, value, label, description, color, icon, sort_order = 0 }: CreateItemRequest = req.body;

    if (!category_id || !code || !value || !label) {
      res.status(400).json({
        success: false,
        error: '分类ID、代码、值和标签不能为空'
      } as ApiResponse);
      return;
    }

    // 检查分类是否存在
    const checkCategorySql = 'SELECT id FROM option_categories WHERE id = ? AND is_active = 1';
    const categoryResult = await mysqlManager.query(checkCategorySql, [category_id]);
    
    if (!categoryResult.success || !Array.isArray(categoryResult.data) || categoryResult.data.length === 0) {
      res.status(400).json({
        success: false,
        error: '分类不存在或已禁用'
      } as ApiResponse);
      return;
    }

    // 检查同一分类下代码是否已存在
    const checkCodeSql = 'SELECT id FROM option_items WHERE category_id = ? AND code = ?';
    const codeResult = await mysqlManager.query(checkCodeSql, [category_id, code]);
    
    if (codeResult.success && Array.isArray(codeResult.data) && codeResult.data.length > 0) {
      res.status(400).json({
        success: false,
        error: '该分类下已存在相同的代码'
      } as ApiResponse);
      return;
    }

    // 生成UUID作为主键
    const { v4: uuidv4 } = require('uuid');
    const itemId = uuidv4();

    // 创建新选项项
    const insertSql = `
      INSERT INTO option_items (id, category_id, code, value, label, description, color, icon, sort_order, is_active, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `;
    const insertResult = await mysqlManager.query(insertSql, [
      itemId,
      category_id,
      code,
      value,
      label,
      description || null,
      color || null,
      icon || null,
      sort_order,
      1
    ]);

    if (!insertResult.success) {
      res.status(500).json({
        success: false,
        error: `创建选项项失败: ${insertResult.error}`
      } as ApiResponse);
      return;
    }

    // 获取创建的记录
    const selectSql = 'SELECT * FROM option_items WHERE id = ?';
    const selectResult = await mysqlManager.query(selectSql, [itemId]);
    
    const createdItem = selectResult.success && Array.isArray(selectResult.data) && selectResult.data.length > 0
      ? selectResult.data[0] : null;

    res.status(201).json({
      success: true,
      data: createdItem,
      message: '选项项创建成功'
    } as ApiResponse);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse);
  }
});

/**
 * 更新选项项
 * PUT /api/options-management/items/:id
 */
router.put('/items/:id', async (req: Request, res: Response): Promise<void> => {
  try {
    const mysqlManager = getMySQLManagerInstance();
    
    const { id } = req.params;
    const updateData: UpdateItemRequest = req.body;

    const updateFields = [];
    const updateParams = [];
    
    if (updateData.code !== undefined) {
      updateFields.push('code = ?');
      updateParams.push(updateData.code);
    }
    if (updateData.value !== undefined) {
      updateFields.push('value = ?');
      updateParams.push(updateData.value);
    }
    if (updateData.label !== undefined) {
      updateFields.push('label = ?');
      updateParams.push(updateData.label);
    }
    if (updateData.description !== undefined) {
      updateFields.push('description = ?');
      updateParams.push(updateData.description);
    }
    if (updateData.color !== undefined) {
      updateFields.push('color = ?');
      updateParams.push(updateData.color);
    }
    if (updateData.icon !== undefined) {
      updateFields.push('icon = ?');
      updateParams.push(updateData.icon);
    }
    if (updateData.is_active !== undefined) {
      updateFields.push('is_active = ?');
      updateParams.push(updateData.is_active ? 1 : 0);
    }
    if (updateData.sort_order !== undefined) {
      updateFields.push('sort_order = ?');
      updateParams.push(updateData.sort_order);
    }
    
    if (updateFields.length === 0) {
      res.status(400).json({
        success: false,
        error: '没有提供要更新的数据'
      } as ApiResponse);
      return;
    }

    // 如果更新代码，检查同一分类下是否已存在
    if (updateData.code) {
      const getCurrentSql = 'SELECT category_id FROM option_items WHERE id = ?';
      const currentResult = await mysqlManager.query(getCurrentSql, [id]);
      
      if (currentResult.success && Array.isArray(currentResult.data) && currentResult.data.length > 0) {
        const currentItem = currentResult.data[0] as any;
        const checkCodeSql = 'SELECT id FROM option_items WHERE category_id = ? AND code = ? AND id != ?';
        const codeResult = await mysqlManager.query(checkCodeSql, [currentItem.category_id, updateData.code, id]);
        
        if (codeResult.success && Array.isArray(codeResult.data) && codeResult.data.length > 0) {
          res.status(400).json({
            success: false,
            error: '该分类下已存在相同的代码'
          } as ApiResponse);
          return;
        }
      }
    }

    updateFields.push('updated_at = NOW()');
    updateParams.push(id);

    const updateSql = `UPDATE option_items SET ${updateFields.join(', ')} WHERE id = ?`;
    const updateResult = await mysqlManager.query(updateSql, updateParams);

    if (!updateResult.success) {
      res.status(500).json({
        success: false,
        error: `更新选项项失败: ${updateResult.error}`
      } as ApiResponse);
      return;
    }

    if (updateResult.affectedRows === 0) {
      res.status(404).json({
        success: false,
        error: '选项项不存在'
      } as ApiResponse);
      return;
    }

    // 获取更新后的记录
    const selectSql = 'SELECT * FROM option_items WHERE id = ?';
    const selectResult = await mysqlManager.query(selectSql, [id]);
    
    const updatedItem = selectResult.success && Array.isArray(selectResult.data) && selectResult.data.length > 0
      ? selectResult.data[0] : null;

    res.json({
      success: true,
      data: updatedItem,
      message: '选项项更新成功'
    } as ApiResponse);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse);
  }
});

/**
 * 删除选项项
 * DELETE /api/options-management/items/:id
 */
router.delete('/items/:id', async (req: Request, res: Response): Promise<void> => {
  try {
    const mysqlManager = getMySQLManagerInstance();
    
    const { id } = req.params;

    const deleteSql = 'DELETE FROM option_items WHERE id = ?';
    const deleteResult = await mysqlManager.query(deleteSql, [id]);

    if (!deleteResult.success) {
      res.status(500).json({
        success: false,
        error: `删除选项项失败: ${deleteResult.error}`
      } as ApiResponse);
      return;
    }

    if (deleteResult.affectedRows === 0) {
      res.status(404).json({
        success: false,
        error: '选项项不存在'
      } as ApiResponse);
      return;
    }

    res.json({
      success: true,
      message: '选项项删除成功'
    } as ApiResponse);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse);
  }
});

/**
 * 批量更新选项项排序
 * PUT /api/options-management/items/batch-sort
 */
router.put('/items/batch-sort', async (req: Request, res: Response): Promise<void> => {
  try {
    const mysqlManager = getMySQLManagerInstance();
    
    const { items }: { items: { id: string; sort_order: number }[] } = req.body;

    if (!items || !Array.isArray(items)) {
      res.status(400).json({
        success: false,
        error: '请提供有效的选项项列表'
      } as ApiResponse);
      return;
    }

    // 批量更新排序
    const updatePromises = items.map(item => {
      const updateSql = 'UPDATE option_items SET sort_order = ?, updated_at = NOW() WHERE id = ?';
      return mysqlManager.query(updateSql, [item.sort_order, item.id]);
    });

    const results = await Promise.all(updatePromises);
    
    // 检查是否有错误
    const errors = results.filter(result => !result.success);
    if (errors.length > 0) {
      res.status(500).json({
        success: false,
        error: '批量更新排序失败'
      } as ApiResponse);
      return;
    }

    res.json({
      success: true,
      message: '排序更新成功'
    } as ApiResponse);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse);
  }
});

/**
 * 批量删除选项项
 * DELETE /api/options-management/items/batch
 * body: { ids: number[] }
 */
router.delete('/items/batch', async (req: Request, res: Response): Promise<void> => {
  try {
    const mysqlManager = getMySQLManagerInstance();
    
    const { ids } = req.body as { ids: number[] };
    if (!Array.isArray(ids) || ids.length === 0) {
      res.status(400).json({ success: false, error: 'ids 参数不能为空' } as ApiResponse);
      return;
    }

    const deleteSql = `DELETE FROM option_items WHERE id IN (${ids.map(() => '?').join(', ')})`;
    const deleteResult = await mysqlManager.query(deleteSql, ids);

    if (!deleteResult.success) {
      res.status(500).json({ success: false, error: `批量删除失败: ${deleteResult.error}` } as ApiResponse);
      return;
    }

    res.json({ success: true, message: `成功删除 ${deleteResult.affectedRows} 个选项项` } as ApiResponse);
  } catch (error) {
    res.status(500).json({ success: false, error: '服务器内部错误' } as ApiResponse);
  }
});

/**
 * 批量更新选项项状态
 * PUT /api/options-management/items/batch-status
 * body: { ids: number[], is_active: boolean }
 */
router.put('/items/batch-status', async (req: Request, res: Response): Promise<void> => {
  try {
    const mysqlManager = getMySQLManagerInstance();
    
    const { ids, is_active } = req.body as { ids: number[]; is_active: boolean };
    if (!Array.isArray(ids) || ids.length === 0) {
      res.status(400).json({ success: false, error: 'ids 参数不能为空' } as ApiResponse);
      return;
    }

    if (typeof is_active !== 'boolean') {
      res.status(400).json({ success: false, error: 'is_active 必须为布尔值' } as ApiResponse);
      return;
    }

    const updateSql = `UPDATE option_items SET is_active = ?, updated_at = NOW() WHERE id IN (${ids.map(() => '?').join(', ')})`;
    const updateResult = await mysqlManager.query(updateSql, [is_active ? 1 : 0, ...ids]);

    if (!updateResult.success) {
      res.status(500).json({ success: false, error: `批量更新状态失败: ${updateResult.error}` } as ApiResponse);
      return;
    }

    res.json({ success: true, message: `成功更新 ${updateResult.affectedRows} 个选项项的状态` } as ApiResponse);
  } catch (error) {
    res.status(500).json({ success: false, error: '服务器内部错误' } as ApiResponse);
  }
});

export default router;