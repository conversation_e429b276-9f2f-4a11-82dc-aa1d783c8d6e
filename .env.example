# MySQL数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_mysql_password
MYSQL_DATABASE=workchat_admin

# MySQL数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password_here
DB_NAME=workchat_admin

# 迁移配置
MIGRATION_BATCH_SIZE=1000
MIGRATION_MAX_RETRIES=3
MIGRATION_TIMEOUT=300000
MIGRATION_PARALLEL_TABLES=3

# 日志配置
LOG_LEVEL=INFO
LOG_TO_FILE=true
LOG_TO_DATABASE=true
LOG_TO_CONSOLE=true
LOG_FILE_PATH=./logs/migration.log
LOG_MAX_FILE_SIZE=100
LOG_MAX_FILES=10

# 备份配置
BACKUP_ENABLED=true
BACKUP_PATH=./backups
BACKUP_COMPRESS=true
BACKUP_KEEP_COUNT=5

# 性能配置
PERFORMANCE_MONITORING=true
MEMORY_LIMIT=2048
CPU_LIMIT=80
PROGRESS_REPORT_INTERVAL=30

# 错误处理配置
CONTINUE_ON_ERROR=false
MAX_ERRORS=10
AUTO_ROLLBACK=true
ERROR_LOG_PATH=./logs/migration-errors.log