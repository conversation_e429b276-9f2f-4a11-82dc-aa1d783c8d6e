<template>
  <div class="analytics-customer">
    <!-- 页面头部 -->
    <div class="page-header">
      <n-space justify="space-between" align="center">
        <div>
  
          <p class="page-description">客户数据统计与行为分析</p>
        </div>
        <n-space>
          <n-date-picker
            v-model:value="dateRange"
            type="daterange"
            clearable
            placeholder="选择时间范围"
            @update:value="handleDateChange"
          />
          <n-button type="primary" @click="handleExport">
            <template #icon>
              <n-icon><download-outline /></n-icon>
            </template>
            导出报表
          </n-button>
          <n-button type="default" @click="handleRefresh">
            <template #icon>
              <n-icon><refresh-outline /></n-icon>
            </template>
            刷新数据
          </n-button>
        </n-space>
      </n-space>
    </div>

    <!-- 客户概览指标 -->
    <n-grid :cols="4" :x-gap="16" class="metrics-grid">
      <n-card class="metric-card">
        <n-statistic
          label="总客户数"
          :value="metrics.totalCustomers"
        />
        <div class="metric-trend">
          <n-tag :type="metrics.customersTrend >= 0 ? 'success' : 'error'" size="small">
            {{ metrics.customersTrend >= 0 ? '+' : '' }}{{ metrics.customersTrend }}%
          </n-tag>
          <span class="trend-text">较上期</span>
        </div>
      </n-card>
      
      <n-card class="metric-card">
        <n-statistic
          label="新增客户"
          :value="metrics.newCustomers"
        />
        <div class="metric-trend">
          <n-tag :type="metrics.newTrend >= 0 ? 'success' : 'error'" size="small">
            {{ metrics.newTrend >= 0 ? '+' : '' }}{{ metrics.newTrend }}%
          </n-tag>
          <span class="trend-text">较上期</span>
        </div>
      </n-card>
      
      <n-card class="metric-card">
        <n-statistic
          label="活跃客户"
          :value="metrics.activeCustomers"
        />
        <div class="metric-trend">
          <n-tag :type="metrics.activeTrend >= 0 ? 'success' : 'error'" size="small">
            {{ metrics.activeTrend >= 0 ? '+' : '' }}{{ metrics.activeTrend }}%
          </n-tag>
          <span class="trend-text">较上期</span>
        </div>
      </n-card>
      
      <n-card class="metric-card">
        <n-statistic
          label="客户留存率"
          :value="metrics.retentionRate"
          :precision="2"
        >
          <template #suffix>%</template>
        </n-statistic>
        <div class="metric-trend">
          <n-tag :type="metrics.retentionTrend >= 0 ? 'success' : 'error'" size="small">
            {{ metrics.retentionTrend >= 0 ? '+' : '' }}{{ metrics.retentionTrend }}%
          </n-tag>
          <span class="trend-text">较上期</span>
        </div>
      </n-card>
    </n-grid>

    <!-- 图表区域 -->
    <n-grid :cols="2" :x-gap="16" class="charts-grid">
      <!-- 客户增长趋势 -->
      <n-card title="客户增长趋势" class="chart-card">
        <div class="chart-container" ref="customerGrowthRef"></div>
      </n-card>
      
      <!-- 客户来源分布 -->
      <n-card title="客户来源分布" class="chart-card">
        <div class="chart-container" ref="customerSourceRef"></div>
      </n-card>
    </n-grid>

    <n-grid :cols="2" :x-gap="16" class="charts-grid">
      <!-- 客户价值分布 -->
      <n-card title="客户价值分布" class="chart-card">
        <div class="chart-container" ref="customerValueRef"></div>
      </n-card>
      
      <!-- 客户活跃度分析 -->
      <n-card title="客户活跃度分析" class="chart-card">
        <div class="chart-container" ref="customerActivityRef"></div>
      </n-card>
    </n-grid>

    <!-- 客户地域分布 -->
    <n-card title="客户地域分布" class="chart-card full-width">
      <div class="chart-container large" ref="customerRegionRef"></div>
    </n-card>

    <!-- 客户详细分析表格 -->
    <n-card title="客户分析明细" class="table-card">
      <template #header-extra>
        <n-space>
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索客户名称、手机号"
            clearable
            style="width: 200px"
          >
            <template #prefix>
              <n-icon><search-outline /></n-icon>
            </template>
          </n-input>
          <n-select
            v-model:value="levelFilter"
            placeholder="客户等级"
            clearable
            style="width: 120px"
            :options="levelOptions"
          />
          <n-select
            v-model:value="sourceFilter"
            placeholder="客户来源"
            clearable
            style="width: 120px"
            :options="sourceOptions"
          />
        </n-space>
      </template>
      
      <n-data-table
        ref="tableRef"
        :columns="columns"
        :data="customerData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row: CustomerAnalytics) => row.id"
        remote
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, watch, h, computed } from 'vue'
import { useMessage } from 'naive-ui'
import {
  DownloadOutline,
  RefreshOutline,
  SearchOutline
} from '@vicons/ionicons5'
import * as echarts from 'echarts'
import type { CustomerAnalytics } from '@/types'
import type { DataTableColumns } from 'naive-ui'
import { useCustomerOptionsStore } from '@/stores/customerOptions'
import { CustomerOptionCategory } from '@/constants/customerOptions'

const message = useMessage()
const customerOptionsStore = useCustomerOptionsStore()

// 响应式数据
const loading = ref(false)
const dateRange = ref<[number, number] | null>(null)
const searchKeyword = ref('')
const levelFilter = ref<string | null>(null)
const sourceFilter = ref<string | null>(null)
const customerData = ref<CustomerAnalytics[]>([])

// 图表引用
const customerGrowthRef = ref<HTMLElement>()
const customerSourceRef = ref<HTMLElement>()
const customerValueRef = ref<HTMLElement>()
const customerActivityRef = ref<HTMLElement>()
const customerRegionRef = ref<HTMLElement>()

// 核心指标
const metrics = reactive({
  totalCustomers: 0,
  customersTrend: 0,
  newCustomers: 0,
  newTrend: 0,
  activeCustomers: 0,
  activeTrend: 0,
  retentionRate: 0,
  retentionTrend: 0
})

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true,
  prefix: ({ itemCount }: { itemCount: number }) => `共 ${itemCount} 条`
})

// 筛选选项
const levelOptions = computed(() => 
  customerOptionsStore.toSelectOptions(CustomerOptionCategory.LEVEL)
)

const sourceOptions = computed(() => 
  customerOptionsStore.toSelectOptions(CustomerOptionCategory.SOURCE)
)

// 表格列配置
const columns: DataTableColumns<CustomerAnalytics> = [
  {
    title: '客户名称',
    key: 'name',
    width: 120,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '手机号',
    key: 'phone',
    width: 130
  },
  {
    title: '客户等级',
    key: 'level',
    width: 100,
    render: (row) => {
      const level = levelOptions.find(item => item.value === row.level)
      const levelMap: Record<string, string> = {
        normal: 'default',
        important: 'warning',
        vip: 'success',
        strategic: 'error'
      }
      return h(
        'n-tag',
        { type: levelMap[row.level] || 'default', size: 'small' },
        { default: () => level?.label || row.level }
      )
    }
  },
  {
    title: '客户来源',
    key: 'source',
    width: 100,
    render: (row) => {
      const source = sourceOptions.find(item => item.value === row.source)
      return source?.label || row.source
    }
  },
  {
    title: '累计消费',
    key: 'totalSpent',
    width: 120,
    render: (row) => `¥${row.totalSpent.toLocaleString()}`
  },
  {
    title: '订单数量',
    key: 'orderCount',
    width: 100
  },
  {
    title: '最后消费',
    key: 'lastOrderDate',
    width: 120,
    render: (row) => row.lastOrderDate ? new Date(row.lastOrderDate).toLocaleDateString() : '-'
  },
  {
    title: '活跃度',
    key: 'activityScore',
    width: 100,
    render: (row) => {
      const score = row.activityScore || 0
      let type = 'default'
      if (score >= 80) type = 'success'
      else if (score >= 60) type = 'warning'
      else if (score >= 40) type = 'info'
      else type = 'error'
      
      return h(
        'n-progress',
        {
          type: 'line',
          percentage: score,
          status: type,
          showIndicator: false,
          height: 8
        }
      )
    }
  },
  {
    title: '注册时间',
    key: 'registeredAt',
    width: 150,
    render: (row) => new Date(row.registeredAt).toLocaleDateString()
  }
]

// 图表实例
let customerGrowthChart: echarts.ECharts | null = null
let customerSourceChart: echarts.ECharts | null = null
let customerValueChart: echarts.ECharts | null = null
let customerActivityChart: echarts.ECharts | null = null
let customerRegionChart: echarts.ECharts | null = null

// 方法
const handleDateChange = () => {
  fetchData()
}

const handleExport = () => {
  // TODO: 实现导出功能
  message.info('导出功能开发中')
}

const handleRefresh = () => {
  fetchData()
}

const handlePageChange = (page: number) => {
  pagination.page = page
  fetchCustomerData()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  fetchCustomerData()
}

// 初始化客户增长趋势图
const initCustomerGrowthChart = () => {
  if (!customerGrowthRef.value) return
  
  customerGrowthChart = echarts.init(customerGrowthRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['新增客户', '累计客户']
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    },
    yAxis: [
      {
        type: 'value',
        name: '新增客户',
        position: 'left'
      },
      {
        type: 'value',
        name: '累计客户',
        position: 'right'
      }
    ],
    series: [
      {
        name: '新增客户',
        type: 'bar',
        yAxisIndex: 0,
        data: [45, 52, 38, 48, 35, 78, 72, 65, 68, 82, 95, 105],
        itemStyle: {
          color: '#5470c6'
        }
      },
      {
        name: '累计客户',
        type: 'line',
        yAxisIndex: 1,
        data: [45, 97, 135, 183, 218, 296, 368, 433, 501, 583, 678, 783],
        smooth: true,
        itemStyle: {
          color: '#91cc75'
        }
      }
    ]
  }
  
  customerGrowthChart.setOption(option)
}

// 初始化客户来源分布图
const initCustomerSourceChart = () => {
  if (!customerSourceRef.value) return
  
  customerSourceChart = echarts.init(customerSourceRef.value)
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '客户来源',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 335, name: '线上推广' },
          { value: 310, name: '朋友推荐' },
          { value: 234, name: '电话营销' },
          { value: 135, name: '展会活动' },
          { value: 148, name: '门店咨询' },
          { value: 89, name: '其他' }
        ]
      }
    ]
  }
  
  customerSourceChart.setOption(option)
}

// 初始化客户价值分布图
const initCustomerValueChart = () => {
  if (!customerValueRef.value) return
  
  customerValueChart = echarts.init(customerValueRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['0-1000', '1000-5000', '5000-10000', '10000-50000', '50000+']
    },
    yAxis: {
      type: 'value',
      name: '客户数量'
    },
    series: [
      {
        name: '客户数量',
        type: 'bar',
        data: [120, 200, 150, 80, 30],
        itemStyle: {
          color: '#fac858'
        }
      }
    ]
  }
  
  customerValueChart.setOption(option)
}

// 初始化客户活跃度分析图
const initCustomerActivityChart = () => {
  if (!customerActivityRef.value) return
  
  customerActivityChart = echarts.init(customerActivityRef.value)
  
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      top: '5%',
      left: 'center'
    },
    series: [
      {
        name: '客户活跃度',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '40',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 180, name: '高活跃' },
          { value: 280, name: '中活跃' },
          { value: 200, name: '低活跃' },
          { value: 120, name: '沉睡' }
        ]
      }
    ]
  }
  
  customerActivityChart.setOption(option)
}

// 初始化客户地域分布图
const initCustomerRegionChart = () => {
  if (!customerRegionRef.value) return
  
  customerRegionChart = echarts.init(customerRegionRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: ['其他', '重庆', '天津', '成都', '深圳', '杭州', '广州', '上海', '北京']
    },
    series: [
      {
        name: '客户数量',
        type: 'bar',
        data: [45, 68, 72, 85, 95, 108, 125, 158, 180],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }
    ]
  }
  
  customerRegionChart.setOption(option)
}

// 获取核心指标数据
const fetchMetrics = async () => {
  try {
    // TODO: 调用实际API
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    Object.assign(metrics, {
      totalCustomers: 1856,
      customersTrend: 8.5,
      newCustomers: 156,
      newTrend: 12.3,
      activeCustomers: 1245,
      activeTrend: 5.8,
      retentionRate: 78.5,
      retentionTrend: 2.1
    })
  } catch (error) {
    message.error('获取指标数据失败')
  }
}

// 获取客户分析数据
const fetchCustomerData = async () => {
  try {
    loading.value = true
    
    // TODO: 调用实际API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    const mockData: CustomerAnalytics[] = [
      {
        id: 1,
        name: '张三',
        phone: '13800138001',
        level: 'vip',
        source: 'online',
        totalSpent: 25800,
        orderCount: 15,
        lastOrderDate: '2024-02-28T10:30:00Z',
        activityScore: 85,
        registeredAt: '2023-06-15T09:00:00Z'
      },
      {
        id: 2,
        name: '李四',
        phone: '13800138002',
        level: 'important',
        source: 'referral',
        totalSpent: 12500,
        orderCount: 8,
        lastOrderDate: '2024-03-01T14:20:00Z',
        activityScore: 72,
        registeredAt: '2023-08-20T11:30:00Z'
      }
    ]
    
    customerData.value = mockData
    pagination.itemCount = mockData.length
  } catch (error) {
    message.error('获取客户数据失败')
  } finally {
    loading.value = false
  }
}

// 获取所有数据
const fetchData = async () => {
  await Promise.all([
    fetchMetrics(),
    fetchCustomerData()
  ])
}

// 初始化图表
const initCharts = async () => {
  await nextTick()
  initCustomerGrowthChart()
  initCustomerSourceChart()
  initCustomerValueChart()
  initCustomerActivityChart()
  initCustomerRegionChart()
}

// 监听搜索和筛选
watch([searchKeyword, levelFilter, sourceFilter], () => {
  pagination.page = 1
  fetchCustomerData()
})

// 初始化
onMounted(async () => {
  await customerOptionsStore.loadAllCustomerOptions()
  await fetchData()
  await initCharts()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    customerGrowthChart?.resize()
    customerSourceChart?.resize()
    customerValueChart?.resize()
    customerActivityChart?.resize()
    customerRegionChart?.resize()
  })
})
</script>

<style scoped>
.analytics-customer {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--n-text-color);
}

.page-description {
  margin: 4px 0 0 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.metrics-grid {
  margin-bottom: 20px;
}

.metric-card {
  text-align: center;
  position: relative;
}

.metric-trend {
  margin-top: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.trend-text {
  font-size: 12px;
  color: var(--n-text-color-3);
}

.charts-grid {
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
}

.chart-card.full-width {
  margin-bottom: 20px;
}

.chart-container {
  width: 100%;
  height: 320px;
}

.chart-container.large {
  height: 400px;
}

.table-card {
  margin-bottom: 20px;
}
</style>