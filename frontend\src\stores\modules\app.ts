import { defineStore } from 'pinia'
import { ref, h } from 'vue'
import { NIcon } from 'naive-ui'
import {
  SpeedometerOutline,
  PeopleOutline,
  ClipboardOutline,
  VideocamOutline,
  MegaphoneOutline,
  AnalyticsOutline,
  PersonOutline,
  SettingsOutline,
  EyeOutline,
  LogoWechat,
  CogOutline,
  LibraryOutline
} from '@vicons/ionicons5'
import type { MenuOption } from '@/types/menu'
import type { RouteLocationNormalized } from 'vue-router'

// 预创建菜单图标组件，避免在menuOptions中重复创建
const createMenuIcon = (icon: any) => () => h(NIcon, null, { default: () => h(icon) })

const DashboardIcon = createMenuIcon(SpeedometerOutline)
const CustomerIcon = createMenuIcon(PeopleOutline)
const _FollowIcon = createMenuIcon(ClipboardOutline)
const MeetingIcon = createMenuIcon(VideocamOutline)
const MarketingIcon = createMenuIcon(MegaphoneOutline)
const _TrackingIcon = createMenuIcon(EyeOutline)
const WechatIcon = createMenuIcon(LogoWechat)
const AnalyticsIcon = createMenuIcon(AnalyticsOutline)
const UserIcon = createMenuIcon(PersonOutline)
const ContentIcon = createMenuIcon(LibraryOutline)
const CogIcon = createMenuIcon(CogOutline)
const SettingsIcon = createMenuIcon(SettingsOutline)

export const useAppStore = defineStore('app', () => {
  // 侧边栏状态
  const sidebarCollapsed = ref(false)
  
  // 主题设置
  const isDarkMode = ref(false)
  
  // 页面加载状态
  const pageLoading = ref(false)
  
  // 面包屑导航
  const breadcrumbs = ref<Array<{ label: string; path?: string }>>([])
  
  // 当前激活的菜单项
  const activeMenuKey = ref<string>('')
  
  // 菜单配置
  const menuOptions = ref<MenuOption[]>([
    {
      label: '仪表板',
      key: 'dashboard',
      icon: DashboardIcon
    },
    {
      label: '客户管理',
      key: 'customer',
      icon: CustomerIcon,
      children: [
        {
          label: '客户列表',
          key: 'customer-list'
        },
        {
          label: '客户公海',
          key: 'customer-pool'
        },
        {
          label: '跟进记录',
          key: 'follow-records'
        },
        {
          label: '待办事项',
          key: 'follow-todos'
        }
      ]
    },
    {
      label: '会议管理',
      key: 'meeting',
      icon: MeetingIcon
    },
    {
      label: '营销活动',
      key: 'marketing',
      icon: MarketingIcon,
      children: [
        {
          label: '活动列表',
          key: 'marketing-list'
        },
        {
          label: '客户追踪',
          key: 'tracking'
        }
      ]
    },
    {
      label: '微信管理',
      key: 'wechat',
      icon: WechatIcon,
      children: [
        {
          label: '微信客户',
          key: 'wechat-customers'
        },
        {
          label: '客户分析',
          key: 'wechat-customer-analysis'
        },
        {
          label: '分享记录',
          key: 'wechat-shares'
        },
        {
          label: '群组管理',
          key: 'wechat-groups'
        },
        {
          label: '消息管理',
          key: 'wechat-messages'
        }
      ]
    },
    {
      label: '数据分析',
      key: 'analytics',
      icon: AnalyticsIcon,
      children: [
        {
          label: '数据分析概览',
          key: 'analytics-overview'
        },
        {
          label: '客户价值分析',
          key: 'analytics-customer-value'
        },
        {
          label: '销售漏斗分析',
          key: 'analytics-sales-funnel'
        },
        {
          label: '转化率统计',
          key: 'analytics-conversion'
        },
        {
          label: '业绩分析仪表板',
          key: 'analytics-performance'
        }
      ]
    },
    {
      label: '售后管理',
      key: 'afterservice',
      icon: SettingsIcon,
      children: [
        {
          label: '工单列表',
          key: 'afterservice-tickets'
        },
        {
          label: '工单处理',
          key: 'afterservice-process'
        },
        {
          label: '工单提醒',
          key: 'afterservice-reminders'
        },
        {
          label: '工单时间线',
          key: 'afterservice-timeline'
        }
      ]
    },
    {
      label: '工地管理',
      key: 'sites',
      icon: SettingsIcon
    },
    {
      label: '动态内容',
      key: 'content',
      icon: ContentIcon,
      children: [
        {
          label: '公司动态',
          key: 'content-company-news'
        },
        {
          label: '素材动态',
          key: 'content-material-library'
        },
        {
          label: '案例展示',
          key: 'content-case-showcase'
        }
      ]
    },
    {
      label: '有优合伙人',
      key: 'partners',
      icon: UserIcon,
      children: [
        {
          label: '人员列表',
          key: 'partners-list'
        },
        {
          label: '数据分析',
          key: 'partners-analytics'
        },
        {
          label: '积分商城',
          key: 'partners-mall'
        }
      ]
    },
    {
      label: '系统设置',
      key: 'settings',
      icon: SettingsIcon,
      children: [
        {
          label: '基础设置',
          key: 'settings-basic'
        },
        {
          label: '客户设置',
          key: 'settings-customer'
        },
        {
          label: '组织架构',
          key: 'settings-organization'
        },
        {
          label: '选项管理',
          key: 'settings-options',
          meta: {
            permissions: ['settings:options']
          }
        }
      ]
    }
  ])
  
  // 切换侧边栏
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }
  
  // 切换主题
  const toggleTheme = () => {
    isDarkMode.value = !isDarkMode.value
    // 保存到本地存储
    localStorage.setItem('theme', isDarkMode.value ? 'dark' : 'light')
  }
  
  // 设置页面加载状态
  const setPageLoading = (loading: boolean) => {
    pageLoading.value = loading
  }
  
  // 更新面包屑
  const updateBreadcrumbs = (route: RouteLocationNormalized) => {
    const crumbs: Array<{ label: string; path?: string }> = []
    
    // 根据路由生成面包屑
    if (route.matched.length > 0) {
      route.matched.forEach((match, index) => {
        if (match.meta?.title) {
          crumbs.push({
            label: match.meta.title as string,
            path: index === route.matched.length - 1 ? undefined : match.path
          })
        }
      })
    }
    
    breadcrumbs.value = crumbs
  }
  
  // 设置激活的菜单项
  const setActiveMenuKey = (key: string) => {
    activeMenuKey.value = key
  }
  
  // 初始化应用设置
  const initApp = () => {
    // 从本地存储恢复主题设置
    const savedTheme = localStorage.getItem('theme')
    if (savedTheme) {
      isDarkMode.value = savedTheme === 'dark'
    }
    
    // 从本地存储恢复侧边栏状态
    const savedSidebarState = localStorage.getItem('sidebarCollapsed')
    if (savedSidebarState) {
      sidebarCollapsed.value = JSON.parse(savedSidebarState)
    }
  }
  
  // 监听侧边栏状态变化并保存
  const saveSidebarState = () => {
    localStorage.setItem('sidebarCollapsed', JSON.stringify(sidebarCollapsed.value))
  }
  
  return {
    // 状态
    sidebarCollapsed,
    isDarkMode,
    pageLoading,
    breadcrumbs,
    activeMenuKey,
    menuOptions,
    
    // 方法
    toggleSidebar,
    toggleTheme,
    setPageLoading,
    updateBreadcrumbs,
    setActiveMenuKey,
    initApp,
    saveSidebarState
  }
})