import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/stores'

// 导入页面组件
import Layout from '@/components/layout/AppLayout.vue'
import Login from '@/views/auth/Login.vue'
import Dashboard from '@/views/dashboard/Dashboard.vue'
// 客户管理模块 - 使用静态导入提高响应速度
import CustomerList from '@/views/customer/CustomerList.vue'
import CustomerDetail from '@/views/customer/CustomerDetail.vue'
import CustomerPool from '@/views/customer/CustomerPool.vue'
import FollowRecords from '@/views/follow/FollowRecords.vue'
import FollowTodos from '@/views/follow/FollowTodos.vue'
import MeetingList from '@/views/meeting/MeetingList.vue'
import MarketingList from '@/views/marketing/MarketingList.vue'
import CampaignProgress from '@/views/marketing/CampaignProgress.vue'
import CampaignAnalytics from '@/views/marketing/CampaignAnalytics.vue'
import TrackingList from '@/views/tracking/TrackingList.vue'
import AnalyticsSales from '@/views/analytics/AnalyticsSales.vue'
// import UserList from '@/views/user/UserList.vue'
// import Settings from '@/views/settings/Settings.vue'
import BasicSettings from '@/views/settings/BasicSettings.vue'
import SecuritySettings from '@/views/settings/SecuritySettings.vue'
import StorageSettings from '@/views/settings/StorageSettings.vue'

import WechatCustomerList from '@/views/wechat/WechatCustomerList.vue'
import ShareRecordList from '@/views/wechat/ShareRecordList.vue'
import WechatGroupList from '@/views/wechat/WechatGroupList.vue'
import WechatMessageList from '@/views/wechat/WechatMessageList.vue'
import DataAnalytics from '@/views/analytics/DataAnalytics.vue'
import CustomerValueAnalysis from '@/views/analytics/CustomerValueAnalysis.vue'
import SalesFunnelAnalysis from '@/views/analytics/SalesFunnelAnalysis.vue'
import ConversionAnalysis from '@/views/analytics/ConversionAnalysis.vue'
import PerformanceDashboard from '@/views/analytics/PerformanceDashboard.vue'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/',
    component: Layout,
    redirect: (_to) => {
      // 根据认证状态决定重定向目标
      const authStore = useAuthStore()
      return authStore.isAuthenticated ? '/dashboard' : '/login'
    },
    meta: {
      requiresAuth: false
    },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: Dashboard,
        meta: {
          title: '仪表板',
          icon: 'dashboard-outline',
          requiresAuth: true
        }
      },
      {
        path: 'customer',
        name: 'Customer',
        redirect: '/customer/list',
        meta: {
          title: '客户管理',
          icon: 'people-outline',
          requiresAuth: true
        },
        children: [
          {
            path: 'management',
            name: 'CustomerManagementRedirect',
            redirect: '/settings/options?tab=customers',
            meta: {
              hideInMenu: true,
              requiresAuth: true,
              permissions: ['customer:read']
            }
          },
          {
            path: 'list',
            name: 'CustomerList',
            component: CustomerList,
            meta: {
              title: '客户列表',
              requiresAuth: true,
              permissions: ['customer:read']
            }
          },
          {
            path: 'detail/:id',
            name: 'CustomerDetail',
            component: CustomerDetail,
            meta: {
              title: '客户详情',
              requiresAuth: true,
              hideInMenu: true,
              permissions: ['customer:read']
            }
          },
          {
            path: 'pool',
            name: 'CustomerPool',
            component: CustomerPool,
            meta: {
              title: '客户公海',
              requiresAuth: true,
              permissions: ['customer:pool']
            }
          },
          {
            path: '/follow/records',
            name: 'FollowRecords',
            component: FollowRecords,
            meta: {
              title: '跟进记录',
              requiresAuth: true,
              permissions: ['follow:read']
            }
          },
          {
            path: '/follow/todos',
            name: 'FollowTodos',
            component: FollowTodos,
            meta: {
              title: '待办事项',
              requiresAuth: true,
              permissions: ['follow:read']
            }
          },
          {
            path: 'follow/:customerId',
            name: 'CustomerFollowDetail',
            component: () => import('@/views/customer/CustomerFollowDetail.vue'),
            meta: {
              title: '客户跟进详情',
              requiresAuth: true,
              hideInMenu: true,
              permissions: ['customer:read', 'follow:read']
            }
          }
        ]
      },

      {
        path: 'meeting',
        name: 'Meeting',
        component: MeetingList,
        meta: {
          title: '会议管理',
          icon: 'videocam-outline',
          requiresAuth: true,
          permissions: ['meeting:read']
        }
      },
      {
        path: 'marketing',
        name: 'Marketing',
        redirect: '/marketing/list',
        meta: {
          title: '营销活动',
          icon: 'megaphone-outline',
          requiresAuth: true,
          permissions: ['marketing:read']
        },
        children: [
          {
            path: 'list',
            name: 'MarketingList',
            component: MarketingList,
            meta: {
              title: '活动列表',
              requiresAuth: true,
              permissions: ['marketing:read']
            }
          },
          {
            path: 'create',
            name: 'CreateCampaign',
            component: () => import('@/views/marketing/CreateCampaign.vue'),
            meta: {
              title: '创建活动',
              requiresAuth: true,
              hideInMenu: true,
              permissions: ['marketing:create']
            }
          },
          {
            path: 'edit/:id',
            name: 'EditCampaign',
            component: () => import('@/views/marketing/CreateCampaign.vue'),
            meta: {
              title: '编辑活动',
              requiresAuth: true,
              hideInMenu: true,
              permissions: ['marketing:update']
            }
          },
          {
            path: 'progress',
            name: 'CampaignProgress',
            component: CampaignProgress,
            meta: {
              title: '进度跟进',
              requiresAuth: true,
              permissions: ['marketing:progress']
            }
          },
          {
            path: 'analytics',
            name: 'CampaignAnalytics',
            component: CampaignAnalytics,
            meta: {
              title: '数据分析',
              requiresAuth: true,
              permissions: ['marketing:analytics']
            }
          },
          {
            path: 'tracking',
            name: 'MarketingTracking',
            component: TrackingList,
            meta: {
              title: '客户追踪',
              requiresAuth: true,
              permissions: ['tracking:read']
            }
          }
        ]
      },

      {
        path: 'wechat',
        name: 'Wechat',
        redirect: '/wechat/customers',
        meta: {
          title: '微信管理',
          icon: 'logo-wechat',
          requiresAuth: true
        },
        children: [
          {
            path: 'customers',
            name: 'WechatCustomers',
            component: WechatCustomerList,
            meta: {
              title: '微信客户',
              requiresAuth: true,
              permissions: ['wechat:read']
            }
          },
          {
            path: 'customer-analysis',
            name: 'WechatCustomerAnalysis',
            component: () => import('@/views/wechat/CustomerAnalysis.vue'),
            meta: {
              title: '客户分析',
              requiresAuth: true,
              permissions: ['wechat:analytics']
            }
          },
          {
             path: 'shares',
             name: 'ShareRecords',
             component: ShareRecordList,
             meta: {
               title: '分享记录',
               requiresAuth: true,
               permissions: ['wechat:read']
             }
           },
           {
             path: 'groups',
             name: 'WechatGroups',
             component: WechatGroupList,
             meta: {
               title: '群组管理',
               requiresAuth: true,
               permissions: ['wechat:read']
             }
           },
           {
             path: 'messages',
             name: 'WechatMessages',
             component: WechatMessageList,
             meta: {
               title: '消息管理',
               requiresAuth: true,
               permissions: ['wechat:read']
             }
           }
        ]
      },
      {
        path: 'analytics',
        name: 'Analytics',
        redirect: '/analytics/overview',
        meta: {
          title: '数据分析',
          icon: 'analytics-outline',
          requiresAuth: true,
          permissions: ['analytics:read']
        },
        children: [
          {
            path: 'overview',
            name: 'DataAnalytics',
            component: DataAnalytics,
            meta: {
              title: '数据分析概览',
              requiresAuth: true,
              permissions: ['analytics:read']
            }
          },
          {
            path: 'sales',
            name: 'AnalyticsSales',
            component: AnalyticsSales,
            meta: {
              title: '销售统计',
              requiresAuth: true,
              permissions: ['analytics:read']
            }
          },

          {
            path: 'customer-value',
            name: 'CustomerValueAnalysis',
            component: CustomerValueAnalysis,
            meta: {
              title: '客户价值分析',
              requiresAuth: true,
              permissions: ['analytics:read']
            }
          },
          {
            path: 'sales-funnel',
            name: 'SalesFunnelAnalysis',
            component: SalesFunnelAnalysis,
            meta: {
              title: '销售漏斗分析',
              requiresAuth: true,
              permissions: ['analytics:read']
            }
          },
          {
            path: 'conversion',
            name: 'ConversionAnalysis',
            component: ConversionAnalysis,
            meta: {
              title: '转化率统计',
              requiresAuth: true,
              permissions: ['analytics:read']
            }
          },
          {
            path: 'performance',
            name: 'PerformanceDashboard',
            component: PerformanceDashboard,
            meta: {
              title: '业绩分析仪表板',
              requiresAuth: true,
              permissions: ['analytics:read']
            }
          }
        ]
      },
      {
        path: 'afterservice',
        name: 'AfterService',
        redirect: '/afterservice/tickets',
        meta: {
          title: '售后管理',
          icon: 'headset-outline',
          requiresAuth: true,
          permissions: ['afterservice:read']
        },
        children: [
          {
            path: 'tickets',
            name: 'TicketList',
            component: () => import('@/views/afterservice/TicketList.vue'),
            meta: {
              title: '工单列表',
              requiresAuth: true,
              permissions: ['afterservice:read']
            }
          },
          {
            path: 'process/:id?',
            name: 'TicketProcess',
            component: () => import('@/views/afterservice/TicketProcess.vue'),
            meta: {
              title: '工单处理',
              requiresAuth: true,
              permissions: ['afterservice:write']
            }
          },
          {
            path: 'reminders',
            name: 'TicketReminders',
            component: () => import('@/views/afterservice/TicketReminders.vue'),
            meta: {
              title: '工单提醒',
              requiresAuth: true,
              permissions: ['afterservice:read']
            }
          },
          {
            path: 'timeline',
            name: 'TicketTimeline',
            component: () => import('@/views/afterservice/TicketTimeline.vue'),
            meta: {
              title: '工单时间线',
              requiresAuth: true,
              permissions: ['afterservice:read']
            }
          }
        ]
      },
      {
        path: 'sites',
        name: 'SiteManagement',
        redirect: '/sites/list',
        meta: {
          title: '工地管理',
          icon: 'hammer-outline',
          requiresAuth: true,
          permissions: ['sites:read']
        },
        children: [
          {
            path: 'list',
            name: 'SiteList',
            component: () => import('@/views/sites/SiteList.vue'),
            meta: {
              title: '工地列表',
              requiresAuth: true,
              permissions: ['sites:read']
            }
          },
          {
            path: 'detail/:id',
            name: 'SiteDetail',
            component: () => import('@/views/sites/SiteDetail.vue'),
            meta: {
              title: '工地详情',
              requiresAuth: true,
              hideInMenu: true,
              permissions: ['sites:read']
            }
          }
        ]
      },
      {
        path: 'partners',
        name: 'Partners',
        redirect: '/partners/list',
        meta: {
          title: '有优合伙人',
          icon: 'people-circle-outline',
          requiresAuth: true,
          permissions: ['partners:read']
        },
        children: [
          {
            path: 'list',
            name: 'PartnersList',
            component: () => import('@/views/partners/PartnersList.vue'),
            meta: {
              title: '人员列表',
              requiresAuth: true,
              permissions: ['partners:read']
            }
          },
          {
            path: 'analytics',
            name: 'PartnersAnalytics',
            component: () => import('@/views/partners/PartnersAnalytics.vue'),
            meta: {
              title: '数据分析',
              requiresAuth: true,
              permissions: ['partners:analytics']
            }
          },
          {
            path: 'mall',
            name: 'PartnersMall',
            component: () => import('@/views/partners/PartnersMall.vue'),
            meta: {
              title: '积分商城',
              requiresAuth: true,
              permissions: ['partners:mall']
            }
          }
        ]
      },
      {
        path: 'settings',
        name: 'Settings',
        redirect: '/settings/basic',
        meta: {
          title: '系统设置',
          icon: 'settings-outline',
          requiresAuth: true,
          permissions: ['settings:read']
        },
        children: [
          {
            path: 'basic',
            name: 'BasicSettings',
            component: BasicSettings,
            meta: {
              title: '基础设置',
              requiresAuth: true,
              permissions: ['settings:read']
            }
          },
          {
            path: 'options',
            name: 'OptionsManagement',
            redirect: '/settings/basic?tab=options',
            meta: {
              title: '选项管理',
              requiresAuth: true,
              permissions: ['settings:read'],
              hideInMenu: true
            }
          },
          {
            path: 'organization',
            name: 'OrganizationSettings',
            component: () => import('@/views/settings/OrganizationSettings.vue'),
            meta: {
              title: '组织架构',
              requiresAuth: true,
              permissions: ['settings:read']
            }
          },
          {
            path: 'security',
            name: 'SecuritySettings',
            component: SecuritySettings,
            meta: {
              title: '安全设置',
              requiresAuth: true,
              permissions: ['settings:security']
            }
          },
          {
            path: 'storage',
            name: 'StorageSettings',
            component: StorageSettings,
            meta: {
              title: '存储设置',
              requiresAuth: true,
              permissions: ['settings:storage']
            }
          },


        ]
      },
      {
        path: 'content',
        name: 'Content',
        redirect: '/content/company-news',
        meta: {
          title: '动态内容',
          icon: 'library-outline',
          requiresAuth: true
        },
        children: [
          {
            path: 'company-news',
            name: 'CompanyNews',
            component: () => import('@/views/content/CompanyNews.vue'),
            meta: {
              title: '公司动态',
              icon: 'newspaper-outline',
              requiresAuth: true
            }
          },
          {
            path: 'material-library',
            name: 'MaterialLibrary',
            component: () => import('@/views/content/MaterialLibrary.vue'),
            meta: {
              title: '素材动态',
              icon: 'images-outline',
              requiresAuth: true
            }
          },
          {
            path: 'case-showcase',
            name: 'CaseShowcase',
            component: () => import('@/views/content/CaseShowcase.vue'),
            meta: {
              title: '案例展示',
              icon: 'home-outline',
              requiresAuth: true
            }
          }
        ]
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/dashboard'
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // 设置页面加载状态
  if (typeof window !== 'undefined') {
    const appStore = (await import('@/stores')).useAppStore()
    appStore.setPageLoading(true)
  }
  
  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      // 未登录，跳转到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
    
    // 检查权限
    if (to.meta.permissions && Array.isArray(to.meta.permissions)) {
      let requiredPermissions = [...to.meta.permissions]
      

      
      const hasPermission = requiredPermissions.some(permission => 
        authStore.hasPermission(permission as string)
      )
      
      if (!hasPermission) {
        console.warn('用户权限不足，重定向到仪表板')
        // 使用replace避免在历史记录中留下无权限页面
        next({ path: '/dashboard', replace: true })
        return
      }
    }
  } else if (to.path === '/login' && authStore.isAuthenticated) {
    // 已登录用户访问登录页，重定向到首页
    next('/dashboard')
    return
  }
  
  next()
})

// 路由后置守卫
router.afterEach(async (to) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - YYSH客户管理系统`
  } else {
    document.title = 'YYSH客户管理系统'
  }
  
  // 清除页面加载状态
  if (typeof window !== 'undefined') {
    const appStore = (await import('@/stores')).useAppStore()
    // 延迟清除加载状态，确保页面渲染完成
    setTimeout(() => {
      appStore.setPageLoading(false)
    }, 100)
  }
})

export default router
