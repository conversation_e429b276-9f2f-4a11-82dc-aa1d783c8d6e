/**
 * 客户参数管理API封装
 * @description 封装客户参数管理相关的API接口调用
 * <AUTHOR> Document
 * @date 2024-01-20
 */

import { http as request } from './http'
import type {
  OptionCategory,
  OptionItem,
  CreateOptionCategoryRequest,
  UpdateOptionCategoryRequest,
  CreateOptionItemRequest,
  UpdateOptionItemRequest,
  GetOptionCategoriesParams,
  GetOptionItemsParams,
  GetOptionCategoriesResponse,
  GetOptionItemsResponse,
  CreateOptionCategoryResponse,
  CreateOptionItemResponse,
  UpdateOptionCategoryResponse,
  UpdateOptionItemResponse,
  DeleteResponse,
  ApiResponse,
  PaginatedResponse
} from '@/types/customerOptions'
import { API_ENDPOINTS, CustomerOptionCategory } from '@/constants/customerOptions'
export { CustomerOptionCategory } from '@/constants/customerOptions'

// ==================== 选项分类API ====================

/**
 * 获取选项分类列表
 * @param params 查询参数
 * @returns 分类列表响应
 */
export const getOptionCategories = async (
  params?: GetOptionCategoriesParams
): Promise<GetOptionCategoriesResponse> => {
  try {
    const response = await request.get<PaginatedResponse<OptionCategory>>(
      API_ENDPOINTS.MANAGEMENT.CATEGORIES.LIST,
      { params }
    )
    return {
      success: true,
      code: 200,
      data: response.data,
      message: '获取选项数据列表成功'
    }
  } catch (error: any) {
    console.error('获取选项分类列表失败:', error)
    return {
      success: false,
      code: 500,
      data: { data: [], total: 0, page: 1, pageSize: 10 } as any,
      message: error.message || '获取选项分类列表失败'
    }
  }
}

/**
 * 创建选项分类
 * @param data 创建参数
 * @returns 创建结果
 */
export const createOptionCategory = async (
  data: CreateOptionCategoryRequest
): Promise<CreateOptionCategoryResponse> => {
  try {
    const response = await request.post<OptionCategory>(
      API_ENDPOINTS.MANAGEMENT.CATEGORIES.CREATE,
      data
    )
    return {
      success: true,
      code: 201,
      data: response.data,
      message: '创建选项分类成功'
    }
  } catch (error: any) {
    console.error('创建选项分类失败:', error)
    return {
      success: false,
      code: 500,
      data: null as any,
      message: error.message || '创建选项分类失败'
    }
  }
}

/**
 * 更新选项分类
 * @param id 分类ID
 * @param data 更新参数
 * @returns 更新结果
 */
export const updateOptionCategory = async (
  id: string,
  data: UpdateOptionCategoryRequest
): Promise<UpdateOptionCategoryResponse> => {
  try {
    const response = await request.put<OptionCategory>(
      API_ENDPOINTS.MANAGEMENT.CATEGORIES.UPDATE(id),
      data
    )
    return {
      success: true,
      code: 200,
      data: response.data,
      message: '更新选项分类成功'
    }
  } catch (error: any) {
    console.error('更新选项分类失败:', error)
    return {
      success: false,
      code: 500,
      data: null as any,
      message: error.message || '更新选项分类失败'
    }
  }
}

/**
 * 删除选项分类
 * @param id 分类ID
 * @returns 删除结果
 */
export const deleteOptionCategory = async (
  id: string
): Promise<DeleteResponse> => {
  try {
    await request.delete(API_ENDPOINTS.MANAGEMENT.CATEGORIES.DELETE(id))
    return {
      success: true,
      code: 200,
      data: { deleted: true },
      message: '删除选项分类成功'
    }
  } catch (error: any) {
    console.error('删除选项分类失败:', error)
    return {
      success: false,
      code: 500,
      data: { deleted: false },
      message: error.message || '删除选项分类失败'
    }
  }
}

// ==================== 选项数据API ====================

/**
 * 获取选项数据列表
 * @param params 查询参数
 * @returns 选项数据列表响应
 */
export const getOptionItems = async (
  params?: GetOptionItemsParams
): Promise<GetOptionItemsResponse> => {
  try {
    const response = await request.get<PaginatedResponse<OptionItem>>(
      API_ENDPOINTS.MANAGEMENT.ITEMS.LIST,
      { params }
    )
    return {
      success: true,
      code: 200,
      data: response.data,
      message: '获取选项数据列表成功'
    }
  } catch (error: any) {
    console.error('获取选项数据列表失败:', error)
    return {
      success: false,
      code: 500,
      data: { data: [], total: 0, page: 1, pageSize: 10 } as any,
      message: error.message || '获取选项数据列表失败'
    }
  }
}

/**
 * 根据分类代码获取选项数据
 * @param categoryCode 分类代码
 * @param params 其他查询参数
 * @returns 选项数据列表响应
 */
export const getOptionItemsByCategory = async (
  categoryCode: string,
  params?: Omit<GetOptionItemsParams, 'category_code'>
): Promise<GetOptionItemsResponse> => {
  return getOptionItems({
    ...params,
    category_code: categoryCode
  })
}

/**
 * 创建选项数据
 * @param data 创建参数
 * @returns 创建结果
 */
export const createOptionItem = async (
  data: CreateOptionItemRequest
): Promise<CreateOptionItemResponse> => {
  try {
    const response = await request.post<OptionItem>(
      API_ENDPOINTS.MANAGEMENT.ITEMS.CREATE,
      data
    )
    return {
      success: true,
      code: 201,
      data: response.data,
      message: '创建选项数据成功'
    }
  } catch (error: any) {
    console.error('创建选项数据失败:', error)
    return {
      success: false,
      code: 500,
      data: null as any,
      message: error.message || '创建选项数据失败'
    }
  }
}

/**
 * 更新选项数据
 * @param id 选项ID
 * @param data 更新参数
 * @returns 更新结果
 */
export const updateOptionItem = async (
  id: string,
  data: UpdateOptionItemRequest
): Promise<UpdateOptionItemResponse> => {
  try {
    const response = await request.put<OptionItem>(
      API_ENDPOINTS.MANAGEMENT.ITEMS.UPDATE(id),
      data
    )
    return {
      success: true,
      code: 200,
      data: response.data,
      message: '更新选项数据成功'
    }
  } catch (error: any) {
    console.error('更新选项数据失败:', error)
    return {
      success: false,
      code: 500,
      data: null as any,
      message: error.message || '更新选项数据失败'
    }
  }
}

/**
 * 删除选项数据
 * @param id 选项ID
 * @returns 删除结果
 */
export const deleteOptionItem = async (
  id: string
): Promise<DeleteResponse> => {
  try {
    await request.delete(API_ENDPOINTS.MANAGEMENT.ITEMS.DELETE(id))
    return {
      success: true,
      code: 200,
      data: { deleted: true },
      message: `成功删除选项数据 ${id}`
    }
  } catch (error: any) {
    console.error('删除选项数据失败:', error)
    return {
      success: false,
      code: 500,
      data: { deleted: false },
      message: error.message || '删除选项数据失败'
    }
  }
}

/**
 * 批量删除选项数据
 * @param ids 选项ID数组
 * @returns 删除结果
 */
export const batchDeleteOptionItems = async (
  ids: string[]
): Promise<DeleteResponse> => {
  try {
    await request.post(API_ENDPOINTS.MANAGEMENT.ITEMS.BATCH_DELETE, { ids })
    return {
      success: true,
      code: 200,
      data: { deleted: true },
      message: `成功删除 ${ids.length} 条选项数据`
    }
  } catch (error: any) {
    console.error('批量删除选项数据失败:', error)
    return {
      success: false,
      code: 500,
      data: { deleted: false },
      message: error.message || '批量删除选项数据失败'
    }
  }
}

// ==================== 选项数据获取API（使用/api/options/端点）====================

/**
 * 获取所有选项分类（用于下拉框数据获取）
 * @returns 选项分类列表
 */
export const getOptionsCategories = async (): Promise<ApiResponse<OptionCategory[]>> => {
  try {
    const response = await request.get<OptionCategory[]>(
      API_ENDPOINTS.OPTIONS.CATEGORIES
    )
    return {
      success: true,
      code: 200,
      data: response.data,
      message: '获取选项分类成功'
    }
  } catch (error: any) {
    console.error('获取选项分类失败:', error)
    return {
      success: false,
      code: 500,
      data: [],
      message: error.message || '获取选项分类失败'
    }
  }
}

/**
 * 根据分类名称获取选项数据（用于下拉框数据获取）
 * @param categoryName 分类名称
 * @returns 选项数据列表
 */
export const getOptionsByCategory = async (
  categoryName: string
): Promise<ApiResponse<OptionItem[]>> => {
  try {
    const response = await request.get<OptionItem[]>(
      API_ENDPOINTS.OPTIONS.BY_CATEGORY(categoryName)
    )
    return {
      success: true,
      code: 200,
      data: response.data,
      message: '获取选项数据成功'
    }
  } catch (error: any) {
    console.error('获取选项数据失败:', error)
    return {
      success: false,
      code: 500,
      data: [],
      message: error.message || '获取选项数据失败'
    }
  }
}

/**
 * 获取所有选项数据（用于下拉框数据获取）
 * @returns 所有选项数据
 */
export const getAllOptions = async (): Promise<ApiResponse<OptionItem[]>> => {
  try {
    const response = await request.get<OptionItem[]>(
      API_ENDPOINTS.OPTIONS.ALL
    )
    return {
      success: true,
      code: 200,
      data: response.data,
      message: '获取所有选项数据成功'
    }
  } catch (error: any) {
    console.error('获取所有选项数据失败:', error)
    return {
      success: false,
      code: 500,
      data: [],
      message: error.message || '获取所有选项数据失败'
    }
  }
}

// ==================== 客户参数专用API ====================

/**
 * 获取客户来源选项
 * @param params 查询参数
 * @returns 客户来源列表
 */
export const getCustomerSources = async (
  params?: Omit<GetOptionItemsParams, 'category_code'>
): Promise<GetOptionItemsResponse> => {
  return getOptionItemsByCategory(CustomerOptionCategory.SOURCE, params)
}

/**
 * 获取客户等级选项
 * @param params 查询参数
 * @returns 客户等级列表
 */
export const getCustomerLevels = async (
  params?: Omit<GetOptionItemsParams, 'category_code'>
): Promise<GetOptionItemsResponse> => {
  return getOptionItemsByCategory(CustomerOptionCategory.LEVEL, params)
}

/**
 * 获取客户状态选项
 * @param params 查询参数
 * @returns 客户状态列表
 */
export const getCustomerStatuses = async (
  params?: Omit<GetOptionItemsParams, 'category_code'>
): Promise<GetOptionItemsResponse> => {
  return getOptionItemsByCategory(CustomerOptionCategory.STATUS, params)
}

/**
 * 获取客户标签选项
 * @param params 查询参数
 * @returns 客户标签列表
 */
export const getCustomerTags = async (
  params?: Omit<GetOptionItemsParams, 'category_code'>
): Promise<GetOptionItemsResponse> => {
  return getOptionItemsByCategory(CustomerOptionCategory.TAG, params)
}



// ==================== 批量操作API ====================

/**
 * 批量创建选项数据
 * @param categoryCode 分类代码
 * @param items 选项数据数组
 * @returns 创建结果
 */
export const batchCreateOptionItems = async (
  categoryCode: string,
  items: Omit<CreateOptionItemRequest, 'category_id'>[]
): Promise<ApiResponse<OptionItem[]>> => {
  try {
    // 首先获取分类信息
    const categoriesResponse = await getOptionCategories({
      page: 1,
      pageSize: 1
    })
    
    if (!categoriesResponse.success || !categoriesResponse.data?.items?.length) {
      return {
        success: false,
        code: 404,
        data: [],
        message: `未找到分类代码为 ${categoryCode} 的分类`
      }
    }
    
    // 查找匹配的分类
    const category = categoriesResponse.data?.items?.find((cat: OptionCategory) => cat.code === categoryCode)
    if (!category) {
      return {
        success: false,
        code: 404,
        data: [],
        message: `未找到分类代码为 ${categoryCode} 的分类`
      }
    }
    
    const categoryId = category.id
    
    // 批量创建选项数据
    const results: OptionItem[] = []
    const errors: string[] = []
    
    for (const item of items) {
      const result = await createOptionItem({
        ...item,
        category_id: categoryId
      })
      
      if (result.success && result.data) {
        results.push(result.data)
      } else {
        errors.push(result.message || '创建失败')
      }
    }
    
    if (errors.length > 0) {
      return {
        success: false,
        code: 400,
        data: [],
        message: `部分创建失败: ${errors.join(', ')}`
      }
    }

    return {
      success: true,
      code: 200,
      data: results,
      message: `成功创建 ${results.length} 条选项数据`
    }
  } catch (error: any) {
    console.error('批量创建选项数据失败:', error)
    return {
      success: false,
      code: 500,
      data: [],
      message: error.message || '批量创建选项数据失败'
    }
  }
}

/**
 * 批量更新选项数据状态
 * @param ids 选项ID数组
 * @param isActive 是否启用
 * @returns 更新结果
 */
export const batchUpdateOptionItemsStatus = async (
  ids: string[],
  isActive: boolean
): Promise<ApiResponse<OptionItem[]>> => {
  try {
    const results: OptionItem[] = []
    const errors: string[] = []
    
    for (const id of ids) {
      const result = await updateOptionItem(id, { is_active: isActive })
      
      if (result.success && result.data) {
        results.push(result.data)
      } else {
        errors.push(result.message || '更新失败')
      }
    }
    
    if (errors.length > 0) {
      return {
        success: false,
        code: 400,
        data: [],
        message: `部分更新失败: ${errors.join(', ')}`
      }
    }

    return {
      success: true,
      code: 200,
      data: results,
      message: `成功${isActive ? '启用' : '禁用'} ${results.length} 条选项数据`
    }
  } catch (error: any) {
    console.error('批量更新选项数据状态失败:', error)
    return {
      success: false,
      code: 500,
      data: [],
      message: error.message || '批量更新选项数据状态失败'
    }
  }
}

// ==================== 工具函数 ====================

/**
 * 检查分类代码是否为客户参数分类
 * @param categoryCode 分类代码
 * @returns 是否为客户参数分类
 */
export const isCustomerOptionCategory = (categoryCode: string): boolean => {
  return Object.values(CustomerOptionCategory).includes(categoryCode as CustomerOptionCategory)
}

/**
 * 获取客户参数分类的API函数映射（使用新的/api/options/端点）
 */
export const CUSTOMER_OPTION_API_MAP = {
  [CustomerOptionCategory.SOURCE]: () => getOptionsByCategory(CustomerOptionCategory.SOURCE),
  [CustomerOptionCategory.LEVEL]: () => getOptionsByCategory(CustomerOptionCategory.LEVEL),
  [CustomerOptionCategory.STATUS]: () => getOptionsByCategory(CustomerOptionCategory.STATUS),
  [CustomerOptionCategory.TAG]: () => getOptionsByCategory(CustomerOptionCategory.TAG),
  [CustomerOptionCategory.DECORATION_TYPE]: () => getOptionsByCategory(CustomerOptionCategory.DECORATION_TYPE),
  [CustomerOptionCategory.HOUSE_STATUS]: () => getOptionsByCategory(CustomerOptionCategory.HOUSE_STATUS),
  [CustomerOptionCategory.PACKAGE_TYPE]: () => getOptionsByCategory(CustomerOptionCategory.PACKAGE_TYPE),
  [CustomerOptionCategory.PAYMENT_METHOD]: () => getOptionsByCategory(CustomerOptionCategory.PAYMENT_METHOD),
  [CustomerOptionCategory.DESIGNER]: () => getOptionsByCategory(CustomerOptionCategory.DESIGNER),
  [CustomerOptionCategory.SALES]: () => getOptionsByCategory(CustomerOptionCategory.SALES)
} as const

/**
 * 根据分类代码获取对应的API函数
 * @param categoryCode 分类代码
 * @returns API函数
 */
export const getCustomerOptionAPI = (
  categoryCode: string
): (() => Promise<ApiResponse<OptionItem[]>>) | null => {
  if (!isCustomerOptionCategory(categoryCode)) {
    return null
  }
  return CUSTOMER_OPTION_API_MAP[categoryCode as CustomerOptionCategory]
}

// ==================== 导出默认对象 ====================

export default {
  // 分类相关
  getOptionCategories,
  createOptionCategory,
  updateOptionCategory,
  deleteOptionCategory,
  
  // 选项数据相关
  getOptionItems,
  getOptionItemsByCategory,
  createOptionItem,
  updateOptionItem,
  deleteOptionItem,
  batchDeleteOptionItems,
  
  // 选项数据获取（使用/api/options/端点）
  getOptionsCategories,
  getOptionsByCategory,
  getAllOptions,
  
  // 客户参数专用
  getCustomerSources,
  getCustomerLevels,
  getCustomerStatuses,
  getCustomerTags,
  
  // 批量操作
  batchCreateOptionItems,
  batchUpdateOptionItemsStatus,
  
  // 工具函数
  isCustomerOptionCategory,
  getCustomerOptionAPI
}