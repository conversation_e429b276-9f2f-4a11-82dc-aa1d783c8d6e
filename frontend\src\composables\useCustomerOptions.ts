/**
 * 客户参数管理组合函数
 * @description 提供客户参数管理的响应式数据和操作方法
 * <AUTHOR> Document
 * @date 2024-01-20
 */

import { ref, reactive, computed, watch, onMounted, onUnmounted } from 'vue'
import { useMessage } from 'naive-ui'
import type {
  OptionItem,
  CreateOptionItemRequest,
  UpdateOptionItemRequest,
  GetOptionItemsParams,
  UseCustomerOptionsParams,
  UseCustomerOptionsReturn,
  PaginationConfig
} from '@/types/customerOptions'
import {
  getOptionItemsByCategory,
  createOptionItem,
  updateOptionItem,
  deleteOptionItem,
  batchDeleteOptionItems,
  batchUpdateOptionItemsStatus
} from '@/api/customerOptions'
import {
  DEFAULT_PAGINATION,
  CUSTOMER_OPTION_CATEGORIES,
  CACHE_KEYS,
  CACHE_EXPIRY
} from '@/constants/customerOptions'
import { debounce } from 'lodash-es'

/**
 * 客户参数管理组合函数
 * @param params 配置参数
 * @returns 响应式数据和操作方法
 */
export function useCustomerOptions(params: UseCustomerOptionsParams): UseCustomerOptionsReturn {
  const { categoryCode, autoLoad = true, pagination: paginationConfig, enableCache = true } = params
  const message = useMessage()

  // ==================== 响应式数据 ====================

  /** 选项数据列表 */
  const items = ref<OptionItem[]>([])
  
  /** 加载状态 */
  const loading = ref(false)
  
  /** 提交状态 */
  const submitting = ref(false)
  
  /** 搜索关键词 */
  const searchKeyword = ref('')
  
  /** 状态筛选 */
  const statusFilter = ref<string>('')
  
  /** 选中的行键 */
  const checkedRowKeys = ref<Array<string | number>>([])
  
  /** 分页配置 */
  const pagination = reactive<PaginationConfig>({
    page: paginationConfig?.page || DEFAULT_PAGINATION.page,
    pageSize: paginationConfig?.pageSize || DEFAULT_PAGINATION.pageSize,
    itemCount: 0,
    showSizePicker: DEFAULT_PAGINATION.showSizePicker,
    pageSizes: DEFAULT_PAGINATION.pageSizes,
    onChange: (page: number) => {
      pagination.page = page
      loadItems()
    },
    onUpdatePageSize: (pageSize: number) => {
      pagination.pageSize = pageSize
      pagination.page = 1
      loadItems()
    }
  })

  // ==================== 计算属性 ====================

  /** 分类信息 */
  const categoryInfo = computed(() => {
    return CUSTOMER_OPTION_CATEGORIES[categoryCode as keyof typeof CUSTOMER_OPTION_CATEGORIES] || {
      code: categoryCode,
      name: '未知分类',
      description: '',
      icon: 'QuestionOutlined',
      color: '#8c8c8c'
    }
  })

  /** 是否有选中项 */
  const hasSelection = computed(() => checkedRowKeys.value.length > 0)

  /** 选中项数量 */
  const selectionCount = computed(() => checkedRowKeys.value.length)

  /** 是否为空数据 */
  const isEmpty = computed(() => items.value.length === 0 && !loading.value)

  /** 缓存键 */
  const cacheKey = computed(() => CACHE_KEYS.ITEMS(categoryCode))

  // ==================== 缓存管理 ====================

  /**
   * 从缓存获取数据
   */
  const getFromCache = (): OptionItem[] | null => {
    if (!enableCache) return null
    
    try {
      const cached = localStorage.getItem(cacheKey.value)
      if (!cached) return null
      
      const { data, timestamp } = JSON.parse(cached)
      const now = Date.now()
      
      // 检查缓存是否过期
      if (now - timestamp > CACHE_EXPIRY.MEDIUM) {
        localStorage.removeItem(cacheKey.value)
        return null
      }
      
      return data
    } catch (error) {
      console.warn('读取缓存失败:', error)
      return null
    }
  }

  /**
   * 保存数据到缓存
   */
  const saveToCache = (data: OptionItem[]) => {
    if (!enableCache) return
    
    try {
      const cacheData = {
        data,
        timestamp: Date.now()
      }
      localStorage.setItem(cacheKey.value, JSON.stringify(cacheData))
    } catch (error) {
      console.warn('保存缓存失败:', error)
    }
  }

  /**
   * 清除缓存
   */
  const clearCache = () => {
    if (!enableCache) return
    localStorage.removeItem(cacheKey.value)
  }

  // ==================== 数据加载 ====================

  /**
   * 加载选项数据
   */
  const loadItems = async (useCache = true): Promise<void> => {
    try {
      loading.value = true
      
      // 尝试从缓存获取数据
      if (useCache) {
        const cachedData = getFromCache()
        if (cachedData) {
          items.value = cachedData
          pagination.itemCount = cachedData.length
          return
        }
      }
      
      // 构建查询参数
      const queryParams: GetOptionItemsParams = {
        page: pagination.page,
        pageSize: pagination.pageSize,
        category_code: categoryCode
      }
      
      // 添加搜索条件
      if (searchKeyword.value.trim()) {
        (queryParams as any).search = searchKeyword.value.trim()
      }
      
      // 添加状态筛选
      if (statusFilter.value && statusFilter.value !== '') {
        (queryParams as any).is_active = statusFilter.value === 'true'
      }
      
      // 调用API
      const response = await getOptionItemsByCategory(categoryCode, queryParams)
      
      // 确保响应数据结构正确
      const responseData = response?.data || response || {}
      const itemsData = responseData.items || []
      const totalCount = responseData.total || itemsData.length
      
      items.value = Array.isArray(itemsData) ? itemsData : []
      pagination.itemCount = totalCount
      
      // 保存到缓存（仅在无搜索和筛选条件时）
      if (!searchKeyword.value && (!statusFilter.value || statusFilter.value === '')) {
        saveToCache(items.value)
      }
    } catch (error: any) {
      console.error('加载选项数据失败:', error)
      message.error(error.message || '加载数据失败')
      items.value = []
      pagination.itemCount = 0
    } finally {
      loading.value = false
    }
  }

  /**
   * 刷新数据
   */
  const refreshData = async (): Promise<void> => {
    clearCache()
    await loadItems(false)
  }

  // ==================== 数据操作 ====================

  /**
   * 创建选项
   */
  const createItem = async (data: CreateOptionItemRequest): Promise<boolean> => {
    try {
      submitting.value = true
      
      const response = await createOptionItem(data)
      
      message.success('创建成功')
      clearCache()
      await loadItems(false)
      return true
    } catch (error: any) {
      console.error('创建选项失败:', error)
      message.error(error.message || '创建失败')
      return false
    } finally {
      submitting.value = false
    }
  }

  /**
   * 更新选项
   */
  const updateItem = async (id: string, data: UpdateOptionItemRequest): Promise<boolean> => {
    try {
      submitting.value = true
      
      const response = await updateOptionItem(id, data)
      
      message.success('更新成功')
      clearCache()
      await loadItems(false)
      return true
    } catch (error: any) {
      console.error('更新选项失败:', error)
      message.error(error.message || '更新失败')
      return false
    } finally {
      submitting.value = false
    }
  }

  /**
   * 删除选项
   */
  const deleteItem = async (id: string): Promise<boolean> => {
    try {
      submitting.value = true
      
      const response = await deleteOptionItem(id)
      
      message.success('删除成功')
      clearCache()
      await loadItems(false)
      
      // 清除选中状态
      checkedRowKeys.value = checkedRowKeys.value.filter(key => key !== id)
      
      return true
    } catch (error: any) {
      console.error('删除选项失败:', error)
      message.error(error.message || '删除失败')
      return false
    } finally {
      submitting.value = false
    }
  }

  /**
   * 批量删除选项
   */
  const batchDelete = async (ids: string[]): Promise<boolean> => {
    try {
      submitting.value = true
      
      const response = await batchDeleteOptionItems(ids)
      
      message.success(`成功删除 ${ids.length} 条数据`)
      clearCache()
      await loadItems(false)
      
      // 清除选中状态
      checkedRowKeys.value = []
      
      return true
    } catch (error: any) {
      console.error('批量删除选项失败:', error)
      message.error(error.message || '批量删除失败')
      return false
    } finally {
      submitting.value = false
    }
  }

  /**
   * 批量更新状态
   */
  const batchUpdateStatus = async (ids: string[], isActive: boolean): Promise<boolean> => {
    try {
      submitting.value = true
      
      const response = await batchUpdateOptionItemsStatus(ids, isActive)
      
      message.success(`成功${isActive ? '启用' : '禁用'} ${ids.length} 条数据`)
      clearCache()
      await loadItems(false)
      return true
    } catch (error: any) {
      console.error('批量更新状态失败:', error)
      message.error(error.message || '批量更新状态失败')
      return false
    } finally {
      submitting.value = false
    }
  }

  // ==================== 搜索和筛选 ====================

  /**
   * 防抖搜索函数
   */
  const debouncedSearch = debounce(() => {
    pagination.page = 1
    loadItems(false)
  }, 300)

  /**
   * 处理搜索
   */
  const handleSearch = (keyword: string) => {
    searchKeyword.value = keyword
    debouncedSearch()
  }

  /**
   * 处理筛选
   */
  const handleFilter = (status: string) => {
    statusFilter.value = status
    pagination.page = 1
    loadItems(false)
  }

  /**
   * 重置筛选
   */
  const resetFilters = () => {
    searchKeyword.value = ''
    statusFilter.value = ''
    pagination.page = 1
    loadItems(false)
  }

  // ==================== 生命周期 ====================

  // 监听搜索关键词变化
  watch(
    () => searchKeyword.value,
    () => {
      debouncedSearch()
    }
  )

  // 组件挂载时自动加载数据
  onMounted(() => {
    if (autoLoad) {
      loadItems()
    }
  })

  // 组件卸载时清理定时器
  onUnmounted(() => {
    debouncedSearch.cancel()
  })

  // ==================== 返回值 ====================

  return {
    // 响应式数据
    items,
    loading,
    submitting,
    pagination,
    searchKeyword,
    statusFilter,
    checkedRowKeys,
    
    // 计算属性
    categoryInfo,
    hasSelection,
    selectionCount,
    isEmpty,
    
    // 操作方法
    loadItems,
    createItem,
    updateItem,
    deleteItem,
    batchDelete,
    batchUpdateStatus,
    
    // 搜索和筛选
    handleSearch,
    handleFilter,
    resetFilters,
    refreshData,
    
    // 缓存管理
    clearCache
  }
}

// ==================== 工具函数 ====================

/**
 * 创建客户参数管理实例的工厂函数
 * @param categoryCode 分类代码
 * @param options 配置选项
 * @returns 组合函数实例
 */
export function createCustomerOptionsManager(
  categoryCode: string,
  options?: Partial<UseCustomerOptionsParams>
) {
  return useCustomerOptions({
    categoryCode,
    ...options
  })
}

/**
 * 批量创建多个客户参数管理实例
 * @param categoryCodes 分类代码数组
 * @param options 配置选项
 * @returns 组合函数实例映射
 */
export function createMultipleCustomerOptionsManagers(
  categoryCodes: string[],
  options?: Partial<UseCustomerOptionsParams>
) {
  const managers: Record<string, UseCustomerOptionsReturn> = {}
  
  categoryCodes.forEach(categoryCode => {
    managers[categoryCode] = useCustomerOptions({
      categoryCode,
      ...options
    })
  })
  
  return managers
}

// ==================== 导出 ====================

export default useCustomerOptions